"use client"

import React, { useState, useEffect, useMemo } from "react"
import { But<PERSON> } from "@ui/components/ui/button"
import { ChevronDownIcon, ChevronUpIcon, GearIcon, LinkIcon, ArrowLeftIcon, BriefcaseIcon, CrownIcon, StarIcon, UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, GlobeIcon, CalendarIcon, BuildingIcon, UsersIcon, DollarSignIcon, RocketIcon, ChartLineIcon } from "@ui/components/icons/FontAwesomeRegular"
import { ExternalLinkIcon } from "@radix-ui/react-icons"
import { HandThumbUpIcon, HandThumbDownIcon, CheckCircleIcon, XCircleIcon, ClockIcon, AcademicCapIcon, BriefcaseIcon as BriefcaseSolidIcon } from "@heroicons/react/24/outline"
import { cn } from "@ui/lib/utils"
import { useScreenSize } from "@ui/providers/screenSize"
import { getLead, sendEmailToLead } from "@ui/api/leads"
import { Lead, LeadMeta } from "@ui/typings/lead"
import { useAlert } from "@ui/providers/alert"
import { VoteLeadModal } from "../VoteLeadModal"
import { UnlockEmailModal } from "../../common/unlockEmail"
import { UnlockPhoneModal } from "../../common/unlockPhone"
import { getMetaProperty,  getMetaArrayProperty, getApolloProperty, formatDate, getTimeAgo } from "../../../../../utils/lead"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@ui/components/ui/dialog"
import { Input } from "@ui/components/ui/input"
import { Textarea } from "@ui/components/ui/textarea"
import { Loader } from "@ui/components/custom-ui/loader"


interface DetailsProps {
  leadId?: string
  token?: string
  workspaceId?: string
  onBack?: () => void
  handleSendEmail?: (leadId: string, leadData?: any) => void
}

export const Details = ({ 
  leadId,
  token,
  workspaceId,
  onBack,
  handleSendEmail: parentHandleSendEmail
}: DetailsProps) => {
  const { isMobile } = useScreenSize()
  const { toast } = useAlert()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [leadData, setLeadData] = useState<Lead | null>(null)
  const [voteModalOpen, setVoteModalOpen] = useState(false)
  
  // Unlock modal states
  const [emailModalOpen, setEmailModalOpen] = useState(false)
  const [phoneModalOpen, setPhoneModalOpen] = useState(false)
  
  // Send email modal states
  const [sendEmailDialogOpen, setSendEmailDialogOpen] = useState(false)
  const [emailSubject, setEmailSubject] = useState('')
  const [emailBody, setEmailBody] = useState('')
  const [sendingEmail, setSendingEmail] = useState(false)
  const [selectedLeadForEmail, setSelectedLeadForEmail] = useState<any>(null)
  

  
  // Fetch lead data function
  const fetchLeadData = async () => {
    if (!leadId || !token || !workspaceId) return
    
    setLoading(true)
    setError(null)
    
    try {
      const response = await getLead(token, workspaceId, leadId)
      
      if (response.isSuccess && response.data?.data) {
        setLeadData(response.data.data)
      } else {
        setError(response.error || "Failed to load lead data")
        toast.error("Error", {
          description: response.error || "Failed to load lead data"
        })
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred"
      setError(errorMessage)
      toast.error("Error", {
        description: errorMessage
      })
    } finally {
      setLoading(false)
    }
  }

  // Fetch lead data on mount
  useEffect(() => {
    fetchLeadData()
  }, [leadId, token, workspaceId, toast])
  
  // Handle unlock success - refresh lead data
  const handleUnlockSuccess = (unlockedLead: any) => {
    if (unlockedLead) {
      setLeadData(unlockedLead)
    }
  }
  
  // Unlock handlers
  const handleUnlockEmail = () => {
    setEmailModalOpen(true)
  }
  
  const handleUnlockPhone = () => {
    setPhoneModalOpen(true)
  }
  
  const handleSendEmail = () => {
    
    if (!leadData) {
      return
    }
    
    const email = leadData.normalizedData?.email || (leadData.apolloData as any)?.email || leadData.email
    const isEmailVisible = leadData.normalizedData?.isEmailVisible || (leadData.apolloData as any)?.normalizedData?.isEmailVisible
    
    if (!email || !isEmailVisible) {
      toast.error("You have to unlock the email first before sending an email.")
      return
    }
    
    setSelectedLeadForEmail({ ...leadData, email })
    setEmailSubject('')
    setEmailBody('')
    setSendEmailDialogOpen(true)
  }
  
  const handleConfirmSendEmail = async () => {
    if (!selectedLeadForEmail || !emailSubject.trim() || !emailBody.trim()) {
      toast.error("Please fill in both subject and body")
      return
    }

    setSendingEmail(true)
    try {
      const response = await sendEmailToLead(token || '', workspaceId || '', selectedLeadForEmail.id, {
        subject: emailSubject.trim(),
        body: emailBody.trim()
      })
      
      if (response.isSuccess) {
        toast.success("Email sent successfully!")
        setSendEmailDialogOpen(false)
        setEmailSubject('')
        setEmailBody('')
        setSelectedLeadForEmail(null)
      } else {
        toast.error(response.error || "Failed to send email")
      }
    } catch (error) {
      console.error("Failed to send email:", error)
      toast.error("Failed to send email")
    } finally {
      setSendingEmail(false)
    }
  }
  
  const handleVoteClick = () => {
    if (!leadId) {
      toast.error("Error", {
        description: "No lead selected for voting"
      })
      return
    }
    
    setVoteModalOpen(true)
  }
  
  const handleVoteSuccess = () => {
    toast.success("Success", {
      description: "Your vote has been recorded"
    })
    // Refresh lead data to show updated vote history
    fetchLeadData()
  }
  
  // Loading state
  if (loading) {
  return (
      <div className="w-full h-full bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-slate-600 font-medium">Loading lead details...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="w-full h-full bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <XCircleIcon className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold text-red-900 mb-2">Error loading lead details</h3>
          <p className="text-red-700 mb-6">{error}</p>
          <Button variant="outline" onClick={onBack} className="border-red-300 text-red-700 hover:bg-red-50">
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  // Loading state protection - don't render if data isn't ready
  if (!leadData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading lead data...</p>
        </div>
      </div>
    );
  }

  // Enhanced type guard - handle both explicit "person" type and data structure detection
  const hasPersonData = !!leadData?.apolloData?.name && 
                       !!(leadData?.apolloData as any)?.title && 
                       !!(leadData?.apolloData as any)?.employment_history;
  
  const isPersonLead = leadData?.type === 'person' || hasPersonData;
  const apolloPersonData = isPersonLead ? (leadData.apolloData as any) : null;
  
  
  return (
    <div className="w-full h-full bg-white overflow-auto">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-md border-b border-neutral-300">
        <div className="flex items-center justify-between px-6 py-1.5">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack} className="p-1.5 hover:bg-slate-100">
              <ArrowLeftIcon className="w-4 h-4" />
            </Button>
            <div>
              <h1 className="text-base font-semibold text-slate-900 leading-tight">
                {leadData?.normalizedData?.name || leadData?.name || "Lead Details"}
              </h1>
              <p className="text-xs text-slate-500 leading-tight">
                {String(leadData?.normalizedData?.jobTitle || apolloPersonData?.title || "Loading...")}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button size="sm" className="bg-black hover:bg-gray-800 text-white px-3 py-1.5 text-xs" onClick={handleSendEmail}>
              <EnvelopeIcon className="w-3 h-3 mr-1.5" />
              Send Email
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4">
        <div className="w-full">
          {/* Hero Section */}
          <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Profile Photo & Basic Info */}
              <div className="flex flex-col lg:flex-row items-start gap-4">
                {/* Profile Photo */}
                <div className="relative">
                  {apolloPersonData?.photo_url ? (
                    <img 
                      src={apolloPersonData.photo_url} 
                      alt={leadData.normalizedData?.name || "Profile"} 
                      className="w-20 h-20 rounded-xl object-cover border-2 border-slate-100"
                    />
                  ) : (
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center border-2 border-slate-100">
                      <span className="text-white font-bold text-xl">
                        {leadData?.normalizedData?.name?.charAt(0)?.toUpperCase() || "?"}
                      </span>
                    </div>
                  )}
                  
                  {/* Online Status Indicator */}
                  <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                  </div>
                </div>

                {/* Basic Information */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 text-sm text-slate-600 mb-3">
                    <BuildingIcon className="w-4 h-4" />
                    <span>{leadData?.normalizedData?.company || "Company not specified"}</span>
                  </div>

                  {/* Location & Timezone */}
                  <div className="flex items-center gap-4 text-xs text-slate-500">
                    {apolloPersonData?.location?.city && (
                      <div className="flex items-center gap-1">
                        <MapPinIcon className="w-3 h-3" />
                        <span>
                          {apolloPersonData.location.city}
                          {apolloPersonData.location.state && `, ${apolloPersonData.location.state}`}
                          {apolloPersonData.location.country && `, ${apolloPersonData.location.country}`}
                  </span>
                      </div>
                )}
                    
                    {apolloPersonData?.time_zone && (
                      <div className="flex items-center gap-1">
                        <ClockIcon className="w-3 h-3" />
                        <span>{String(apolloPersonData.time_zone)}</span>
              </div>
                    )}

                    {apolloPersonData?.formatted_address && (
                      <div className="flex items-center gap-1">
                        <MapPinIcon className="w-3 h-3" />
                        <span>{String(apolloPersonData.formatted_address)}</span>
                      </div>
                    )}
                  </div>
        </div>
      </div>

              {/* Right Side - Stats & Actions */}
              <div className="lg:ml-auto flex flex-col gap-4">
                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-4">
                                      <div className="bg-white rounded-xl p-4 text-center border border-neutral-300">
                      <div className="text-2xl font-bold text-slate-900">
                        {Array.isArray(apolloPersonData?.employment_history) ? apolloPersonData.employment_history.length : 0}
                      </div>
                      <div className="text-xs text-slate-600 font-medium">Positions</div>
                    </div>
                    
                    <div className="bg-white rounded-xl p-4 text-center border border-neutral-300">
                      <div className="text-2xl font-bold text-slate-900">
                        {Array.isArray(apolloPersonData?.departments) ? apolloPersonData.departments.length : 0}
                      </div>
                      <div className="text-xs text-slate-600 font-medium">Departments</div>
                    </div>
                </div>


            </div>
                    </div>

            {/* Professional Summary */}
            {apolloPersonData?.headline && (
              <div className="mt-6 pt-6 border-t border-neutral-300">
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Professional Summary</h3>
                <p className="text-sm text-slate-600 leading-relaxed">
                  {apolloPersonData.headline}
                </p>
                  </div>
                      )}
                    </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content - Left Column */}
            <div className="lg:col-span-2 space-y-6">
              {/* Employment Timeline */}
              <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-slate-900">Employment History</h3>
                  <div className="text-xs text-slate-500 font-medium">
                    {Array.isArray(apolloPersonData?.employment_history) ? apolloPersonData.employment_history.length : 0} positions
                  </div>
                  </div>
                
                {Array.isArray(apolloPersonData?.employment_history) && apolloPersonData.employment_history.length > 0 ? (
                  <div className="space-y-4">
                    {apolloPersonData.employment_history.map((job: any, index: number) => (
                      <div key={index} className="flex items-start gap-4 p-4 bg-white rounded-xl border border-neutral-300">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-semibold text-slate-900">
                              {String(job.title || 'Unknown Title')}
                            </h4>
                            {job.current && (
                              <span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full font-medium">
                                Current
                    </span>
            )}
          </div>
                          <p className="text-sm text-slate-600 mb-2">
                            {String(job.organization_name || 'Unknown Company')}
                          </p>
                          
                          {/* Additional Job Details */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
                            <div className="flex items-center gap-4 text-xs text-slate-500">
                              <span>{formatDate(job.start_date)}</span>
                              <span>•</span>
                              <span>{job.current ? 'Present' : formatDate(job.end_date)}</span>
              </div>
                            
                            {/* Job Metadata */}
                            <div className="flex flex-wrap gap-2">
                              {job.degree && (
                                <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                                  Degree: {String(job.degree)}
                  </span>
                              )}
                              {job.major && (
                                <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                                  Major: {String(job.major)}
                  </span>
                              )}
                              {job.grade_level && (
                                <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                                  Grade: {String(job.grade_level)}
                  </span>
                              )}
                              {job.kind && (
                                <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                                  Type: {String(job.kind)}
                  </span>
                              )}
                </div>
                          </div>
                          
                          {/* Job Description */}
                          {job.description && (
                            <p className="text-xs text-slate-600 mt-2 p-2 bg-white rounded border">
                              {String(job.description)}
                            </p>
                          )}
                          
                          {/* Job Emails */}
                          {job.emails && Array.isArray(job.emails) && job.emails.length > 0 && (
                            <div className="mt-2">
                              <p className="text-xs text-slate-500 mb-1">Associated Emails:</p>
                              <div className="flex flex-wrap gap-1">
                                {job.emails.map((email: string, emailIndex: number) => (
                                  <span key={emailIndex} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                                    {String(email)}
                  </span>
                                ))}
                </div>
                </div>
                          )}
                          
                          {/* Raw Address */}
                          {job.raw_address && (
                            <div className="mt-2 text-xs text-slate-500">
                              <span className="font-medium">Address:</span> {String(job.raw_address)}
              </div>
            )}
          </div>
                </div>
                    ))}
              </div>
                ) : (
                  <div className="text-center py-8">
                    <BriefcaseIcon className="w-12 h-12 text-slate-300 mx-auto mb-3" />
                    <p className="text-sm text-slate-500">No employment history available</p>
                </div>
              )}
            </div>

              {/* Company Information */}
              {apolloPersonData?.organization && typeof apolloPersonData.organization === 'object' && (
                <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-6">Company Information</h3>
                  
                  {/* Company Header with Logo */}
                  <div className="flex items-center gap-4 mb-6 p-4 bg-white rounded-xl border border-neutral-300">
                    {(apolloPersonData.organization ).logo_url && (
                      <img 
                        src={(apolloPersonData.organization ).logo_url} 
                        alt={`${(apolloPersonData.organization ).name} logo`}
                        className="w-16 h-16 rounded-lg object-cover border border-neutral-300"
                      />
                    )}
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-slate-900">
                        {(apolloPersonData.organization ).name || 'N/A'}
                      </h4>
                      {(apolloPersonData.organization ).primary_domain && (
                        <p className="text-sm text-slate-600">
                          {(apolloPersonData.organization ).primary_domain}
                        </p>
                      )}
                    </div>
        </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Company Details</h4>
                        <div className="space-y-2 text-sm text-slate-600">
              <div className="flex items-center gap-2">
                            <BuildingIcon className="w-4 h-4 text-slate-400" />
                            <span>{(apolloPersonData.organization ).name || 'N/A'}</span>
                          </div>
                          {(apolloPersonData.organization ).website_url && (
                            <div className="flex items-center gap-2">
                              <GlobeIcon className="w-4 h-4 text-slate-400" />
                              <a 
                                href={(apolloPersonData.organization ).website_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-700 underline"
                              >
                                Visit Website
                              </a>
              </div>
                          )}
                          {(apolloPersonData.organization ).phone && (
                            <div className="flex items-center gap-2">
                              <PhoneIcon className="w-4 h-4 text-slate-400" />
                              <span>{(apolloPersonData.organization ).phone}</span>
            </div>
                          )}
                          {(apolloPersonData.organization ).primary_phone && (
                            <div className="flex items-center gap-2">
                              <PhoneIcon className="w-4 h-4 text-slate-400" />
                              <span className="text-sm text-slate-600">
                                {(apolloPersonData.organization ).primary_phone.number}
                                <span className="text-xs text-slate-400 ml-2">
                                   {(apolloPersonData.organization ).primary_phone.source}
                                </span>
                              </span>
                            </div>
                          )}
                        </div>
                </div>
                
                      {/* Additional Company URLs */}
                      {(apolloPersonData.organization ).blog_url || (apolloPersonData.organization ).angellist_url || (apolloPersonData.organization ).crunchbase_url ? (
                    <div>
                          <h4 className="text-sm font-semibold text-slate-700 mb-2">Additional Links</h4>
                          <div className="space-y-2 text-sm text-slate-600">
                            {(apolloPersonData.organization ).blog_url && (
                        <div className="flex items-center gap-2">
                                <LinkIcon className="w-4 h-4 text-slate-400" />
                                <a 
                                  href={(apolloPersonData.organization ).blog_url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-700 underline"
                                >
                                  Blog
                            </a>
                        </div>
                        )}
                            {(apolloPersonData.organization ).angellist_url && (
                          <div className="flex items-center gap-2">
                                <LinkIcon className="w-4 h-4 text-slate-400" />
                                <a 
                                  href={(apolloPersonData.organization ).angellist_url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-700 underline"
                                >
                                  AngelList
                            </a>
                          </div>
                        )}
                              {(apolloPersonData.organization).crunchbase_url && (
                          <div className="flex items-center gap-2">
                                <LinkIcon className="w-4 h-4 text-slate-400" />
                                <a 
                                  href={(apolloPersonData.organization ).crunchbase_url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-700 underline"
                                >
                                  Crunchbase
                            </a>
                        </div>
                        )}
                      </div>
                    </div>
                      ) : null}
                    </div>
                    
                    <div className="space-y-4">
                    <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Growth Metrics</h4>
                        <div className="space-y-2 text-sm text-slate-600">
                          <div className="flex justify-between">
                            <span>6 Month Growth:</span>
                            <span className="font-medium">
                              {(apolloPersonData.organization ).organization_headcount_six_month_growth || 0}%
                      </span>
                    </div>
                          <div className="flex justify-between">
                            <span>12 Month Growth:</span>
                            <span className="font-medium">
                              {(apolloPersonData.organization ).organization_headcount_twelve_month_growth || 0}%
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>24 Month Growth:</span>
                            <span className="font-medium">
                                                             {(apolloPersonData.organization).organization_headcount_twenty_four_month_growth || 0}%
                            </span>
                          </div>
                        </div>
                      </div>

                    <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Company Details</h4>
                        <div className="space-y-2 text-sm text-slate-600">
                          {(apolloPersonData.organization).founded_year && (
                            <div className="flex justify-between">
                              <span>Founded:</span>
                              <span className="font-medium">
                                {(apolloPersonData.organization).founded_year}
                      </span>
                    </div>
                          )}
                          {(apolloPersonData.organization).primary_domain && (
                            <div className="flex justify-between">
                              <span>Domain:</span>
                              <span className="font-medium">
                                {(apolloPersonData.organization).primary_domain}
                              </span>
                      </div>
                )}
                          {(apolloPersonData.organization).linkedin_uid && (
                            <div className="flex justify-between">
                              <span>LinkedIn ID:</span>
                              <span className="font-medium">
                                {(apolloPersonData.organization).linkedin_uid}
                              </span>
                    </div>
                          )}
                          {(apolloPersonData.organization).alexa_ranking && (
                            <div className="flex justify-between">
                              <span>Alexa Ranking:</span>
                              <span className="font-medium">
                                {((apolloPersonData.organization).alexa_ranking).toLocaleString()}
                              </span>
                            </div>
                          )}
                          {(apolloPersonData.organization).estimated_num_employees && (
                            <div className="flex justify-between">
                              <span>Estimated Employees:</span>
                              <span className="font-medium">
                                {((apolloPersonData.organization).estimated_num_employees).toLocaleString()}
                              </span>
                            </div>
                          )}
                          {(apolloPersonData.organization).publicly_traded_symbol && (
                            <div className="flex justify-between">
                              <span>Public Trading:</span>
                              <span className="font-medium">
                                {(apolloPersonData.organization).publicly_traded_symbol} 
                                {((apolloPersonData.organization).publicly_traded_exchange || 'Unknown Exchange')}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Company Languages */}
                      {(apolloPersonData.organization).languages && Array.isArray((apolloPersonData.organization).languages) && (apolloPersonData.organization).languages.length > 0 && (
                    <div>
                          <h4 className="text-sm font-semibold text-slate-700 mb-2">Languages</h4>
                          <div className="flex flex-wrap gap-2">
                            {(apolloPersonData.organization).languages.map((lang: string, index: number) => (
                              <span key={index} className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full font-medium">
                                {String(lang)}
                      </span>
                            ))}
                    </div>
                        </div>
                      )}

                                            {/* Company Stage & Description */}
                      {((apolloPersonData.organization ).stage || (apolloPersonData.organization ).short_description) && (
                    <div>
                          <h4 className="text-sm font-semibold text-slate-700 mb-2">Company Details</h4>
                          <div className="space-y-2">
                            {(apolloPersonData.organization ).stage && (
                              <div className="flex justify-between">
                                <span>Stage:</span>
                                <span className="font-medium capitalize">
                                  {String((apolloPersonData.organization as any).stage)}
                      </span>
                              </div>
                            )}
                            {(apolloPersonData.organization ).short_description && (
                              <div>
                                <span className="text-xs text-slate-500">Description:</span>
                                <p className="text-xs text-slate-600 mt-1 p-2 bg-slate-50 rounded">
                                  {String((apolloPersonData.organization as any).short_description)}
                                </p>
                              </div>
                            )}
                    </div>
                  </div>
                )}
                
                      {/* Company Industry & Keywords */}
                      {((apolloPersonData.organization as any).industry || (apolloPersonData.organization as any).keywords) && (
                    <div>
                          <h4 className="text-sm font-semibold text-slate-700 mb-2">Industry & Keywords</h4>
                          <div className="space-y-2">
                            {(apolloPersonData.organization as any).industry && (
                              <div className="flex justify-between">
                                <span>Industry:</span>
                                <span className="font-medium capitalize">
                                  {String((apolloPersonData.organization as any).industry)}
                      </span>
                    </div>
                            )}
                            {(apolloPersonData.organization as any).keywords && Array.isArray((apolloPersonData.organization as any).keywords) && (apolloPersonData.organization as any).keywords.length > 0 && (
                    <div>
                                <span className="text-xs text-slate-500">Keywords:</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {(apolloPersonData.organization as any).keywords.map((keyword: string, index: number) => (
                                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                      {String(keyword)}
                      </span>
                                  ))}
                    </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Skills & Functions */}
              {(apolloPersonData?.departments || apolloPersonData?.functions || apolloPersonData?.subdepartments) && (
                <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-6">Professional Profile</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {apolloPersonData?.departments && Array.isArray(apolloPersonData.departments) && (
                    <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-3">Departments</h4>
                        <div className="flex flex-wrap gap-2">
                          {apolloPersonData.departments.map((dept: any, index: number) => (
                            <span key={index} className="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full font-medium">
                              {String(dept).replace(/_/g, ' ')}
                      </span>
                          ))}
                    </div>
                  </div>
                )}

                    {apolloPersonData?.functions && Array.isArray(apolloPersonData.functions) && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-3">Functions</h4>
                        <div className="flex flex-wrap gap-2">
                          {apolloPersonData.functions.map((func: any, index: number) => (
                            <span key={index} className="px-3 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium">
                              {String(func)}
                            </span>
                          ))}
                            </div>
                          </div>
                    )}

                    {apolloPersonData?.subdepartments && Array.isArray(apolloPersonData.subdepartments) && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-3">Sub-departments</h4>
                        <div className="flex flex-wrap gap-2">
                          {apolloPersonData.subdepartments.map((subdept: any, index: number) => (
                            <span key={index} className="px-3 py-1 bg-purple-100 text-purple-700 text-xs rounded-full font-medium">
                              {String(subdept).replace(/_/g, ' ')}
                            </span>
                          ))}
                        </div>
                  </div>
                )}
                  </div>

                  {apolloPersonData?.seniority && (
                    <div className="mt-6 pt-6 border-t border-neutral-300">
                      <h4 className="text-sm font-semibold text-slate-700 mb-3">Seniority Level</h4>
                      <span className="px-4 py-2 bg-slate-100 text-slate-700 text-sm rounded-lg font-medium capitalize">
                        {String(apolloPersonData.seniority)}
                      </span>
                    </div>
                  )}
                  </div>
                )}
                
              {/* Additional Location Details */}
              {(apolloPersonData?.street_address || apolloPersonData?.postal_code) && (
                <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Detailed Location</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {apolloPersonData.street_address && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Street Address</h4>
                        <p className="text-sm text-slate-600">{String(apolloPersonData.street_address)}</p>
                  </div>
                )}
                    {apolloPersonData.postal_code && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Postal Code</h4>
                        <p className="text-sm text-slate-600">{String(apolloPersonData.postal_code)}</p>
              </div>
            )}
          </div>
                </div>
              )}

              {/* Company Technologies */}
              {(apolloPersonData?.organization as any)?.current_technologies && Array.isArray((apolloPersonData.organization as any).current_technologies) && (apolloPersonData.organization as any).current_technologies.length > 0 && (
                <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Company Technologies</h3>
                  <div className="flex flex-wrap gap-2">
                    {(apolloPersonData.organization as any).current_technologies.map((tech: string, index: number) => (
                      <span key={index} className="px-3 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full font-medium">
                        {String(tech)}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            {/* Sidebar - Right Column */}
            <div className="space-y-6">
              {/* Contact Information */}
              <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Contact Information</h3>
                <div className="space-y-4">
                  {/* Email */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <EnvelopeIcon className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-700">Email</span>
                    </div>
                    {leadData?.isUnlocked && leadData.normalizedData.isEmailVisible && leadData.normalizedData.email ? (
              <div className="flex items-center gap-2">
                        <span className="text-sm text-slate-600">{leadData.normalizedData.email}</span>
                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                      </div>
                    ) : (
                      <Button variant="outline" size="sm" className="text-xs rounded-full w-full" onClick={handleUnlockEmail}>
                        <EnvelopeIcon className="w-3 h-3 mr-2" />
                        Unlock Email
                </Button>
                    )}
              </div>

                  {/* Phone */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <PhoneIcon className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-700">Phone</span>
            </div>
                    {leadData?.isUnlocked && leadData.normalizedData.isPhoneVisible && leadData.normalizedData.phone ? (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-slate-600">{leadData.normalizedData.phone}</span>
                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                      </div>
                    ) : (
                      <Button variant="outline" size="sm" className="text-xs rounded-full w-full" onClick={handleUnlockPhone}>
                        <PhoneIcon className="w-3 h-3 mr-2" />
                        Unlock Phone
                      </Button>
                        )}
                      </div>

                  {/* Social Media */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <LinkIcon className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-700">Social Media</span>
                    </div>
                    <div className="space-y-2">
                      {leadData?.apolloData?.linkedin_url && (
                        <a 
                          href={String(leadData.apolloData.linkedin_url)} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700"
                        >
                          <span>LinkedIn</span>
                          <ExternalLinkIcon className="w-3 h-3" />
                        </a>
                      )}
                      {leadData?.apolloData?.twitter_url && (
                        <a 
                          href={String(leadData.apolloData.twitter_url)} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700"
                        >
                          <span>Twitter</span>
                          <ExternalLinkIcon className="w-3 h-3" />
                        </a>
                      )}
                      {apolloPersonData?.github_url && (
                        <a
                          href={String(apolloPersonData.github_url)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700"
                        >
                          <span>GitHub</span>
                          <ExternalLinkIcon className="w-3 h-3" />
                        </a>
                      )}
                      {leadData?.apolloData?.facebook_url && (
                        <a 
                          href={String(leadData.apolloData.facebook_url)} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700"
                        >
                          <span>Facebook</span>
                          <ExternalLinkIcon className="w-3 h-3" />
                        </a>
                      )}
                      {(!leadData?.apolloData?.linkedin_url && !leadData?.apolloData?.twitter_url && !apolloPersonData?.github_url && !leadData?.apolloData?.facebook_url) && (
                        <span className="text-sm text-slate-500">No social media available</span>
                      )}
                    </div>
                  </div>
                </div>
                </div>

              {/* Lead Status */}
              <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Lead Status</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Status:</span>
                    <span className="text-sm font-medium text-slate-900">
                      {leadData?.isUnlocked ? 'Unlocked' : 'Locked'}
                    </span>
                        </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Source:</span>
                    <span className="text-sm font-medium text-slate-900 capitalize">
                      {leadData?.source || 'Unknown'}
                    </span>
                      </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Created:</span>
                    <span className="text-sm font-medium text-slate-900">
                      {leadData?.createdAt ? getTimeAgo(leadData.createdAt) : 'Unknown'}
                    </span>
                    </div>
                  {leadData?.lastEnrichedAt && (
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-600">Last Updated:</span>
                      <span className="text-sm font-medium text-slate-900">
                        {getTimeAgo(leadData.lastEnrichedAt)}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Apollo Data Insights */}
              {leadData?.apolloData && (
                <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Apollo Insights</h3>
                  <div className="space-y-3">
                    {leadData.apolloData.intent_strength && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Intent Strength:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {String(leadData.apolloData.intent_strength)}
                        </span>
              </div>
            )}
                    {apolloPersonData?.extrapolated_email_confidence && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Email Confidence:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {String(apolloPersonData.extrapolated_email_confidence)}%
                        </span>
          </div>
                    )}
                    {apolloPersonData?.email_status && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Email Status:</span>
                        <span className={cn(
                          "text-sm font-medium px-2 py-1 rounded-full text-xs",
                          apolloPersonData.email_status === 'verified'
                            ? "bg-green-100 text-green-700"
                            : "bg-yellow-100 text-yellow-700"
                        )}>
                          {String(apolloPersonData.email_status)}
                        </span>
        </div>
                    )}
                    {leadData.apolloData.show_intent && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Shows Intent:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {leadData.apolloData.show_intent ? 'Yes' : 'No'}
                        </span>
      </div>
                    )}
                    {apolloPersonData?.email_domain_catchall && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Catch-all Domain:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {apolloPersonData.email_domain_catchall ? 'Yes' : 'No'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Vote Lead */}
              <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Rate This Lead</h3>
                <p className="text-sm text-slate-600 mb-4">
                  Help improve lead quality by rating this contact
                </p>

                <Button 
                  onClick={() => handleVoteClick()}
                  className="w-full bg-black hover:bg-gray-800 text-white"
                >
                  <HandThumbUpIcon className="w-4 h-4 mr-2" />
                  Rate with Feedback
                </Button>
              </div>

            </div>
          </div>
        </div>
      </div>

      {/* Vote Modal */}
      {leadId && (
        <VoteLeadModal
          open={voteModalOpen}
          onOpenChange={setVoteModalOpen}
          leadId={leadId}
          onVoteSuccess={handleVoteSuccess}
        />
      )}

      {/* Unlock Email Modal */}
      {leadId && (
        <UnlockEmailModal
          open={emailModalOpen}
          onOpenChange={setEmailModalOpen}
          leadId={leadId}
          onUnlockSuccess={handleUnlockSuccess}
        />
      )}

      {/* Unlock Phone Modal */}
      {leadId && (
        <UnlockPhoneModal
          open={phoneModalOpen}
          onOpenChange={setPhoneModalOpen}
          leadId={leadId}
          onUnlockSuccess={handleUnlockSuccess}
        />
      )}
      
      <Dialog open={sendEmailDialogOpen} onOpenChange={setSendEmailDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Send Email</DialogTitle>
            <DialogDescription>
              Send an email to {selectedLeadForEmail?.normalizedData?.name || selectedLeadForEmail?.apolloData?.name || 'this lead'}
              {selectedLeadForEmail?.email ? ` (${selectedLeadForEmail.email})` : ''}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Subject:</label>
                <Input
                  value={emailSubject}
                  onChange={(e) => setEmailSubject(e.target.value)}
                  placeholder="Enter email subject"
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Message:</label>
                <Textarea
                  value={emailBody}
                  onChange={(e) => setEmailBody(e.target.value)}
                  placeholder="Enter your message"
                  className="mt-1 min-h-[100px]"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setSendEmailDialogOpen(false)}
              disabled={sendingEmail}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmSendEmail}
              disabled={!emailSubject.trim() || !emailBody.trim() || sendingEmail}
              className="gap-2"
            >
              {sendingEmail ? (
                <>
                  <Loader theme="dark" className="size-4" />
                  Sending...
                </>
              ) : (
                <>
                  <EnvelopeIcon className="size-4" />
                  Send Email
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
