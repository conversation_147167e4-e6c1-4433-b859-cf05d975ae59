"use client"

import React, { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { ScrollArea } from "@ui/components/ui/scroll-area"
import { PlusIcon, UserGroupIcon, EnvelopeIcon, PhoneIcon, EllipsisVerticalIcon, TrashIcon, ChevronRightIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon, MagnifyingGlassIcon } from "@ui/components/icons/FontAwesomeRegular"
import { MagnifyingGlassCircleIcon } from "@heroicons/react/24/outline"
import { Loader } from "@ui/components/custom-ui/loader"
import { CompanyFilter } from "@ui/components/workspace/main/lead-generation/filters/company"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { searchCompanyLeads, saveSearch } from "@ui/api/leads"
import { SearchFilters, SearchLeadsRequest, Lead as APILead, NormalizedLeadData } from "@ui/typings/lead"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@ui/components/ui/dialog"
import { CompanyTableHeader } from "./index"


interface Lead {
    id: string
    name: string
    company: string
    email: string
    phone: string
    links: string
    industry: string
    location: string
}

interface FindLeadsProps {
    onLeadCreated?: (lead: any) => void
    sidebarState?: {
        isOpen: boolean
        setIsOpen: (open: boolean) => void
    }

    ActionButton?: any
    ViewLinksModal?: any
    LeadActionsDropdown?: any
    leadActions?: any
    leadManagement?: any
}

// All components are now imported from index

const FindLeads = ({ onLeadCreated, sidebarState, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement }: FindLeadsProps) => {
    const router = useRouter()
    const params = useParams()
    const { workspace } = useWorkspace()
    const { token } = useAuth()
    const { toast } = useAlert()
    

    const [leads, setLeads] = useState<Lead[]>([])
    const [isSearching, setIsSearching] = useState(false)
    const [hasSearched, setHasSearched] = useState(false)
    const [searchResults, setSearchResults] = useState<any>(null)
    
    // Search filters state - ADDED
    const [searchFilters, setSearchFilters] = useState<SearchFilters>({
        person: {},
        company: {},
        signals: {},
        customFilters: {}
    })
    
    // Exclude my leads state - ADDED
    const [excludeMyLeads, setExcludeMyLeads] = useState(false)
    

    const [currentPage, setCurrentPage] = useState(1)
    const [totalPages, setTotalPages] = useState(1)
    const [totalCount, setTotalCount] = useState(0)
    
    // Modal states are now managed by shared hook
    const [isSavingSearch, setIsSavingSearch] = useState(false)
    const [currentSearchId, setCurrentSearchId] = useState<string>('')
    
    // Dialog state for save search
    const [saveDialogOpen, setSaveDialogOpen] = useState(false)
    const [searchName, setSearchName] = useState("")
    

    // Register unlock success callbacks
    React.useEffect(() => {
        if (leadManagement?.setUnlockEmailCallback) {
            leadManagement.setUnlockEmailCallback((unlockedLead: any) => {
                const normalizedData = unlockedLead?.normalizedData || unlockedLead?.apolloData?.normalizedData
                
                // Update the lead in the list with unlocked data
                setLeads(prev => 
                    prev.map(lead => 
                        lead.id === leadManagement?.selectedLeadId 
                            ? { 
                                ...lead, 
                                email: normalizedData?.email || "unlock",
                                normalizedData: {
                                    ...(lead as any).normalizedData,
                                    email: normalizedData?.email || (lead as any).normalizedData?.email,
                                    isEmailVisible: true // Set to true after successful unlock
                                }
                            }
                            : lead
                    )
                )
            })
        }
        
        if (leadManagement?.setUnlockPhoneCallback) {
            leadManagement.setUnlockPhoneCallback((unlockedLead: any) => {
                const normalizedData = unlockedLead?.normalizedData || unlockedLead?.apolloData?.normalizedData
                
                // Update the lead in the list with unlocked data
                setLeads(prev =>
                    prev.map(lead =>
                        lead.id === leadManagement?.selectedLeadId
                            ? {
                                ...lead,
                                phone: normalizedData?.phone || "unlock",
                                email: normalizedData?.email || lead.email,
                                normalizedData: {
                                    ...(lead as any).normalizedData,
                                    email: normalizedData?.email || (lead as any).normalizedData?.email,
                                    phone: normalizedData?.phone || (lead as any).normalizedData?.phone,
                                    isEmailVisible: normalizedData?.isEmailVisible || (lead as any).normalizedData?.isEmailVisible,
                                    isPhoneVisible: true // Set to true after successful unlock
                                }
                            }
                            : lead
                    )
                )
            })
        }
    }, [leadManagement, leads.length])
    
    // Initialize searchFilters with default values when component mounts
    React.useEffect(() => {
        const defaultFilters: SearchFilters = {
            person: {},
            company: {},
            signals: {},
            customFilters: {}
        }
        setSearchFilters(defaultFilters)
    }, [])
    
    // Search functionality
    const searchLeads = async (filters: SearchFilters = searchFilters, page: number = 1) => {

        
        if (!workspace?.workspace?.id || !token?.token) {
            toast.error("Authentication required")
            return
        }

        // Check if API is properly configured
        if (!process.env.NEXT_PUBLIC_API_URL) {
            toast.error("Search service is currently unavailable. Please try again later.")
            return
        }

        setIsSearching(true)
        try {
            const cleanFilters = { ...filters };

            const searchRequest: SearchLeadsRequest = {
                filters: cleanFilters,
                excludeMyLeads: excludeMyLeads,
                pagination: {
                    page: page,
                    limit: 50
                }
            }

            const response = await searchCompanyLeads(token.token, workspace.workspace.id, searchRequest)
            
            if (response.error) {
                // Provide user-friendly error messages
                const errorMessage = response.error.toLowerCase()
                if (errorMessage.includes('unauthorized') || errorMessage.includes('authentication')) {
                    toast.error("Session expired. Please log in again.")
                } else if (errorMessage.includes('network') || errorMessage.includes('connection')) {
                    toast.error("Connection error. Please check your internet and try again.")
                } else if (errorMessage.includes('env') || errorMessage.includes('undefined')) {
                    toast.error("Search service is currently unavailable. Please try again later.")
                } else {
                    toast.error("Failed to search company leads. Please try again.")
                }
                return
            }

            const apiLeads = response.data?.data?.leads || []
            
            console.log(`🔍 [FRONTEND DEBUG] ✅ API leads extracted:`, {
                leadsCount: apiLeads.length,
                firstLead: apiLeads[0] || null,
                allLeads: apiLeads
            });
            
            // Convert API leads to UI format using shared logic
            const convertedLeads: Lead[] = leadManagement?.convertApiLeadsToUI(apiLeads) || []

            console.log(`🔍 [FRONTEND DEBUG] Converted leads for UI:`, {
                convertedCount: convertedLeads.length,
                firstConverted: convertedLeads[0] || null
            });

            setLeads(convertedLeads)
            // Store search results with proper structure for pagination display
            setSearchResults({
                totalCount: response.data?.data?.totalCount,
                metadata: response.data?.data?.metadata || {},
                hasNextPage: response.data?.data?.hasNextPage
            })
            
            // Update pagination state
            const responseTotalCount = response.data?.data?.totalCount || 0
            const responseHasNextPage = response.data?.data?.hasNextPage || false
            
            // Calculate total pages based on cached results
            // Show ALL cached pages + ONE page to trigger Apollo expansion
            let availablePages = 1
            
            // Use the backend's totalPagesAvailable to show all cached pages
            // This ensures users can navigate to ALL available pages, plus one more to trigger Apollo
            // Example: If backend has 14 pages, show 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15
            // Where page 15 triggers Apollo to get more leads
            const totalPagesAvailable = response.data?.data?.metadata?.totalPagesAvailable || 1
            availablePages = totalPagesAvailable + 1
            
            setTotalCount(responseTotalCount)
            setTotalPages(availablePages)
            setCurrentPage(page) // Set to the page we just searched for
            
            // Track the current search ID for saving functionality
            const searchId = response.data?.data?.searchId || ''
            setCurrentSearchId(searchId)
            
            console.log(`🔍 [FRONTEND DEBUG] 📊 Pagination updated:`, {
                currentPage: page,
                totalPages: availablePages,
                totalCount: responseTotalCount,
                leadsPerPage: 50
            })
            
            setHasSearched(true)
            toast.success(`Found ${convertedLeads.length} company leads`)
            
            console.log(`🔍 [FRONTEND DEBUG] ✅ Search completed successfully. State updated.`);
            
        } catch (error) {
            console.error("Search error:", error)
            // Provide user-friendly error message without exposing technical details
            toast.error("Search service is temporarily unavailable. Please try again later.")
        } finally {
            setIsSearching(false)
            console.log(`🔍 [FRONTEND DEBUG] Search state reset to not searching`);
        }
    }
    
    // Handle filter changes from CompanyFilter
    const handleFilterChange = (filters: SearchFilters) => {
        console.log(`🔍 [FRONTEND DEBUG] handleFilterChange called with:`, JSON.stringify(filters, null, 2));
        console.log(`🔍 [FRONTEND DEBUG] Previous searchFilters:`, JSON.stringify(searchFilters, null, 2));
        
        // Reset pagination when filters change - this ensures fresh results from page 1
        // when user changes search criteria (industry, company size, etc.)
        setCurrentPage(1)
        setTotalPages(1)
        setTotalCount(0)
        setCurrentSearchId('')
        setHasSearched(false)
        setLeads([])
        setSearchResults(null)
        console.log(`🔍 [FRONTEND DEBUG] 🔄 Pagination and search state reset to page 1 (filters changed)`);
        
        // Simply replace the filters completely - CompanyFilter sends the complete state
        // No need to merge as it can cause conflicts and state inconsistencies
        setSearchFilters(filters) // FIXED: Now properly updates the state
        
        console.log(`🔍 [FRONTEND DEBUG] searchFilters state updated to:`, JSON.stringify(filters, null, 2));
        // Don't auto-search! Let user control search via Search button
        // This provides a much cleaner UX flow
    }
    
    // Pagination functions
    const handlePageChange = (page: number) => {
        console.log(`🔍 [FRONTEND DEBUG] Page change requested: ${page}`);
        if (page < 1) {
            console.log(`🔍 [FRONTEND DEBUG] ❌ Invalid page: ${page} - cannot be less than 1`);
            return
        }
        
        // Allow clicking beyond current totalPages - this will trigger Apollo calls
        // when the backend detects the requested page doesn't exist in cache
        if (page > totalPages) {
            console.log(`🔍 [FRONTEND DEBUG] 🔄 Page ${page} requested beyond current cache (${totalPages} pages)`);
            console.log(`🔍 [FRONTEND DEBUG] This will trigger Apollo call to get more leads`);
        }
        
        // Automatically search for the page with current filters - this is the fix!
        // This ensures pagination works correctly without requiring extra button clicks
        setCurrentPage(page)
        console.log(`🔍 [FRONTEND DEBUG] ✅ Page set to ${page}. Automatically searching for page ${page} results`);
        
        // Use current filters and search for the specific page
        searchLeads(searchFilters, page)
    }
    
    const calculateTotalPages = (totalLeads: number, leadsPerPage: number = 50) => {
        return Math.ceil(totalLeads / leadsPerPage)
    }
    
    const generatePageNumbers = () => {
        const pages = []
        const maxVisiblePages = 5 // Show max 5 page numbers at once
        
        if (totalPages <= maxVisiblePages) {
            // Show all pages if total is small
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i)
            }
        } else {
            // Show smart pagination with ellipsis
            if (currentPage <= 3) {
                // Near start: show 1, 2, 3, 4, 5, ..., last
                for (let i = 1; i <= 5; i++) {
                    pages.push(i)
                }
                pages.push('...')
                pages.push(totalPages)
            } else if (currentPage >= totalPages - 2) {
                // Near end: show 1, ..., last-4, last-3, last-2, last-1, last
                pages.push(1)
                pages.push('...')
                for (let i = totalPages - 4; i <= totalPages; i++) {
                    pages.push(i)
                }
            } else {
                // Middle: show 1, ..., current-1, current, current+1, ..., last
                pages.push(1)
                pages.push('...')
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pages.push(i)
                }
                pages.push('...')
                pages.push(totalPages)
            }
        }
        
        return pages
    }
    
    // Manual search trigger
    const handleSearch = () => {
        console.log(`🔍 [FRONTEND DEBUG] handleSearch called`);
        console.log(`🔍 [FRONTEND DEBUG] Current searchFilters:`, JSON.stringify(searchFilters, null, 2));
        console.log(`🔍 [FRONTEND DEBUG] Current page: ${currentPage}`);
        console.log(`🔍 [FRONTEND DEBUG] Current searchQuery: ""`); // searchQuery removed
        
        // Use the current page that user selected (don't reset to 1)
        // This allows users to click page 2, then click Search to get page 2 results
        
        // ONLY send sidebar filters - ignore search input field
        // Search input is for a different purpose, not for Apollo searches
        const filtersToSend = {
            ...searchFilters // FIXED: Now uses the actual searchFilters state
        };
        
        console.log(`🔍 [FRONTEND DEBUG] Final filters being sent to searchLeads (sidebar filters only):`, JSON.stringify(filtersToSend, null, 2));
        console.log(`🔍 [FRONTEND DEBUG] Searching for page: ${currentPage}`);
        searchLeads(filtersToSend, currentPage) // Use current page, not always page 1
    }
    
    // Event handlers
    const handleCreateLead = () => {
        console.log("Create company lead clicked")
    }
    
    // handleImportLeads is now provided by shared hook
    
        // Selection handlers are now provided by shared hook
    
    // Save current search results as a saved search
    const handleSaveLeads = () => {
        console.log('💾 [FRONTEND SAVE LEADS - COMPANY] 🚀 User clicked Save Leads button')
        console.log('💾 [FRONTEND SAVE LEADS - COMPANY] 📊 Current state:', {
            hasWorkspace: !!workspace?.workspace?.id,
            hasToken: !!token?.token,
            hasSearched,
            leadsCount: leads.length,
            currentSearchId,
            currentPage,
            searchFilters: searchFilters
        })
        
        if (!workspace?.workspace?.id || !token?.token) {
            console.log('💾 [FRONTEND SAVE LEADS - COMPANY] ❌ Missing authentication')
            toast.error("Authentication required")
            return
        }
        
        if (!hasSearched || leads.length === 0) {
            console.log('💾 [FRONTEND SAVE LEADS - COMPANY] ❌ No search results to save')
            toast.error("No search results to save")
            return
        }
        
        console.log('💾 [FRONTEND SAVE LEADS - COMPANY] ✅ Validation passed, opening save dialog')
        
        // Set default name and open dialog
        const defaultName = `Company Search - ${new Date().toLocaleDateString()}`
        console.log('💾 [FRONTEND SAVE LEADS - COMPANY] 📝 Setting default name:', defaultName)
        setSearchName(defaultName)
        setSaveDialogOpen(true)
    }
    
    // Handle the actual save operation
    const handleConfirmSave = async () => {
        console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🚀 User confirmed save operation')
        console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 📊 Save details:', {
            searchName: searchName?.trim(),
            leadsCount: leads.length,
            currentPage,
            currentSearchId,
            hasToken: !!token?.token,
            hasWorkspace: !!workspace?.workspace?.id
        })
        
        if (!searchName || searchName.trim() === "") {
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] ❌ Empty search name')
            toast.error("Search name cannot be empty")
            return
        }
        
        console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] ✅ Starting save process...')
        setIsSavingSearch(true)
        setSaveDialogOpen(false)
        
        try {
            const saveRequest = {
                name: searchName.trim(),
                description: `Saved company search with ${leads.length} leads from page ${currentPage}`,
                filters: searchFilters,
                searchId: currentSearchId || undefined,
                searchType: 'company' as const
            }
            
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 📤 Sending save request:', saveRequest)
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🔗 API call: saveSearch()')
            
            const response = await saveSearch(token.token, workspace.workspace.id, saveRequest)
            
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 📥 Received response from backend')
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🔍 Full response:', response)
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🔍 Response keys:', Object.keys(response))
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🔍 isSuccess:', response.isSuccess)
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🔍 error:', response.error)
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🔍 data:', response.data)
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🔍 data.data:', response.data?.data)
            
            if (response.isSuccess) {
                console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] ✅ Save operation successful!')
                console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 📋 Saved search details:', {
                    name: searchName,
                    leadsCount: leads.length,
                    searchId: response.data?.data?.savedSearch?.id,
                    searchType: 'company'
                })
                toast.success(`Saved "${searchName}" with ${leads.length} company leads`)
            } else {
                console.error('💾 [FRONTEND CONFIRM SAVE - COMPANY] ❌ Save operation failed')
                console.error('💾 [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error details:', {
                    isSuccess: response.isSuccess,
                    error: response.error,
                    status: response.status
                })
                toast.error(response.error || "Failed to save search")
            }
        } catch (error) {
            console.error('💾 [FRONTEND CONFIRM SAVE - COMPANY] ❌ Exception during save operation:', error)
            console.error('💾 [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error type:', typeof error)
            console.error('💾 [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error message:', error instanceof Error ? error.message : 'Unknown error')
            toast.error("Failed to save search. Please try again.")
        } finally {
            console.log('💾 [FRONTEND CONFIRM SAVE - COMPANY] 🏁 Save operation completed, resetting UI state')
            setIsSavingSearch(false)
        }
    }
    
    // Unlock handlers are now provided by shared hook
    
    // Generate contact links based on lead data
    // getContactLinks is now provided by shared hook

    const handleNameClick = (lead: Lead) => {
        // Navigate to company details page using router
        const domain = params.domain
        router.push(`/${domain}/lead-generation/companies/details/${lead.id}`)
    }
    
    // filteredLeads is now provided by shared hook
    const filteredLeads = leadManagement?.getFilteredLeads(leads, undefined, undefined) || leads

    return (
        <>
            <div className="flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white">
                <div className="flex items-center">
                    <h1 className="text-xs font-semibold text-black">Find Companies</h1>
                </div>
                <div className="flex items-center space-x-2">
                    {leadManagement?.selectedLeads.length > 0 && (
                        <Button variant="ghost" className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer" onClick={() => { setLeads(leads.filter(lead => !leadManagement?.selectedLeads.includes(lead.id))); leadManagement?.setSelectedLeads([]) }}>
                            <TrashIcon className="size-3"/>Delete {leadManagement?.selectedLeads.length}
                        </Button>
                    )}
                    {/* Search Input with Button */}
                    <div className="flex items-center gap-2">
                        {/* Inline Pagination - Left side */}
                        {hasSearched && (
                            <div className="flex items-center gap-2">
                                {/* Results info - Removed page display text */}
                                
                                {/* Pagination controls */}
                                {totalPages > 1 ? (
                                    <div className="flex items-center gap-1">
                                        {/* Previous button */}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                                            disabled={currentPage === 1}
                                            className="h-6 w-6 p-0 text-xs"
                                        >
                                            ←
                                        </Button>
                                        
                                        {/* Page numbers */}
                                        <div className="flex items-center gap-1">
                                            {generatePageNumbers().map((page, index) => (
                                                <React.Fragment key={index}>
                                                    {page === '...' ? (
                                                        <span className="px-1 text-neutral-400 text-xs">...</span>
                                                    ) : (
                                                        <Button
                                                            variant={currentPage === page ? "default" : "outline"}
                                                            size="sm"
                                                            onClick={() => handlePageChange(page as number)}
                                                            className={`h-6 w-6 p-0 text-xs ${
                                                                currentPage === page 
                                                                    ? 'bg-primary text-white' 
                                                                    : 'hover:bg-neutral-50'
                                                            }`}
                                                        >
                                                            {page}
                                                        </Button>
                                                    )}
                                                </React.Fragment>
                                            ))}
                                        </div>
                                        
                                        {/* Next button */}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                                            disabled={currentPage === totalPages}
                                            className="h-6 w-6 p-0 text-xs"
                                        >
                                            →
                                        </Button>
                                    </div>
                                ) : (
                                    <div className="text-xs text-neutral-500">
                                        Single page
                                    </div>
                                )}
                            </div>
                        )}
                        
                        <div className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer">
                            <MagnifyingGlassCircleIcon className="size-4"/>
                            <Input 
                                placeholder="Search and find companies" 
                                // value={searchQuery} // searchQuery removed
                                // onChange={(e) => setSearchQuery(e.target.value)} // searchQuery removed
                                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                className="text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground" 
                            />
                        </div>
                        <Button 
                            size="sm" 
                            onClick={handleSearch}
                            disabled={isSearching}
                            className="text-xs rounded-full h-auto px-3 py-1.5"
                        >
                            {isSearching ? "Searching..." : "Search"}
                        </Button>
                        
                        
                        {/* Save Leads Button - only show when we have search results */}
                        {hasSearched && leads.length > 0 && (
                            <Button
                                onClick={handleSaveLeads}
                                disabled={isSavingSearch}
                                size="sm"
                                variant="outline"
                                className="text-xs rounded-full h-auto px-3 py-1.5 gap-1.5"
                            >
                                <DatabaseIcon className="size-3" />
                                {isSavingSearch ? "Saving..." : `Save ${leads.length} Companies`}
                            </Button>
                        )}
                    </div>
                </div>
            </div>
            
            {/* Main Content Area */}
            <div className="flex-1 flex overflow-hidden">
                {/* Sidebar Filter - Always visible for find-leads */}
                <CompanyFilter 
                    forceSidebar={true}
                    onFilterChange={handleFilterChange}
                    excludeMyLeads={excludeMyLeads} // FIXED: Now uses the state variable
                    onExcludeMyLeadsChange={(value) => {
                        console.log(`🔒 [FRONTEND DEBUG] Parent received excludeMyLeads change:`);
                        console.log(`🔒 [FRONTEND DEBUG] - Previous value: ${excludeMyLeads}`);
                        console.log(`🔒 [FRONTEND DEBUG] - New value: ${value}`);
                        console.log(`🔒 [FRONTEND DEBUG] - This will affect display filtering, not search criteria`);
                        console.log(`🔒 [FRONTEND DEBUG] ==========================================`);
                        
                        setExcludeMyLeads(value) // FIXED: Now properly updates the state
                        console.log(`🔒 [FRONTEND DEBUG] excludeMyLeads state updated to: ${value}`);
                    }}
                />
                
                {/* Table Container */}
                <div className="flex-1 overflow-hidden">
                    {filteredLeads.length === 0 ? (
                        /* Empty State - INSIDE the container */
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <MagnifyingGlassIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-neutral-900 mb-2">
                                    {hasSearched ? "No companies found" : "Ready to find companies"}
                                </h3>
                                <p className="text-neutral-500 mb-4 text-sm">
                                    {hasSearched 
                                        ? "Try adjusting your search query or filters to find more results" 
                                        : "Use the search box above or apply filters on the left to discover companies. Click the Search button to start."
                                    }
                                </p>
                            </div>
                        </div>
                    ) : (
                        <>
                            {/* Table with Results */}
                            <ScrollArea className="size-full scrollBlockChild">
                                                    <Table>
                        <CompanyTableHeader 
                            selectedLeads={leadManagement?.selectedLeads || []}
                            filteredLeads={filteredLeads}
                            handleSelectAll={leadManagement?.handleSelectAll || (() => {})}
                        />
                        <TableBody>
                            {filteredLeads.map((lead) => (
                                <TableRow key={lead.id} className="border-b border-neutral-200 hover:bg-neutral-50 transition-colors group">
                                    <TableCell className="w-12 px-3 relative">
                                        <Checkbox checked={leadManagement?.selectedLeads.includes(lead.id)} onCheckedChange={(checked: boolean) => leadManagement?.handleSelectLead(lead.id, checked)} className={`${leadManagement?.selectedLeads.includes(lead.id) ? 'opacity-100 visible' : 'invisible opacity-0 group-hover:opacity-100 group-hover:visible'}`} />
                                    </TableCell>
                                    <TableCell className="px-1">
                                        <button 
                                            className="text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0" 
                                            onClick={() => handleNameClick(lead)}
                                        >
                                            {lead.name}
                                        </button>
                                    </TableCell>
                                    <TableCell className="px-1 text-xs text-muted-foreground">{lead.industry}</TableCell>
                                    <TableCell className="px-1 text-xs text-muted-foreground">{lead.location}</TableCell>
                                    <TableCell className="px-1">
                                        {lead.normalizedData?.email && lead.normalizedData?.isEmailVisible ? (
                                            <div className="text-xs text-black font-medium truncate max-w-[120px]" title={lead.normalizedData.email}>
                                                {lead.normalizedData.email}
                                            </div>
                                        ) : (
                                            <ActionButton icon={EnvelopeIcon} onClick={() => leadManagement?.handleUnlockEmail(lead.id)}>Unlock Email</ActionButton>
                                        )}
                                    </TableCell>
                                    <TableCell className="px-1">
                                        {lead.normalizedData?.phone && lead.normalizedData?.isPhoneVisible ? (
                                            <div className="text-xs text-black font-medium truncate max-w-[120px]" title={lead.normalizedData.phone}>
                                                {lead.normalizedData.phone}
                                            </div>
                                        ) : (
                                            <ActionButton icon={PhoneIcon} onClick={() => leadManagement?.handleUnlockPhone(lead.id)}>Unlock Mobile</ActionButton>
                                        )}
                                    </TableCell>
                                    <TableCell className="px-1">
                                        <ViewLinksModal 
                                            trigger={
                                                <button className="text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0">
                                                    View links<ChevronRightIcon className="size-3" />
                                                </button>
                                            } 
                                            links={leadManagement?.getContactLinks(lead) || []} 
                                        />
                                    </TableCell>
                                    <TableCell className="w-12 px-2 relative">
                                        <LeadActionsDropdown 
                                            trigger={
                                                <Button 
                                                    variant="ghost" 
                                                    size="sm" 
                                                    className="h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible"
                                                >
                                                    <EllipsisVerticalIcon className="size-4" />
                                                </Button>
                                            } 
                                            leadData={lead}
                                            onSendEmail={() => leadActions?.handleSendEmail(lead.id, lead)}
                                            onAddToSegments={() => leadActions?.handleAddToSegments(lead.id)}
                                            onAddToDatabase={() => leadActions?.handleAddToDatabase(lead.id, lead)}
                                            onAddToWorkflow={() => leadActions?.handleAddToWorkflow(lead.id)}
                                        />
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    {/* Horizontal line under table */}
                    <div className="border-b border-neutral-200"></div>
                </ScrollArea>
                
                
                </>
                    )}
                </div>
            </div>
        
            {/* Unlock modals are now handled by SharedModals in the index */}
            
            {/* Save Search Dialog */}
            <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
                <DialogContent className="max-w-[95vw] sm:max-w-[600px] !rounded-none p-4" aria-describedby="save-search-description">
                    <DialogHeader>
                        <DialogTitle>Save Search</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <p id="save-search-description" className="text-sm text-gray-500 mb-4">Enter a name for this saved search:</p>
                        <Input
                            value={searchName}
                            onChange={(e) => setSearchName(e.target.value)}
                            placeholder="Enter search name"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    handleConfirmSave()
                                }
                            }}
                        />
                    </div>
                    <DialogFooter>
                        <Button variant="outline" size="sm" className="text-xs" onClick={() => setSaveDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="outline" size="sm" className="text-xs" onClick={handleConfirmSave}>
                            Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}

export default FindLeads
