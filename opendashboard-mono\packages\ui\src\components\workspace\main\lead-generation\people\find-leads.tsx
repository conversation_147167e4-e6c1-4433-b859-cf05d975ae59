"use client"

import React, { useState, use<PERSON>emo } from "react"
import { <PERSON><PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { ScrollArea } from "@ui/components/ui/scroll-area"
import { PlusIcon, UserGroupIcon, EnvelopeIcon, PhoneIcon, EllipsisVerticalIcon, TrashIcon, ChevronRightIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon, MagnifyingGlassIcon } from "@ui/components/icons/FontAwesomeRegular"
import { MagnifyingGlassCircleIcon } from "@heroicons/react/24/outline"
import { Loader } from "@ui/components/custom-ui/loader"
import { PeopleFilter } from "@ui/components/workspace/main/lead-generation/filters/people"
import { PeopleTableHeader } from "./index"

import { useRouter, usePara<PERSON> } from "@ui/context/routerContext"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { searchPeopleLeads, saveSearch } from "@ui/api/leads"
import { SearchFilters, SearchLeadsRequest, Lead as APILead, NormalizedLeadData } from "@ui/typings/lead"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { useAlert } from "@ui/providers/alert"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@ui/components/ui/dialog"


interface Lead {
    id: string
    name: string
    jobTitle: string
    company: string
    email: string
    phone: string
    links: string
    location: string
}

interface FindLeadsProps {
    onLeadCreated?: (lead: any) => void
    sidebarState?: {
        isOpen: boolean
        setIsOpen: (open: boolean) => void
    }

    ActionButton?: any
    ViewLinksModal?: any
    LeadActionsDropdown?: any
    leadActions?: any
    leadManagement?: any
}



const FindLeads = ({ onLeadCreated, sidebarState, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement }: FindLeadsProps) => {
    const router = useRouter()
    const params = useParams()
    const { workspace } = useWorkspace()
    const { token } = useAuth()
    const { toast } = useAlert()
    

    const [leads, setLeads] = useState<Lead[]>([])
    const [isSearching, setIsSearching] = useState(false)
    const [hasSearched, setHasSearched] = useState(false)
    const [searchResults, setSearchResults] = useState<any>(null)
    

    const [searchFilters, setSearchFilters] = useState<SearchFilters>({
        person: {},
        company: {},
        signals: {},
        customFilters: {}
    })
    

    
    const [excludeMyLeads, setExcludeMyLeads] = useState(false)
    

    const [currentPage, setCurrentPage] = useState(1)
    const [totalPages, setTotalPages] = useState(1)
    const [totalCount, setTotalCount] = useState(0)
    

    React.useEffect(() => {
        if (leadManagement?.setUnlockEmailCallback) {
            leadManagement.setUnlockEmailCallback((unlockedLead: any) => {
                // Update the lead in the list with unlocked data
                setLeads(prev => 
                    prev.map(lead => 
                        lead.id === leadManagement?.selectedLeadId 
                            ? { 
                                ...lead, 
                                email: unlockedLead?.normalizedData?.email || "unlock",
                                normalizedData: {
                                    ...(lead as any).normalizedData,
                                    email: unlockedLead?.normalizedData?.email || (lead as any).normalizedData?.email,
                                    isEmailVisible: unlockedLead?.normalizedData?.isEmailVisible || (lead as any).normalizedData?.isEmailVisible
                                }
                            }
                            : lead
                    )
                )
            })
        }
        
        if (leadManagement?.setUnlockPhoneCallback) {
            leadManagement.setUnlockPhoneCallback((unlockedLead: any) => {
                setLeads(prev =>
                    prev.map(lead =>
                        lead.id === leadManagement?.selectedLeadId
                            ? {
                                ...lead,
                                phone: unlockedLead.normalizedData?.phone || "unlock",
                                email: unlockedLead.normalizedData?.email || lead.email
                            }
                            : lead
                    )
                )
            })
        }
    }, [leadManagement, leads.length])
    

    const [isSavingSearch, setIsSavingSearch] = useState(false)
    const [currentSearchId, setCurrentSearchId] = useState<string>("")
    

    const [saveDialogOpen, setSaveDialogOpen] = useState(false)
    const [searchName, setSearchName] = useState("")
    

    React.useEffect(() => {
        const defaultFilters: SearchFilters = {
            person: {},
            company: {},
            signals: {},
            customFilters: {}
        }
        setSearchFilters(defaultFilters)

    }, [])
    
    const searchLeads = async (filters: SearchFilters = searchFilters, page: number = 1) => {
        if (!workspace?.workspace?.id || !token?.token) {
            toast.error("Authentication required")
            return
        }

        if (!process.env.NEXT_PUBLIC_API_URL) {
            toast.error("Search service is currently unavailable. Please try again later.")
            return
        }

        setIsSearching(true)
        try {
            const cleanFilters = { ...searchFilters };

            const searchRequest: SearchLeadsRequest = {
                filters: cleanFilters,
                excludeMyLeads,
                pagination: {
                    page: page,
                    limit: 50
                }
            }

            const response = await searchPeopleLeads(token.token, workspace.workspace.id, searchRequest)

            if (response.error) {
                // Provide user-friendly error messages
                const errorMessage = response.error.toLowerCase()
                if (errorMessage.includes('unauthorized') || errorMessage.includes('authentication')) {
                    toast.error("Session expired. Please log in again.")
                } else if (errorMessage.includes('network') || errorMessage.includes('connection')) {
                    toast.error("Connection error. Please check your internet and try again.")
                } else if (errorMessage.includes('env') || errorMessage.includes('undefined')) {
                    toast.error("Search service is currently unavailable. Please try again later.")
                } else {
                    toast.error("Failed to search leads. Please try again.")
                }
                return
            }

            if (!response.data || !response.data.data) {
                toast.error("No search results found. Please try different search criteria.")
                return
            }

            const apiLeads = response.data?.data?.leads || []

            const convertedLeads: Lead[] = leadManagement?.convertApiLeadsToUI(apiLeads) || []

            setLeads(convertedLeads)
            setSearchResults({
                totalCount: response.data?.data?.totalCount,
                metadata: response.data?.data?.metadata || {},
                hasNextPage: response.data?.data?.hasNextPage
            })

            const searchId = response.data?.data?.searchId || ""
            setCurrentSearchId(searchId)

            const responseTotalCount = response.data?.data?.totalCount || 0

            let availablePages = 1
            const totalPagesAvailable = response.data?.data?.metadata?.totalPagesAvailable || 1
            availablePages = totalPagesAvailable + 1

            setTotalCount(responseTotalCount)
            setTotalPages(availablePages)
            setCurrentPage(page)

            setHasSearched(true)
            toast.success(`Found ${convertedLeads.length} leads`)

        } catch (error) {
            toast.error("Search service is temporarily unavailable. Please try again later.")
        } finally {
            setIsSearching(false)
        }
    }
    
    const handleFilterChange = (filters: SearchFilters) => {
        setCurrentPage(1)
        setTotalPages(1)
        setTotalCount(0)
        setCurrentSearchId("")
        setHasSearched(false)
        setLeads([])
        setSearchResults(null)

        setSearchFilters(filters)
    }
    
    const handlePageChange = (page: number) => {
        if (page < 1) {
            return
        }

        setCurrentPage(page)
        searchLeads(searchFilters, page)
    }
    
    const calculateTotalPages = (totalLeads: number, leadsPerPage: number = 50) => {
        return Math.ceil(totalLeads / leadsPerPage)
    }
    
    const generatePageNumbers = () => {
        const pages = []
        const maxVisiblePages = 5

        if (totalPages <= maxVisiblePages) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i)
            }
        } else {
            if (currentPage <= 3) {
                for (let i = 1; i <= 5; i++) {
                    pages.push(i)
                }
                pages.push('...')
                pages.push(totalPages)
            } else if (currentPage >= totalPages - 2) {
                pages.push(1)
                pages.push('...')
                for (let i = totalPages - 4; i <= totalPages; i++) {
                    pages.push(i)
                }
            } else {
                pages.push(1)
                pages.push('...')
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pages.push(i)
                }
                pages.push('...')
                pages.push(totalPages)
            }
        }

        return pages
    }
    
    const handleSearch = () => {
        const filtersToSend = {
            ...searchFilters
        };

        searchLeads(filtersToSend, currentPage)
    }
    
    const handleSaveLeads = () => {
        if (!workspace?.workspace?.id || !token?.token) {
            toast.error("Authentication required")
            return
        }

        if (!hasSearched || leads.length === 0) {
            toast.error("No search results to save")
            return
        }

        const defaultName = `Search Results - ${new Date().toLocaleDateString()}`
        setSearchName(defaultName)
        setSaveDialogOpen(true)
    }
    
    const handleConfirmSave = async () => {
        if (!searchName || searchName.trim() === "") {
            toast.error("Search name cannot be empty")
            return
        }

        setIsSavingSearch(true)
        setSaveDialogOpen(false)

        try {
            const saveRequest = {
                name: searchName.trim(),
                description: `Saved search with ${leads.length} leads from page ${currentPage}`,
                filters: searchFilters,
                searchId: currentSearchId || undefined,
                searchType: 'people' as const
            }

            const response = await saveSearch(token.token, workspace.workspace.id, saveRequest)

            if (response.isSuccess) {
                toast.success(`Saved "${searchName}" with ${leads.length} leads`)
            } else {
                toast.error(response.error || "Failed to save search")
            }
        } catch (error) {
            toast.error("Failed to save search. Please try again.")
        } finally {
            setIsSavingSearch(false)
        }
    }
    
    // Filter and search functionality - simplified to work with real API data
    // filteredLeads is now provided by lead management hook
    const filteredLeads = leadManagement?.getFilteredLeads(leads, undefined, undefined) || leads
    


    return (
        <>
            {/* Content Header */}
            <div className="flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white">
                <div className="flex items-center">
                    <h1 className="text-xs font-semibold text-black">Find People</h1>
                </div>
                
                <div className="flex items-center space-x-2">
                    {/* Delete button when items are selected */}
                    {leadManagement?.selectedLeads.length > 0 && (
                        <Button 
                            variant="ghost"
                            className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer"
                            onClick={() => {
                                setLeads(leads.filter(lead => !leadManagement?.selectedLeads.includes(lead.id)))
                                leadManagement?.setSelectedLeads([])
                            }}
                        >
                            <TrashIcon className="size-3"/>
                            Delete {leadManagement?.selectedLeads.length}
                        </Button>
                    )}
                    
                    {/* Search Input with Button */}
                    <div className="flex items-center gap-2">
                        {/* Inline Pagination - Left side */}
                        {hasSearched && (
                            <div className="flex items-center gap-2">
                                {/* Results info */}
                                <div className="text-xs text-neutral-600 whitespace-nowrap">
                                    Page {currentPage} of {totalPages}
                                    {totalCount > 0 && ` (${totalCount} total)`}
                                </div>
                                
                                {/* Pagination controls */}
                                {totalPages > 1 ? (
                                    <div className="flex items-center gap-1">
                                        {/* Previous button */}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                                            disabled={currentPage === 1}
                                            className="h-6 w-6 p-0 text-xs"
                                        >
                                            ←
                                        </Button>
                                        
                                        {/* Page numbers */}
                                        <div className="flex items-center gap-1">
                                            {generatePageNumbers().map((page, index) => (
                                                <React.Fragment key={index}>
                                                    {page === '...' ? (
                                                        <span className="px-1 text-neutral-400 text-xs">...</span>
                                                    ) : (
                                                        <Button
                                                            variant={currentPage === page ? "default" : "outline"}
                                                            size="sm"
                                                            onClick={() => handlePageChange(page as number)}
                                                            className={`h-6 w-6 p-0 text-xs ${
                                                                currentPage === page 
                                                                    ? 'bg-primary text-white' 
                                                                    : 'hover:bg-neutral-50'
                                                            }`}
                                                        >
                                                            {page}
                                                        </Button>
                                                    )}
                                                </React.Fragment>
                                            ))}
                                        </div>
                                        
                                        {/* Next button */}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                                            disabled={currentPage === totalPages}
                                            className="h-6 w-6 p-0 text-xs"
                                        >
                                            →
                                        </Button>
                                    </div>
                                ) : (
                                    <div className="text-xs text-neutral-500">
                                        Single page
                                    </div>
                                )}
                            </div>
                        )}
                        
                        <div className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer">
                            <MagnifyingGlassCircleIcon className="size-4"/>
                            <Input 
                                placeholder="Search and find people"
                                // value={searchQuery} // searchQuery is removed
                                // onChange={e => setSearchQuery(e.target.value)} // searchQuery is removed
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        handleSearch()
                                    }
                                }}
                                className="text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground" 
                            />
                            {isSearching && (
                                <Loader theme="dark" className="size-3" />
                            )}
                        </div>
                        <Button
                            onClick={handleSearch}
                            disabled={isSearching}
                            size="sm"
                            className="text-xs rounded-full h-auto px-3 py-1.5"
                        >
                            {isSearching ? "Searching..." : "Search"}
                        </Button>
                        
                        {/* Save Leads Button - only show when we have search results */}
                        {hasSearched && leads.length > 0 && (
                            <Button 
                                onClick={handleSaveLeads}
                                disabled={isSavingSearch}
                                size="sm" 
                                variant="outline" 
                                className="text-xs rounded-full h-auto px-3 py-1.5 gap-1.5"
                            >
                                <DatabaseIcon className="size-3" />
                                {isSavingSearch ? "Saving..." : `Save ${leads.length} Leads`}
                            </Button>
                        )}
                    </div>
                </div>
            </div>
            
            {/* Main Content Area */}
            <div className="flex-1 flex overflow-hidden">
                {/* Sidebar Filter - Always visible for find-leads */}
                            <PeopleFilter 
              forceSidebar={true}
              onFilterChange={handleFilterChange}
              excludeMyLeads={excludeMyLeads}
              onExcludeMyLeadsChange={(value) => {
             
                setExcludeMyLeads(value)
              }}
            />
                
                {/* Table Container */}
                <div className="flex-1 overflow-hidden">
                    {filteredLeads.length === 0 ? (
                        /* Empty State */
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <MagnifyingGlassIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-neutral-900 mb-2">
                                    {hasSearched ? "No people found" : "Ready to find people"}
                                </h3>
                                <p className="text-neutral-500 mb-4 text-sm">
                                    {hasSearched 
                                        ? "Try adjusting your search query or filters to find more results" 
                                        : "Use the search box above or apply filters on the left to discover people. Click the Search button to start."
                                    }
                                </p>
                            </div>
                        </div>
                    ) : (
                        <>
                            {/* Table with Results */}
                        <ScrollArea className="size-full scrollBlockChild">
                            <Table>
                        <PeopleTableHeader 
                            selectedLeads={leadManagement?.selectedLeads || []}
                            filteredLeads={filteredLeads}
                            handleSelectAll={leadManagement?.handleSelectAll || (() => {})}
                        />
                                                <TableBody>
                            {(() => {
                                return filteredLeads.map((lead) => (
                                    <TableRow key={lead.id} className="border-b border-neutral-200 hover:bg-neutral-50 transition-colors group">
                                        <TableCell className="w-12 px-3 relative">
                                            <Checkbox
                                                checked={leadManagement?.selectedLeads.includes(lead.id)}
                                                onCheckedChange={(checked: boolean) => leadManagement?.handleSelectLead(lead.id, checked)}
                                                className={`${leadManagement?.selectedLeads.includes(lead.id) ? 'opacity-100 visible' : 'invisible opacity-0 group-hover:opacity-100 group-hover:visible'}`}
                                            />
                                        </TableCell>
                                        <TableCell className="px-1">
                                            <button 
                                                className="text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0" 
                                                onClick={() => leadManagement?.handleNameClick(lead)}
                                            >
                                                {lead.name}
                                            </button>
                                        </TableCell>
                                        <TableCell className="px-1 text-xs text-muted-foreground">{lead.jobTitle}</TableCell>
                                        <TableCell className="px-1 text-xs text-muted-foreground">{lead.company}</TableCell>
                                        <TableCell className="px-1">
                                            {lead.email && lead.email !== "unlock" ? (
                                                // Show unlocked email
                                                <div className="text-xs text-black font-medium truncate max-w-[120px]" title={lead.email}>
                                                    {lead.email}
                                                </div>
                                            ) : (
                                                // Show unlock button
                                                <ActionButton 
                                                    icon={EnvelopeIcon} 
                                                    onClick={() => leadManagement?.handleUnlockEmail(lead.id)}
                                                >
                                                    Unlock Email
                                                </ActionButton>
                                            )}
                                        </TableCell>
                                        <TableCell className="px-1">
                                            {lead.phone && lead.phone !== "unlock" ? (
                                                // Show unlocked phone
                                                <div className="text-xs text-black font-medium truncate max-w-[120px]" title={lead.phone}>
                                                    {lead.phone}
                                                </div>
                                            ) : (
                                                // Show unlock button
                                                <ActionButton 
                                                    icon={PhoneIcon} 
                                                    onClick={() => leadManagement?.handleUnlockPhone(lead.id)}
                                                >
                                                    Unlock Mobile
                                                </ActionButton>
                                            )}
                                        </TableCell>
                                        <TableCell className="px-1">
                                            <ViewLinksModal
                                                trigger={
                                                    <button className="text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0">
                                                        View links
                                                        <ChevronRightIcon className="size-3" />
                                                    </button>
                                                }
                                                links={leadManagement?.getContactLinks(lead) || []}
                                            />
                                        </TableCell>
                                        <TableCell className="w-12 px-2 relative">
                                            <LeadActionsDropdown
                                                trigger={
                                                    <Button 
                                                        variant="ghost" 
                                                        size="sm" 
                                                        className="h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible"
                                                    >
                                                        <EllipsisVerticalIcon className="size-4" />
                                                </Button>
                                                }
                                                leadData={lead}
                                                onSendEmail={() => leadActions?.handleSendEmail(lead.id, lead)}
                                                onAddToSegments={() => leadActions?.handleAddToSegments(lead.id)}
                                                onAddToDatabase={() => leadActions?.handleAddToDatabase(lead.id, lead)}
                                                onAddToWorkflow={() => leadActions?.handleAddToWorkflow(lead.id)}
                                            />
                                        </TableCell>
                                    </TableRow>
                                ))
                            })()}
                        </TableBody>
                    </Table>
                        {/* Horizontal line under table */}
                        <div className="border-b border-neutral-200"></div>
                    </ScrollArea>
                    

                        </>
                    )}
                </div>
            </div>
        
            {/* Unlock Modals */}
            {leadManagement?.selectedLeadId && (
                <>
                    {/* Unlock modals are now handled by SharedModals in the index */}
                </>
            )}
            
            {/* Save Search Dialog */}
            <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
                <DialogContent className="max-w-[95vw] sm:max-w-[600px] !rounded-none p-4" aria-describedby="save-search-description">
                    <DialogHeader>
                        <DialogTitle>Save Search</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <p id="save-search-description" className="text-sm text-gray-500 mb-4">Enter a name for this saved search:</p>
                        <Input
                            value={searchName}
                            onChange={(e) => setSearchName(e.target.value)}
                            placeholder="Enter search name"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    handleConfirmSave()
                                }
                            }}
                        />
                    </div>
                    <DialogFooter>
                        <Button variant="outline" size="sm" className="text-xs" onClick={() => setSaveDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="outline" size="sm" className="text-xs" onClick={handleConfirmSave}>
                            Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            
            {/* Add to Database Dialog is now provided by shared hook */}
        </>
    )
}

export default FindLeads
