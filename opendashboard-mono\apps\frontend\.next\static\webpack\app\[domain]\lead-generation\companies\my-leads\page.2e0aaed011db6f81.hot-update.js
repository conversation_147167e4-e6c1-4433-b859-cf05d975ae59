"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/lead-generation/companies/my-leads/page",{

/***/ "(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/people/find-leads.tsx":
/*!*********************************************************************************************!*\
  !*** ../../packages/ui/src/components/workspace/main/lead-generation/people/find-leads.tsx ***!
  \*********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ui/components/ui/button */ \"(app-pages-browser)/../../packages/ui/src/components/ui/button.tsx\");\n/* harmony import */ var _ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ui/components/ui/input */ \"(app-pages-browser)/../../packages/ui/src/components/ui/input.tsx\");\n/* harmony import */ var _ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ui/components/ui/checkbox */ \"(app-pages-browser)/../../packages/ui/src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ui/components/ui/table */ \"(app-pages-browser)/../../packages/ui/src/components/ui/table.tsx\");\n/* harmony import */ var _ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ui/components/ui/scroll-area */ \"(app-pages-browser)/../../packages/ui/src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ui/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/../../packages/ui/src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassCircleIcon.js\");\n/* harmony import */ var _ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ui/components/custom-ui/loader */ \"(app-pages-browser)/../../packages/ui/src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _ui_components_workspace_main_lead_generation_filters_people__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ui/components/workspace/main/lead-generation/filters/people */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/filters/people.tsx\");\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./index */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/people/index.tsx\");\n/* harmony import */ var _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ui/context/routerContext */ \"(app-pages-browser)/../../packages/ui/src/context/routerContext.tsx\");\n/* harmony import */ var _ui_api_leads__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ui/api/leads */ \"(app-pages-browser)/../../packages/ui/src/api/leads.ts\");\n/* harmony import */ var _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @ui/providers/workspace */ \"(app-pages-browser)/../../packages/ui/src/providers/workspace.tsx\");\n/* harmony import */ var _ui_providers_user__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ui/providers/user */ \"(app-pages-browser)/../../packages/ui/src/providers/user.tsx\");\n/* harmony import */ var _ui_providers_alert__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @ui/providers/alert */ \"(app-pages-browser)/../../packages/ui/src/providers/alert.tsx\");\n/* harmony import */ var _ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @ui/components/ui/dialog */ \"(app-pages-browser)/../../packages/ui/src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst FindLeads = (param)=>{\n    let { onLeadCreated, sidebarState, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement } = param;\n    _s();\n    const router = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const params = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__.useParams)();\n    const { workspace } = (0,_ui_providers_workspace__WEBPACK_IMPORTED_MODULE_13__.useWorkspace)();\n    const { token } = (0,_ui_providers_user__WEBPACK_IMPORTED_MODULE_14__.useAuth)();\n    const { toast } = (0,_ui_providers_alert__WEBPACK_IMPORTED_MODULE_15__.useAlert)();\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchFilters, setSearchFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        person: {},\n        company: {},\n        signals: {},\n        customFilters: {}\n    });\n    const [excludeMyLeads, setExcludeMyLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockEmailCallback) {\n            leadManagement.setUnlockEmailCallback((unlockedLead)=>{\n                // Update the lead in the list with unlocked data\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _unlockedLead_normalizedData, _unlockedLead_normalizedData1, _lead_normalizedData, _unlockedLead_normalizedData2, _lead_normalizedData1;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            email: (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_normalizedData = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData === void 0 ? void 0 : _unlockedLead_normalizedData.email) || \"unlock\",\n                            normalizedData: {\n                                ...lead.normalizedData,\n                                email: (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_normalizedData1 = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData1 === void 0 ? void 0 : _unlockedLead_normalizedData1.email) || ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email),\n                                isEmailVisible: (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_normalizedData2 = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData2 === void 0 ? void 0 : _unlockedLead_normalizedData2.isEmailVisible) || ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.isEmailVisible)\n                            }\n                        } : lead;\n                    }));\n            });\n        }\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockPhoneCallback) {\n            leadManagement.setUnlockPhoneCallback((unlockedLead)=>{\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _unlockedLead_normalizedData, _unlockedLead_normalizedData1;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            phone: ((_unlockedLead_normalizedData = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData === void 0 ? void 0 : _unlockedLead_normalizedData.phone) || \"unlock\",\n                            email: ((_unlockedLead_normalizedData1 = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData1 === void 0 ? void 0 : _unlockedLead_normalizedData1.email) || lead.email\n                        } : lead;\n                    }));\n            });\n        }\n    }, [\n        leadManagement,\n        leads.length\n    ]);\n    const [isSavingSearch, setIsSavingSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSearchId, setCurrentSearchId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [saveDialogOpen, setSaveDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchName, setSearchName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const defaultFilters = {\n            person: {},\n            company: {},\n            signals: {},\n            customFilters: {}\n        };\n        setSearchFilters(defaultFilters);\n    }, []);\n    const searchLeads = async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : searchFilters, page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        var _workspace_workspace;\n        if (!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || !(token === null || token === void 0 ? void 0 : token.token)) {\n            toast.error(\"Authentication required\");\n            return;\n        }\n        if (false) {}\n        setIsSearching(true);\n        try {\n            var _response_data_data, _response_data, _apiLeads__normalizedData, _apiLeads__normalizedData1, _apiLeads__normalizedData2, _response_data_data1, _response_data1, _response_data_data2, _response_data2, _response_data_data3, _response_data3, _response_data_data4, _response_data4, _response_data_data5, _response_data5, _response_data_data6, _response_data6, _response_data_data_metadata, _response_data_data7, _response_data7;\n            const cleanFilters = {\n                ...searchFilters\n            };\n            const searchRequest = {\n                filters: cleanFilters,\n                excludeMyLeads,\n                pagination: {\n                    page: page,\n                    limit: 50\n                }\n            };\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_12__.searchPeopleLeads)(token.token, workspace.workspace.id, searchRequest);\n            if (response.error) {\n                // Provide user-friendly error messages\n                const errorMessage = response.error.toLowerCase();\n                if (errorMessage.includes(\"unauthorized\") || errorMessage.includes(\"authentication\")) {\n                    toast.error(\"Session expired. Please log in again.\");\n                } else if (errorMessage.includes(\"network\") || errorMessage.includes(\"connection\")) {\n                    toast.error(\"Connection error. Please check your internet and try again.\");\n                } else if (errorMessage.includes(\"env\") || errorMessage.includes(\"undefined\")) {\n                    toast.error(\"Search service is currently unavailable. Please try again later.\");\n                } else {\n                    toast.error(\"Failed to search leads. Please try again.\");\n                }\n                return;\n            }\n            // Check if response is successful but has no data\n            if (!response.data || !response.data.data) {\n                console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ❌ API returned no data:\", response);\n                toast.error(\"No search results found. Please try different search criteria.\");\n                return;\n            }\n            const apiLeads = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.leads) || [];\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ✅ API leads extracted:\", {\n                leadsCount: apiLeads.length,\n                firstLead: apiLeads[0] ? {\n                    id: apiLeads[0].id,\n                    name: (_apiLeads__normalizedData = apiLeads[0].normalizedData) === null || _apiLeads__normalizedData === void 0 ? void 0 : _apiLeads__normalizedData.name,\n                    jobTitle: (_apiLeads__normalizedData1 = apiLeads[0].normalizedData) === null || _apiLeads__normalizedData1 === void 0 ? void 0 : _apiLeads__normalizedData1.jobTitle,\n                    company: (_apiLeads__normalizedData2 = apiLeads[0].normalizedData) === null || _apiLeads__normalizedData2 === void 0 ? void 0 : _apiLeads__normalizedData2.company\n                } : null,\n                allLeads: apiLeads.map((lead)=>{\n                    var _lead_normalizedData, _lead_normalizedData1, _lead_normalizedData2;\n                    return {\n                        id: lead.id,\n                        name: (_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.name,\n                        jobTitle: (_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.jobTitle,\n                        company: (_lead_normalizedData2 = lead.normalizedData) === null || _lead_normalizedData2 === void 0 ? void 0 : _lead_normalizedData2.company\n                    };\n                })\n            });\n            // Convert API leads to UI format using shared logic\n            const convertedLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.convertApiLeadsToUI(apiLeads)) || [];\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Converted leads for UI:\", {\n                convertedCount: convertedLeads.length,\n                firstConverted: convertedLeads[0] ? {\n                    id: convertedLeads[0].id,\n                    name: convertedLeads[0].name,\n                    jobTitle: convertedLeads[0].jobTitle,\n                    company: convertedLeads[0].company,\n                    email: convertedLeads[0].email,\n                    phone: convertedLeads[0].phone\n                } : null\n            });\n            setLeads(convertedLeads);\n            // Store search results with proper structure for pagination display\n            setSearchResults({\n                totalCount: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data1 = _response_data1.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.totalCount,\n                metadata: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : (_response_data_data2 = _response_data2.data) === null || _response_data_data2 === void 0 ? void 0 : _response_data_data2.metadata) || {},\n                hasNextPage: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data3 = _response_data3.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.hasNextPage\n            });\n            // Capture searchId for saving searches\n            const searchId = ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data4 = _response_data4.data) === null || _response_data_data4 === void 0 ? void 0 : _response_data_data4.searchId) || \"\";\n            setCurrentSearchId(searchId);\n            // Update pagination state\n            const responseTotalCount = ((_response_data5 = response.data) === null || _response_data5 === void 0 ? void 0 : (_response_data_data5 = _response_data5.data) === null || _response_data_data5 === void 0 ? void 0 : _response_data_data5.totalCount) || 0;\n            const responseHasNextPage = ((_response_data6 = response.data) === null || _response_data6 === void 0 ? void 0 : (_response_data_data6 = _response_data6.data) === null || _response_data_data6 === void 0 ? void 0 : _response_data_data6.hasNextPage) || false;\n            // Calculate total pages based on cached results\n            // Show ALL cached pages + ONE page to trigger Apollo expansion\n            let availablePages = 1;\n            // Use the backend's totalPagesAvailable to show all cached pages\n            // This ensures users can navigate to ALL available pages, plus one more to trigger Apollo\n            // Example: If backend has 14 pages, show 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n            // Where page 15 triggers Apollo to get more leads\n            const totalPagesAvailable = ((_response_data7 = response.data) === null || _response_data7 === void 0 ? void 0 : (_response_data_data7 = _response_data7.data) === null || _response_data_data7 === void 0 ? void 0 : (_response_data_data_metadata = _response_data_data7.metadata) === null || _response_data_data_metadata === void 0 ? void 0 : _response_data_data_metadata.totalPagesAvailable) || 1;\n            availablePages = totalPagesAvailable + 1;\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] Backend totalPagesAvailable: \".concat(totalPagesAvailable));\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] Frontend setting totalPages to: \".concat(availablePages, \" (\").concat(totalPagesAvailable, \" + 1)\"));\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] This shows cached pages + 1 extra to trigger Apollo\");\n            setTotalCount(responseTotalCount);\n            setTotalPages(availablePages);\n            setCurrentPage(page) // Set to the page we just searched for\n            ;\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDCCA Pagination updated:\", {\n                currentPage: page,\n                totalPages: availablePages,\n                totalCount: responseTotalCount,\n                leadsPerPage: 50\n            });\n            setHasSearched(true);\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ✅ Search completed successfully. State updated.\");\n            toast.success(\"Found \".concat(convertedLeads.length, \" leads\"));\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ❌ Search error:\", error);\n            // Provide user-friendly error message without exposing technical details\n            toast.error(\"Search service is temporarily unavailable. Please try again later.\");\n        } finally{\n            setIsSearching(false);\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Search state reset to not searching\");\n        }\n    };\n    // Handle filter changes from LeadFilter\n    const handleFilterChange = (filters)=>{\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] handleFilterChange called with:\", JSON.stringify(filters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Previous searchFilters:\", JSON.stringify(searchFilters, null, 2));\n        // Reset pagination and search state when filters change - this ensures fresh results from page 1\n        // when user changes search criteria (job titles, location, etc.)\n        setCurrentPage(1);\n        setTotalPages(1);\n        setTotalCount(0);\n        setCurrentSearchId(\"\");\n        setHasSearched(false);\n        setLeads([]);\n        setSearchResults(null);\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDD04 Pagination and search state reset (filters changed)\");\n        // Simply replace the filters completely - PeopleFilter sends the complete state\n        // No need to merge as it can cause conflicts and state inconsistencies\n        setSearchFilters(filters);\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] searchFilters state updated to:\", JSON.stringify(filters, null, 2));\n    // Don't auto-search! Let user control search via Search button\n    // This provides a much cleaner UX flow\n    };\n    // Pagination functions\n    const handlePageChange = (page)=>{\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] ==========================================\");\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] \\uD83D\\uDE80 PAGE CHANGE REQUESTED: \".concat(page));\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] Current page: \".concat(currentPage));\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] Total pages: \".concat(totalPages));\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] Current searchFilters:\", JSON.stringify(searchFilters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] Current searchId: \".concat(currentSearchId));\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] ==========================================\");\n        if (page < 1) {\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] ❌ Invalid page: \".concat(page, \" - cannot be less than 1\"));\n            return;\n        }\n        // Check if user is requesting a page beyond what's cached\n        if (page > totalPages) {\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] \\uD83D\\uDD04 Page \".concat(page, \" requested beyond current cache (\").concat(totalPages, \" pages)\"));\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] This will trigger Apollo call to get more leads\");\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] Backend will detect this and call Apollo for page \".concat(page));\n        } else {\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] ✅ Page \".concat(page, \" is within cached range (\").concat(totalPages, \" pages)\"));\n            console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] Backend should return cached results for this page\");\n        }\n        setCurrentPage(page);\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] ✅ Page set to \".concat(page, \". Automatically searching for page \").concat(page, \" results\"));\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] \\uD83D\\uDD0D Calling searchLeads with filters:\", JSON.stringify(searchFilters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [PAGINATION DEBUG] ==========================================\");\n        searchLeads(searchFilters, page) // This is the core fix for auto-searching on page change\n        ;\n    };\n    const calculateTotalPages = function(totalLeads) {\n        let leadsPerPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        return Math.ceil(totalLeads / leadsPerPage);\n    };\n    const generatePageNumbers = ()=>{\n        const pages = [];\n        const maxVisiblePages = 5 // Show max 5 page numbers at once\n        ;\n        if (totalPages <= maxVisiblePages) {\n            // Show all pages if total is small\n            for(let i = 1; i <= totalPages; i++){\n                pages.push(i);\n            }\n        } else {\n            // Show smart pagination with ellipsis\n            if (currentPage <= 3) {\n                // Near start: show 1, 2, 3, 4, 5, ..., last\n                for(let i = 1; i <= 5; i++){\n                    pages.push(i);\n                }\n                pages.push(\"...\");\n                pages.push(totalPages);\n            } else if (currentPage >= totalPages - 2) {\n                // Near end: show 1, ..., last-4, last-3, last-2, last-1, last\n                pages.push(1);\n                pages.push(\"...\");\n                for(let i = totalPages - 4; i <= totalPages; i++){\n                    pages.push(i);\n                }\n            } else {\n                // Middle: show 1, ..., current-1, current, current+1, ..., last\n                pages.push(1);\n                pages.push(\"...\");\n                for(let i = currentPage - 1; i <= currentPage + 1; i++){\n                    pages.push(i);\n                }\n                pages.push(\"...\");\n                pages.push(totalPages);\n            }\n        }\n        return pages;\n    };\n    // Manual search trigger\n    const handleSearch = ()=>{\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] ==========================================\");\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] \\uD83D\\uDE80 SEARCH BUTTON CLICKED\");\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] Current searchFilters:\", JSON.stringify(searchFilters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] Current page: \".concat(currentPage));\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] Current searchId: \".concat(currentSearchId));\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] Total pages: \".concat(totalPages));\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] ==========================================\");\n        // Use the current page that user selected (don't reset to 1)\n        // This allows users to click page 2, then click Search to get page 2 results\n        // ONLY send sidebar filters - ignore search input field\n        // Search input is for a different purpose, not for Apollo searches\n        const filtersToSend = {\n            ...searchFilters\n        };\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] \\uD83D\\uDD0D Final filters being sent to searchLeads:\", JSON.stringify(filtersToSend, null, 2));\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] \\uD83D\\uDD0D Searching for page: \".concat(currentPage));\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] \\uD83D\\uDD0D Filters comparison - Original vs ToSend:\", {\n            original: JSON.stringify(searchFilters, null, 2),\n            toSend: JSON.stringify(filtersToSend, null, 2),\n            areEqual: JSON.stringify(searchFilters) === JSON.stringify(filtersToSend)\n        });\n        console.log(\"\\uD83D\\uDD0D [SEARCH BUTTON DEBUG] ==========================================\");\n        searchLeads(filtersToSend, currentPage) // Use current page, not always page 1\n        ;\n    };\n    // Sidebar state is no longer needed for find-leads as it's always visible\n    // Keep the interface compatible for other components that might use this\n    // Event handlers\n    const handleCreateLead = ()=>{\n        console.log(\"Create lead clicked\");\n    };\n    // handleImportLeads is now provided by shared hook\n    // All handlers are now provided by shared hook\n    // Generate contact links based on lead data\n    // getContactLinks is now provided by shared hook\n    // All handlers including handleViewLinks and handleNameClick are now provided by shared hook\n    // Save current search results as a saved search\n    const handleSaveLeads = ()=>{\n        var _workspace_workspace, _workspace_workspace1;\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS] \\uD83D\\uDE80 User clicked Save Leads button\");\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS] \\uD83D\\uDCCA Current state:\", {\n            hasWorkspace: !!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id),\n            hasToken: !!(token === null || token === void 0 ? void 0 : token.token),\n            hasSearched,\n            leadsCount: leads.length,\n            currentSearchId,\n            currentPage,\n            searchFilters: searchFilters\n        });\n        if (!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace1 = workspace.workspace) === null || _workspace_workspace1 === void 0 ? void 0 : _workspace_workspace1.id) || !(token === null || token === void 0 ? void 0 : token.token)) {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS] ❌ Missing authentication\");\n            toast.error(\"Authentication required\");\n            return;\n        }\n        if (!hasSearched || leads.length === 0) {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS] ❌ No search results to save\");\n            toast.error(\"No search results to save\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS] ✅ Validation passed, opening save dialog\");\n        // Set default name and open dialog\n        const defaultName = \"Search Results - \".concat(new Date().toLocaleDateString());\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS] \\uD83D\\uDCDD Setting default name:\", defaultName);\n        setSearchName(defaultName);\n        setSaveDialogOpen(true);\n    };\n    // Handle the actual save operation\n    const handleConfirmSave = async ()=>{\n        var _workspace_workspace;\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDE80 User confirmed save operation\");\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDCCA Save details:\", {\n            searchName: searchName === null || searchName === void 0 ? void 0 : searchName.trim(),\n            leadsCount: leads.length,\n            currentPage,\n            currentSearchId,\n            hasToken: !!(token === null || token === void 0 ? void 0 : token.token),\n            hasWorkspace: !!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id)\n        });\n        if (!searchName || searchName.trim() === \"\") {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Empty search name\");\n            toast.error(\"Search name cannot be empty\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ✅ Starting save process...\");\n        setIsSavingSearch(true);\n        setSaveDialogOpen(false);\n        try {\n            var _response_data;\n            const saveRequest = {\n                name: searchName.trim(),\n                description: \"Saved search with \".concat(leads.length, \" leads from page \").concat(currentPage),\n                filters: searchFilters,\n                searchId: currentSearchId || undefined,\n                searchType: \"people\"\n            };\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDCE4 Sending save request:\", saveRequest);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD17 API call: saveSearch()\");\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_12__.saveSearch)(token.token, workspace.workspace.id, saveRequest);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDCE5 Received response from backend\");\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D Full response:\", response);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D Response keys:\", Object.keys(response));\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D isSuccess:\", response.isSuccess);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D error:\", response.error);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D data:\", response.data);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D data.data:\", (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data);\n            if (response.isSuccess) {\n                var _response_data_data_savedSearch, _response_data_data, _response_data1;\n                console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ✅ Save operation successful!\");\n                console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDCCB Saved search details:\", {\n                    name: searchName,\n                    leadsCount: leads.length,\n                    searchId: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : (_response_data_data_savedSearch = _response_data_data.savedSearch) === null || _response_data_data_savedSearch === void 0 ? void 0 : _response_data_data_savedSearch.id,\n                    searchType: \"people\"\n                });\n                toast.success('Saved \"'.concat(searchName, '\" with ').concat(leads.length, \" leads\"));\n            } else {\n                console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Save operation failed\");\n                console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Error details:\", {\n                    isSuccess: response.isSuccess,\n                    error: response.error,\n                    status: response.status\n                });\n                toast.error(response.error || \"Failed to save search\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Exception during save operation:\", error);\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Error type:\", typeof error);\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Error message:\", error instanceof Error ? error.message : \"Unknown error\");\n            toast.error(\"Failed to save search. Please try again.\");\n        } finally{\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83C\\uDFC1 Save operation completed, resetting UI state\");\n            setIsSavingSearch(false);\n        }\n    };\n    // Filter and search functionality - simplified to work with real API data\n    // filteredLeads is now provided by lead management hook\n    const filteredLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getFilteredLeads(leads, undefined, undefined)) || leads;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xs font-semibold text-black\",\n                            children: \"Find People\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer\",\n                                onClick: ()=>{\n                                    setLeads(leads.filter((lead)=>!(leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id))));\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setSelectedLeads([]);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.TrashIcon, {\n                                        className: \"size-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    \"Delete \",\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-neutral-600 whitespace-nowrap\",\n                                                children: [\n                                                    \"Page \",\n                                                    currentPage,\n                                                    \" of \",\n                                                    totalPages,\n                                                    totalCount > 0 && \" (\".concat(totalCount, \" total)\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            totalPages > 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handlePageChange(Math.max(1, currentPage - 1)),\n                                                        disabled: currentPage === 1,\n                                                        className: \"h-6 w-6 p-0 text-xs\",\n                                                        children: \"←\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: generatePageNumbers().map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                children: page === \"...\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-1 text-neutral-400 text-xs\",\n                                                                    children: \"...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 57\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: currentPage === page ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handlePageChange(page),\n                                                                    className: \"h-6 w-6 p-0 text-xs \".concat(currentPage === page ? \"bg-primary text-white\" : \"hover:bg-neutral-50\"),\n                                                                    children: page\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 49\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handlePageChange(Math.min(totalPages, currentPage + 1)),\n                                                        disabled: currentPage === totalPages,\n                                                        className: \"h-6 w-6 p-0 text-xs\",\n                                                        children: \"→\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 37\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-neutral-500\",\n                                                children: \"Single page\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"size-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                placeholder: \"Search and find people\",\n                                                // value={searchQuery} // searchQuery is removed\n                                                // onChange={e => setSearchQuery(e.target.value)} // searchQuery is removed\n                                                onKeyDown: (e)=>{\n                                                    if (e.key === \"Enter\") {\n                                                        handleSearch();\n                                                    }\n                                                },\n                                                className: \"text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            isSearching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_8__.Loader, {\n                                                theme: \"dark\",\n                                                className: \"size-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSearch,\n                                        disabled: isSearching,\n                                        size: \"sm\",\n                                        className: \"text-xs rounded-full h-auto px-3 py-1.5\",\n                                        children: isSearching ? \"Searching...\" : \"Search\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    hasSearched && leads.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSaveLeads,\n                                        disabled: isSavingSearch,\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        className: \"text-xs rounded-full h-auto px-3 py-1.5 gap-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.DatabaseIcon, {\n                                                className: \"size-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            isSavingSearch ? \"Saving...\" : \"Save \".concat(leads.length, \" Leads\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                lineNumber: 546,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_workspace_main_lead_generation_filters_people__WEBPACK_IMPORTED_MODULE_9__.PeopleFilter, {\n                        forceSidebar: true,\n                        onFilterChange: handleFilterChange,\n                        excludeMyLeads: excludeMyLeads,\n                        onExcludeMyLeadsChange: (value)=>{\n                            setExcludeMyLeads(value);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 29\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: filteredLeads.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.MagnifyingGlassIcon, {\n                                        className: \"size-12 text-neutral-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-neutral-900 mb-2\",\n                                        children: hasSearched ? \"No people found\" : \"Ready to find people\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-neutral-500 mb-4 text-sm\",\n                                        children: hasSearched ? \"Try adjusting your search query or filters to find more results\" : \"Use the search box above or apply filters on the left to discover people. Click the Search button to start.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                className: \"size-full scrollBlockChild\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_10__.PeopleTableHeader, {\n                                                selectedLeads: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads) || [],\n                                                filteredLeads: filteredLeads,\n                                                handleSelectAll: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectAll) || (()=>{})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                                children: (()=>{\n                                                    return filteredLeads.map((lead)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                            className: \"border-b border-neutral-200 hover:bg-neutral-50 transition-colors group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"w-12 px-3 relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__.Checkbox, {\n                                                                        checked: leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id),\n                                                                        onCheckedChange: (checked)=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectLead(lead.id, checked),\n                                                                        className: \"\".concat((leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id)) ? \"opacity-100 visible\" : \"invisible opacity-0 group-hover:opacity-100 group-hover:visible\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 724,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0\",\n                                                                        onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleNameClick(lead),\n                                                                        children: lead.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1 text-xs text-muted-foreground\",\n                                                                    children: lead.jobTitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1 text-xs text-muted-foreground\",\n                                                                    children: lead.company\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1\",\n                                                                    children: lead.email && lead.email !== \"unlock\" ? // Show unlocked email\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                                        title: lead.email,\n                                                                        children: lead.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 743,\n                                                                        columnNumber: 49\n                                                                    }, undefined) : // Show unlock button\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                                        icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.EnvelopeIcon,\n                                                                        onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockEmail(lead.id),\n                                                                        children: \"Unlock Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 740,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1\",\n                                                                    children: lead.phone && lead.phone !== \"unlock\" ? // Show unlocked phone\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                                        title: lead.phone,\n                                                                        children: lead.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 49\n                                                                    }, undefined) : // Show unlock button\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                                        icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.PhoneIcon,\n                                                                        onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockPhone(lead.id),\n                                                                        children: \"Unlock Mobile\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 764,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 756,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewLinksModal, {\n                                                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0\",\n                                                                            children: [\n                                                                                \"View links\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.ChevronRightIcon, {\n                                                                                    className: \"size-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                                    lineNumber: 777,\n                                                                                    columnNumber: 57\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 53\n                                                                        }, void 0),\n                                                                        links: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getContactLinks(lead)) || []\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"w-12 px-2 relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeadActionsDropdown, {\n                                                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.EllipsisVerticalIcon, {\n                                                                                className: \"size-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                                lineNumber: 791,\n                                                                                columnNumber: 57\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 53\n                                                                        }, void 0),\n                                                                        leadData: lead,\n                                                                        onSendEmail: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleSendEmail(lead.id, lead),\n                                                                        onAddToSegments: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToSegments(lead.id),\n                                                                        onAddToDatabase: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToDatabase(lead.id, lead),\n                                                                        onAddToWorkflow: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToWorkflow(lead.id)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            ]\n                                                        }, lead.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                            lineNumber: 722,\n                                                            columnNumber: 37\n                                                        }, undefined));\n                                                })()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b border-neutral-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                lineNumber: 679,\n                columnNumber: 13\n            }, undefined),\n            (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.Dialog, {\n                open: saveDialogOpen,\n                onOpenChange: setSaveDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.DialogContent, {\n                    className: \"max-w-[95vw] sm:max-w-[600px] !rounded-none p-4\",\n                    \"aria-describedby\": \"save-search-description\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.DialogTitle, {\n                                children: \"Save Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    id: \"save-search-description\",\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: \"Enter a name for this saved search:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: searchName,\n                                    onChange: (e)=>setSearchName(e.target.value),\n                                    placeholder: \"Enter search name\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === \"Enter\") {\n                                            handleConfirmSave();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: ()=>setSaveDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: handleConfirmSave,\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                    lineNumber: 825,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                lineNumber: 824,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(FindLeads, \"zwt0g14gGZIrDzOVWqIYR9d0VQQ=\", false, function() {\n    return [\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__.useParams,\n        _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_13__.useWorkspace,\n        _ui_providers_user__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        _ui_providers_alert__WEBPACK_IMPORTED_MODULE_15__.useAlert\n    ];\n});\n_c = FindLeads;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FindLeads);\nvar _c;\n$RefreshReg$(_c, \"FindLeads\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/people/find-leads.tsx\n"));

/***/ })

});