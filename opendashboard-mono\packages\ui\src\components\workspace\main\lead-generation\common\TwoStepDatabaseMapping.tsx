"use client"

import React, { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@ui/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@ui/components/ui/dialog"
import { DatabaseSelect } from "@ui/components/workspace/main/common/databaseSelect"
import { EnhancedUpdateRecordEditor } from "./EnhancedUpdateRecordEditor"
import { LeadDataSubstitutionProvider } from "./LeadDataSubstitutionProvider"
import { Lead } from "@ui/typings/lead"
import { DatabaseIcon, ArrowLeftIcon, ArrowRightIcon } from "@ui/components/icons/FontAwesomeRegular"
import { Loader } from "@ui/components/custom-ui/loader"

interface Update {
  columnId: string
  value: string
}

interface TwoStepDatabaseMappingProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  lead: Lead
  onConfirm: (mappings: Update[], databaseId: string) => Promise<void>
  loading?: boolean
}

export const TwoStepDatabaseMapping = ({
  open,
  onOpenChange,
  lead,
  onConfirm,
  loading = false
}: TwoStepDatabaseMappingProps) => {
  
  const [step, setStep] = useState<1 | 2>(1)
  const [selectedDatabaseId, setSelectedDatabaseId] = useState<string>('')
  const [mappings, setMappings] = useState<Update[]>([])
  const [processing, setProcessing] = useState(false)

  const handleNext = () => {
    if (step === 1 && selectedDatabaseId) {
      setStep(2)
    }
  }

  const handleBack = () => {
    if (step === 2) {
      setStep(1)
    }
  }

  const handleConfirm = async () => {
    if (!selectedDatabaseId || mappings.length === 0) return
    
    setProcessing(true)
    try {
      await onConfirm(mappings, selectedDatabaseId)
      // Reset state on success
      setStep(1)
      setSelectedDatabaseId('')
      setMappings([])
      onOpenChange(false)
    } catch (error) {
      console.error('Error confirming mappings:', error)
    } finally {
      setProcessing(false)
    }
  }

  const handleClose = () => {
    // Reset state when closing
    setStep(1)
    setSelectedDatabaseId('')
    setMappings([])
    onOpenChange(false)
  }

  const canProceed = useMemo(() => {
    if (step === 1) return selectedDatabaseId !== ''
    if (step === 2) return mappings.length > 0 && mappings.every(m => m.columnId && m.value)
    return false
  }, [step, selectedDatabaseId, mappings])

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DatabaseIcon className="w-5 h-5" />
            Add Lead to Database
          </DialogTitle>
          <DialogDescription>
            Map this lead's data to database columns
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <LeadDataSubstitutionProvider lead={lead}>
            {step === 1 ? (
              <div className="space-y-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <DatabaseIcon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Step 1: Select Database</h3>
                  <p className="text-sm text-muted-foreground">
                    Choose the database where you want to add this lead
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Select Database
                    </label>
                    <DatabaseSelect
                      selectedId={selectedDatabaseId}
                      onChange={setSelectedDatabaseId}
                      className="w-full"
                    />
                  </div>

                  {selectedDatabaseId && (
                    <div className="p-4 bg-muted rounded-lg">
                      <h4 className="text-sm font-medium mb-2">Lead Data Preview</h4>
                      <div className="text-xs text-muted-foreground space-y-1">
                        {lead.type === 'person' ? (
                          <>
                            {lead.name && <div><strong>Name:</strong> {lead.name}</div>}
                            {lead.normalizedData?.jobTitle && <div><strong>Job Title:</strong> {lead.normalizedData.jobTitle}</div>}
                            {lead.normalizedData?.company && <div><strong>Company:</strong> {lead.normalizedData.company}</div>}
                            {lead.normalizedData?.location && <div><strong>Location:</strong> {
                              [lead.normalizedData.location.city, lead.normalizedData.location.state, lead.normalizedData.location.country]
                                .filter(Boolean).join(', ')
                            }</div>}
                            {lead.normalizedData?.email && <div><strong>Email:</strong> {lead.normalizedData.email}</div>}
                            {lead.normalizedData?.phone && <div><strong>Phone:</strong> {lead.normalizedData.phone}</div>}
                          </>
                        ) : (
                          <>
                            {lead.name && <div><strong>Name:</strong> {lead.name}</div>}
                            {(lead.apolloData as any)?.industry && <div><strong>Industry:</strong> {(lead.apolloData as any).industry}</div>}
                            {lead.normalizedData?.location && <div><strong>Location:</strong> {
                              [lead.normalizedData.location.city, lead.normalizedData.location.state, lead.normalizedData.location.country]
                                .filter(Boolean).join(', ')
                            }</div>}
                            {(lead.normalizedData?.email || lead.email) && <div><strong>Email:</strong> {lead.normalizedData?.email || lead.email}</div>}
                            {(lead.normalizedData?.phone || (lead.apolloData as any)?.phone) && <div><strong>Phone:</strong> {lead.normalizedData?.phone || (lead.apolloData as any).phone}</div>}
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <DatabaseIcon className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Step 2: Map Fields</h3>
                  <p className="text-sm text-muted-foreground">
                    Map this lead's fields to database columns
                  </p>
                </div>

                <EnhancedUpdateRecordEditor
                  databaseId={selectedDatabaseId}
                  updates={mappings}
                  onUpdate={setMappings}
                />
              </div>
            )}
          </LeadDataSubstitutionProvider>
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            {step === 2 && (
              <Button variant="outline" onClick={handleBack} disabled={processing}>
                <ArrowLeftIcon className="w-4 h-4 mr-1" />
                Back
              </Button>
            )}
            <Button variant="outline" onClick={handleClose} disabled={processing}>
              Cancel
            </Button>
          </div>
          
          <div className="flex gap-2">
            {step === 1 ? (
              <Button onClick={handleNext} disabled={!canProceed || processing}>
                Next
                <ArrowRightIcon className="w-4 h-4 ml-1" />
              </Button>
            ) : (
              <Button 
                onClick={handleConfirm} 
                disabled={!canProceed || processing}
                className="gap-2"
              >
                {processing ? (
                  <>
                    <Loader theme="dark" className="size-4" />
                    Adding to Database...
                  </>
                ) : (
                  <>
                    <DatabaseIcon className="w-4 h-4" />
                    Add to Database
                  </>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
