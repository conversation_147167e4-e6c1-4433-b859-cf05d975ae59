"use client"

import React, { createContext, useContext, useMemo } from "react"
import { Lead, ApolloPersonData, ApolloCompanyData } from "@ui/typings/lead"

interface LeadDataSubstitutionProviderProps {
  lead: Lead
  children: React.ReactNode
}

interface LeadSubstitutionContextType {
  lead: Lead
  leadData: Record<string, any>
  getAvailableFields: () => Array<{ key: string; label: string; value: any }>
}

const LeadSubstitutionContext = createContext<LeadSubstitutionContextType | null>(null)

export const LeadDataSubstitutionProvider = ({ lead, children }: LeadDataSubstitutionProviderProps) => {

  const leadData = useMemo(() => {
    // Extract all available lead data dynamically using any type
    const apolloData = lead.apolloData as any
    const normalizedData = lead.normalizedData as any
    
    // Start with basic lead properties
    const data: any = {
      'name': lead.name || '',
      'email': lead.email || '',
      'companyDomain': lead.companyDomain || '',
      'source': lead.source || '',
      'createdAt': lead.createdAt instanceof Date ? lead.createdAt.toISOString() : (typeof lead.createdAt === 'string' ? lead.createdAt : ''),
      'lastEnrichedAt': lead.lastEnrichedAt instanceof Date ? lead.lastEnrichedAt.toISOString() : (typeof lead.lastEnrichedAt === 'string' ? lead.lastEnrichedAt : ''),
    }

    // Add normalized data if available
    if (normalizedData) {
      Object.keys(normalizedData).forEach(key => {
        if (normalizedData[key] !== null && normalizedData[key] !== undefined && normalizedData[key] !== '') {
          data[key] = normalizedData[key]
        }
      })
    }

    // Add Apollo data if available
    if (apolloData) {
      Object.keys(apolloData).forEach(key => {
        if (apolloData[key] !== null && apolloData[key] !== undefined && apolloData[key] !== '') {
          data[key] = apolloData[key]
        }
      })
    }

    return data
  }, [lead])

  const getAvailableFields = (): Array<{ key: string; label: string; value: any }> => {
    // Simple function to convert camelCase to readable labels
    const formatLabel = (key: string): string => {
      return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')
    }

    const availableFields = Object.entries(leadData)
      .filter(([_, value]) => value !== '' && value !== null && value !== undefined && value !== 'N/A')
      .map(([key, value]) => ({
        key,
        label: formatLabel(key),
        value
      }))
    
    return availableFields
  }

  const contextValue: LeadSubstitutionContextType = {
    lead,
    leadData,
    getAvailableFields
  }

  return (
    <LeadSubstitutionContext.Provider value={contextValue}>
      {children}
    </LeadSubstitutionContext.Provider>
  )
}

export const useLeadSubstitution = (): LeadSubstitutionContextType => {
  const context = useContext(LeadSubstitutionContext)
  if (!context) {
    throw new Error('useLeadSubstitution must be used within a LeadDataSubstitutionProvider')
  }
  return context
}
