"use client"

import React, { useState, useRef, useMemo } from "react"
import MyLeads from "./my-leads"
import FindLeads from "./find-leads"
import SavedSearch from "./saved-search"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { But<PERSON> } from "@ui/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { EnvelopeIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon } from "@ui/components/icons/FontAwesomeRegular"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@ui/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Input } from "@ui/components/ui/input"
import { Textarea } from "@ui/components/ui/textarea"
import { Label } from "@ui/components/ui/label"
import { addLeadToDatabase, addLeadToSegment, addLeadToWorkflow, sendEmailToLead } from "@ui/api/leads"
import { getDatabases } from "@ui/api/database"

import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { DatabaseSelect } from "@ui/components/workspace/main/common/databaseSelect"
import { OnDemandWorkflowSelect } from "@ui/components/workspace/main/common/onDemandWorkflowSelect"
import { TwoStepDatabaseMapping } from "../common/TwoStepDatabaseMapping"
import { Loader } from "@ui/components/custom-ui/loader"

interface PeopleProps {
    activeSubTab: 'my-leads' | 'find-leads' | 'saved-search'
    onLeadCreated?: (lead: any) => void
}


export const ActionButton = ({ 
    icon: Icon, 
    children, 
    onClick 
}: { 
    icon: React.ComponentType<{ className?: string }>; 
    children: React.ReactNode; 
    onClick: () => void; 
}) => (
    <Button
        variant="outline"
        size="sm"
        onClick={onClick}
        className="text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center"
    >
        <Icon className="size-3" />
        <span className="truncate">{children}</span>
    </Button>
)

export const ViewLinksModal = ({ 
    trigger,
    links 
}: { 
    trigger: React.ReactNode;
    links: Array<{ id: string; title: string; url: string }> 
}) => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
                {trigger}
            </PopoverTrigger>
            <PopoverContent className="w-64 p-4" align="end">
                <h4 className="font-semibold text-sm mb-2">Social Links</h4>
                <div className="space-y-2">
                    {links.length === 0 ? (
                        <div className="text-xs text-muted-foreground">No links available</div>
                    ) : (
                        links.map((link) => (
                            <a
                                key={link.id}
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors p-2 rounded hover:bg-blue-50"
                            >
                                <UpRightFromSquareIcon className="size-3" />
                                <span className="truncate">{link.title}</span>
                            </a>
                        ))
                    )}
                </div>
            </PopoverContent>
        </Popover>
    )
}

interface LeadActionsDropdownProps {
    trigger: React.ReactNode
    onSendEmail?: () => void
    onAddToSegments?: () => void
    onAddToDatabase?: () => void
    onAddToWorkflow?: () => void
    leadData?: any
}

export const LeadActionsDropdown = ({ 
    trigger, 
    onSendEmail, 
    onAddToSegments, 
    onAddToDatabase, 
    onAddToWorkflow,
    leadData 
}: LeadActionsDropdownProps) => {

    const email = leadData?.normalizedData?.email || leadData?.apolloData?.email || leadData?.email
    const hasEmail = !!email
    
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                {trigger}
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuGroup>
                    <DropdownMenuItem 
                        onClick={hasEmail ? onSendEmail : undefined} 
                        className={hasEmail ? "cursor-pointer" : "cursor-not-allowed opacity-50"}
                        disabled={!hasEmail}
                    >
                        <EnvelopeIcon className="mr-2 h-4 w-4" />
                        <span>Send Email</span>
                        {!hasEmail && <span className="ml-auto text-xs text-gray-400">(Locked)</span>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={onAddToSegments} className="cursor-pointer">
                        <ChartLineIcon className="mr-2 h-4 w-4" />
                        <span>Add to Segments</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={onAddToDatabase} className="cursor-pointer">
                        <DatabaseIcon className="mr-2 h-4 w-4" />
                        <span>Add to Database</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={onAddToWorkflow} className="cursor-pointer">
                        <CodeMergeIcon className="mr-2 h-4 w-4" />
                        <span>Add to Workflow</span>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}


export const PeopleTableHeader = ({ 
    selectedLeads, 
    filteredLeads, 
    handleSelectAll 
}: { 
    selectedLeads: string[]
    filteredLeads: any[]
    handleSelectAll: (checked: boolean, leads: any[]) => void
}) => (
    <TableHeader>
        <TableRow className="border-b border-neutral-200 bg-white sticky top-0 z-10">
            <TableHead className="w-12 h-10 px-3">
                <Checkbox
                    checked={selectedLeads.length === filteredLeads.length && filteredLeads.length > 0}
                    onCheckedChange={(checked: boolean) => handleSelectAll(checked, filteredLeads)}
                />
            </TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Name</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Job title</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Company</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Location</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Email</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Phone number</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Social Links</TableHead>
            <TableHead className="w-12 h-10 px-1"></TableHead>
        </TableRow>
    </TableHeader>
)


export const useLeadManagement = () => {
    const { toast } = useAlert()
    const { token } = useAuth()
    const { workspace } = useWorkspace()
    const router = useRouter()
    const params = useParams()
    

    const [addToDatabaseDialogOpen, setAddToDatabaseDialogOpen] = useState(false)
    const [selectedLeadForDatabase, setSelectedLeadForDatabase] = useState<any>(null)
    const [addingToDatabase, setAddingToDatabase] = useState(false)
    const [selectedLeadIdForAction, setSelectedLeadIdForAction] = useState('')
    

    const [sendEmailDialogOpen, setSendEmailDialogOpen] = useState(false)
    const [emailSubject, setEmailSubject] = useState('')
    const [emailBody, setEmailBody] = useState('')
    const [sendingEmail, setSendingEmail] = useState(false)
    const [selectedLeadForEmail, setSelectedLeadForEmail] = useState<any>(null)


    const [addToSegmentDialogOpen, setAddToSegmentDialogOpen] = useState(false)
    const [segmentName, setSegmentName] = useState('')
    const [addingToSegment, setAddingToSegment] = useState(false)


    const [addToWorkflowDialogOpen, setAddToWorkflowDialogOpen] = useState(false)
    const [selectedWorkflowId, setSelectedWorkflowId] = useState('')
    const [addingToWorkflow, setAddingToWorkflow] = useState(false)


    const [emailModalOpen, setEmailModalOpen] = useState(false)
    const [phoneModalOpen, setPhoneModalOpen] = useState(false)
    const [selectedLeadId, setSelectedLeadId] = useState('')
    const unlockEmailCallbackRef = useRef<(() => void) | null>(null)
    const unlockPhoneCallbackRef = useRef<(() => void) | null>(null)


    const [selectedLeads, setSelectedLeads] = useState<string[]>([])


    const handleSelectAll = (checked: boolean, leads: any[]) => {
        if (checked) {
            setSelectedLeads(leads.map(lead => lead.id))
        } else {
            setSelectedLeads([])
        }
    }

    const handleSelectLead = (leadId: string, checked: boolean) => {
        if (checked) {
            setSelectedLeads([...selectedLeads, leadId])
        } else {
            setSelectedLeads(selectedLeads.filter(id => id !== leadId))
        }
    }


    const handleNameClick = (lead: any) => {
        const domain = params.domain
        router.push(`/${domain}/lead-generation/people/details/${lead.id}`)
    }

    const handleAddToDatabase = async (leadId: string, leadData?: any) => {
        const lead = leadData || { id: leadId, name: 'Unknown Lead' }
        setSelectedLeadForDatabase(lead)
        setAddToDatabaseDialogOpen(true)
    }

    const handleConfirmAddToDatabase = async (mappings: any[], databaseId: string) => {
        if (!selectedLeadForDatabase || !databaseId || mappings.length === 0) return
        
        setAddingToDatabase(true)
        try {
            const response = await addLeadToDatabase(
                token?.token || '', 
                workspace?.workspace?.id || '', 
                selectedLeadForDatabase.id, 
                { 
                    targetDatabaseId: databaseId
                }
            )
            
            if (response.isSuccess) {
                toast.success("Lead added to database successfully!")
                setAddToDatabaseDialogOpen(false)
                setSelectedLeadForDatabase(null)
            } else {
                toast.error(response.error || "Failed to add lead to database")
            }
        } catch (error) {
            toast.error("Failed to add lead to database")
        } finally {
            setAddingToDatabase(false)
        }
    }

    const handleSendEmail = async (leadId: string, leadData?: any) => {
        const lead = leadData || { id: leadId, name: 'Unknown Lead' }

        const email = lead.normalizedData?.email || lead.apolloData?.email || lead.email
        const isEmailVisible = lead.normalizedData?.isEmailVisible || lead.apolloData?.normalizedData?.isEmailVisible

        if (!email || !isEmailVisible) {
            toast.error("You have to unlock the email first before sending an email.")
            return
        }

        setSelectedLeadForEmail({ ...lead, email })
        setEmailSubject('')
        setEmailBody('')
        setSendEmailDialogOpen(true)
    }

    const handleConfirmSendEmail = async () => {
        if (!selectedLeadForEmail || !emailSubject.trim() || !emailBody.trim()) {
            toast.error("Please fill in both subject and body")
            return
        }

        setSendingEmail(true)
        try {
            const response = await sendEmailToLead(token?.token || '', workspace?.workspace?.id || '', selectedLeadForEmail.id, {
                subject: emailSubject.trim(),
                body: emailBody.trim()
            })
            

            if (response.isSuccess) {
                toast.success("Email sent successfully!")
                setSendEmailDialogOpen(false)
                setEmailSubject('')
                setEmailBody('')
                setSelectedLeadForEmail(null)
            } else {
                toast.error(response.error || "Failed to send email")
            }
        } catch (error) {
            toast.error("Failed to send email")
        } finally {
            setSendingEmail(false)
        }
    }

    const handleAddToSegments = async (leadId: string) => {
        setSelectedLeadIdForAction(leadId)
        setSegmentName('')
        setAddToSegmentDialogOpen(true)
    }

    const handleConfirmAddToSegment = async () => {
        if (!selectedLeadIdForAction || !segmentName.trim()) {
            toast.error("Please enter a segment name")
            return
        }

        setAddingToSegment(true)
        try {
            await addLeadToSegment(token?.token || '', workspace?.workspace?.id || '', selectedLeadIdForAction, {
                name: segmentName.trim()
            })
            toast.success("Lead added to segment successfully!")
            setAddToSegmentDialogOpen(false)
            setSegmentName('')
            setSelectedLeadIdForAction('')
        } catch (error) {
            toast.error("Failed to add to segment")
        } finally {
            setAddingToSegment(false)
        }
    }

    const handleAddToWorkflow = async (leadId: string) => {
        setSelectedLeadIdForAction(leadId)
        setAddToWorkflowDialogOpen(true)
    }

    const handleConfirmAddToWorkflow = async () => {
        if (!selectedLeadIdForAction || !selectedWorkflowId) {
            toast.error("Please select a workflow")
            return
        }

        setAddingToWorkflow(true)
        try {
            const result = await addLeadToWorkflow(token?.token || '', workspace?.workspace?.id || '', selectedLeadIdForAction, {
                workflowId: selectedWorkflowId
            })

            if (result.isSuccess) {
                toast.success("Lead added to workflow successfully!")
                setAddToWorkflowDialogOpen(false)
                setSelectedWorkflowId('')
                setSelectedLeadIdForAction('')
            } else {
                toast.error(result.error || "Failed to add lead to workflow")
            }
        } catch (error) {
            toast.error("Failed to add to workflow")
        } finally {
            setAddingToWorkflow(false)
        }
    }

    const handleUnlockEmail = async (leadId: string) => {
        setSelectedLeadId(leadId)
        setEmailModalOpen(true)
        if (unlockEmailCallbackRef.current) {
            unlockEmailCallbackRef.current()
        }
    }

    const handleUnlockPhone = async (leadId: string) => {
        setSelectedLeadId(leadId)
        setPhoneModalOpen(true)
        if (unlockPhoneCallbackRef.current) {
            unlockPhoneCallbackRef.current()
        }
    }


    const convertApiLeadsToUI = (apiLeads: any[]): any[] => {
        return apiLeads.map((apiLead) => ({
            id: apiLead.id,
            name: apiLead.normalizedData?.name || apiLead.apolloData?.name || 'Unknown',
            jobTitle: apiLead.normalizedData?.jobTitle || apiLead.apolloData?.jobTitle || '',
            company: apiLead.normalizedData?.company || apiLead.apolloData?.company || '',
            email: apiLead.normalizedData?.isEmailVisible ? apiLead.normalizedData?.email || "unlock" : "unlock",
            phone: apiLead.normalizedData?.isPhoneVisible ? apiLead.normalizedData?.phone || "unlock" : "unlock",
            links: "view"
        }))
    }


    const getFilteredLeads = (leads: any[], searchQuery: string, filter: any) => {
        let filtered = leads


        if (searchQuery) {
            filtered = filtered.filter(lead => 
                lead.normalizedData?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                lead.normalizedData?.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                lead.normalizedData?.company?.toLowerCase().includes(searchQuery.toLowerCase())
            )
        }


        if (filter?.conditions?.length > 0) {
            filtered = filtered.filter(lead => {
                return filter.conditions.every((condition: any) => {
                    const value = condition.value?.toString().toLowerCase() || ''
                    const leadValue = lead[condition.columnId as keyof any]?.toString().toLowerCase() || ''
                    return leadValue.includes(value)
                })
            })
        }

        return filtered
    }


    const getContactLinks = (lead: any) => {
        const links = []
        
        if (lead.normalizedData?.linkedinUrl) {
            links.push({
                id: 'linkedin',
                title: 'LinkedIn',
                url: lead.normalizedData.linkedinUrl
            })
        }
        
        if (lead.normalizedData?.email && lead.normalizedData?.isEmailVisible) {
            links.push({
                id: 'email',
                title: 'Email',
                url: `mailto:${lead.normalizedData.email}`
            })
        }
        
        if (lead.normalizedData?.phone && lead.normalizedData?.isPhoneVisible) {
            links.push({
                id: 'phone',
                title: 'Phone',
                url: `tel:${lead.normalizedData.phone}`
            })
        }
        
        return links
    }


    const leadActions = {
        handleSendEmail,
        handleAddToSegments,
        handleAddToDatabase,
        handleAddToWorkflow,
        handleUnlockEmail,
        handleUnlockPhone
    }



    return {
        addToDatabaseDialogOpen,
        setAddToDatabaseDialogOpen,
        selectedLeadForDatabase,
        setSelectedLeadForDatabase,
        addingToDatabase,
        setAddingToDatabase,
        selectedLeadIdForAction,
        setSelectedLeadIdForAction,
        emailModalOpen,
        setEmailModalOpen,
        phoneModalOpen,
        setPhoneModalOpen,
        selectedLeadId,
        setSelectedLeadId,
        unlockEmailCallbackRef,
        unlockPhoneCallbackRef,
        selectedLeads,
        setSelectedLeads,
        handleSelectAll,
        handleSelectLead,
        handleNameClick,
        leadActions,
        SharedModals,
        handleAddToDatabase,
        handleConfirmAddToDatabase,
        handleSendEmail,
        handleConfirmSendEmail,
        handleAddToSegments,
        handleConfirmAddToSegment,
        sendEmailDialogOpen,
        setSendEmailDialogOpen,
        emailSubject,
        setEmailSubject,
        emailBody,
        setEmailBody,
        sendingEmail,
        setSendingEmail,
        selectedLeadForEmail,
        setSelectedLeadForEmail,
        addToSegmentDialogOpen,
        setAddToSegmentDialogOpen,
        segmentName,
        setSegmentName,
        addingToSegment,
        setAddingToSegment,
        addToWorkflowDialogOpen,
        setAddToWorkflowDialogOpen,
        selectedWorkflowId,
        setSelectedWorkflowId,
        addingToWorkflow,
        setAddingToWorkflow,
        handleAddToWorkflow,
        handleConfirmAddToWorkflow,
        handleUnlockEmail,
        handleUnlockPhone,
        convertApiLeadsToUI,
        getFilteredLeads,
        getContactLinks
    }
    }



const AddToSegmentDialog = ({
    open,
    onOpenChange,
    segmentName,
    setSegmentName,
    addingToSegment,
    handleConfirmAddToSegment
}: {
    open: boolean
    onOpenChange: (open: boolean) => void
    segmentName: string
    setSegmentName: (name: string) => void
    addingToSegment: boolean
    handleConfirmAddToSegment: () => void
}) => {
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Add Lead to Segment</DialogTitle>
                    <DialogDescription>
                        Enter a name for the segment to add this lead to.
                    </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                    <div className="space-y-2">
                        <Label htmlFor="segment-name">Segment Name</Label>
                        <Input
                            id="segment-name"
                            value={segmentName}
                            onChange={(e) => setSegmentName(e.target.value)}
                            placeholder="e.g., High Priority Leads, Q1 Prospects"
                            disabled={addingToSegment}
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        disabled={addingToSegment}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmAddToSegment}
                        disabled={!segmentName.trim() || addingToSegment}
                        className="gap-2"
                    >
                        {addingToSegment ? (
                            <>
                                <Loader theme="dark" className="size-4" />
                                Adding...
                            </>
                        ) : (
                            <>
                                <ChartLineIcon className="size-4" />
                                Add to Segment
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
    }

    const AddToWorkflowDialog = ({
        open,
        onOpenChange,
        selectedWorkflowId,
        setSelectedWorkflowId,
        addingToWorkflow,
        handleConfirmAddToWorkflow
    }: {
        open: boolean
        onOpenChange: (open: boolean) => void
        selectedWorkflowId: string
        setSelectedWorkflowId: (id: string) => void
        addingToWorkflow: boolean
        handleConfirmAddToWorkflow: () => void
    }) => {
        return (
            <Dialog open={open} onOpenChange={onOpenChange}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Add Lead to Workflow</DialogTitle>
                        <DialogDescription>
                            Select a workflow to add this lead to.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <div className="space-y-4">
                            <div>
                                <label className="text-sm font-medium">Select Workflow:</label>
                                <OnDemandWorkflowSelect
                                    selectedId={selectedWorkflowId}
                                    onChange={(id) => setSelectedWorkflowId(id)}
                                    className="mt-1"
                                />
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                            disabled={addingToWorkflow}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirmAddToWorkflow}
                            disabled={!selectedWorkflowId || addingToWorkflow}
                            className="gap-2"
                        >
                            {addingToWorkflow ? (
                                <>
                                    <Loader theme="dark" className="size-4" />
                                    Adding...
                                </>
                            ) : (
                                <>
                                    <CodeMergeIcon className="size-4" />
                                    Add to Workflow
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        )
    }


const SendEmailDialog = ({
    open,
    onOpenChange,
    selectedLeadForEmail,
    emailSubject,
    setEmailSubject,
    emailBody,
    setEmailBody,
    sendingEmail,
    handleConfirmSendEmail
}: {
    open: boolean
    onOpenChange: (open: boolean) => void
    selectedLeadForEmail: any
    emailSubject: string
    setEmailSubject: (subject: string) => void
    emailBody: string
    setEmailBody: (body: string) => void
    sendingEmail: boolean
    handleConfirmSendEmail: () => void
}) => {
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>Send Email to Lead</DialogTitle>
                </DialogHeader>
                <div className="py-4 space-y-4">
                    {selectedLeadForEmail && (
                        <div className="p-3 bg-gray-50 rounded-md">
                            <p className="text-sm text-gray-600">
                                <span className="font-medium">To:</span> {selectedLeadForEmail.name}
                            </p>
                            {selectedLeadForEmail.email && (
                                <p className="text-sm text-gray-600">
                                    <span className="font-medium">Email:</span> {selectedLeadForEmail.email}
                                </p>
                            )}
                        </div>
                    )}
                    
                    <div className="space-y-2">
                        <Label htmlFor="email-subject">Subject</Label>
                        <Input
                            id="email-subject"
                            value={emailSubject}
                            onChange={(e) => setEmailSubject(e.target.value)}
                            placeholder="Enter email subject"
                            disabled={sendingEmail}
                        />
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="email-body">Message</Label>
                        <Textarea
                            id="email-body"
                            value={emailBody}
                            onChange={(e) => setEmailBody(e.target.value)}
                            placeholder="Enter your message here..."
                            rows={8}
                            disabled={sendingEmail}
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        disabled={sendingEmail}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmSendEmail}
                        disabled={!emailSubject.trim() || !emailBody.trim() || sendingEmail}
                    >
                        {sendingEmail ? "Sending..." : "Send Email"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
        )
    }


const SharedModals = ({ 
    emailModalOpen,
    setEmailModalOpen,
    phoneModalOpen,
    setPhoneModalOpen,
    selectedLeadId,
    unlockEmailCallbackRef,
    unlockPhoneCallbackRef,
    addToDatabaseDialogOpen,
    setAddToDatabaseDialogOpen,
    selectedLeadForDatabase,
    setSelectedLeadForDatabase,
    addingToDatabase,
    handleConfirmAddToDatabase,
    selectedLeadIdForAction,
    setSelectedLeadIdForAction,
    sendEmailDialogOpen,
    setSendEmailDialogOpen,
    selectedLeadForEmail,
    emailSubject,
    setEmailSubject,
    emailBody,
    setEmailBody,
    sendingEmail,
    handleConfirmSendEmail,
    addToSegmentDialogOpen,
    setAddToSegmentDialogOpen,
    segmentName,
    setSegmentName,
    addingToSegment,
    handleConfirmAddToSegment,
    addToWorkflowDialogOpen,
    setAddToWorkflowDialogOpen,
    selectedWorkflowId,
    setSelectedWorkflowId,
    addingToWorkflow,
    handleConfirmAddToWorkflow
}: {
    emailModalOpen: boolean
    setEmailModalOpen: (open: boolean) => void
    phoneModalOpen: boolean
    setPhoneModalOpen: (open: boolean) => void
    selectedLeadId: string | null
    unlockEmailCallbackRef: React.MutableRefObject<(() => void) | null>
    unlockPhoneCallbackRef: React.MutableRefObject<(() => void) | null>
    addToDatabaseDialogOpen: boolean
    setAddToDatabaseDialogOpen: (open: boolean) => void
    selectedLeadForDatabase: any
    setSelectedLeadForDatabase: (lead: any) => void
    addingToDatabase: boolean
    handleConfirmAddToDatabase: (mappings: any[], databaseId: string) => Promise<void>
    selectedLeadIdForAction: string
    setSelectedLeadIdForAction: (id: string) => void
    sendEmailDialogOpen: boolean
    setSendEmailDialogOpen: (open: boolean) => void
    selectedLeadForEmail: any
    emailSubject: string
    setEmailSubject: (subject: string) => void
    emailBody: string
    setEmailBody: (body: string) => void
    sendingEmail: boolean
    handleConfirmSendEmail: () => void
    addToSegmentDialogOpen: boolean
    setAddToSegmentDialogOpen: (open: boolean) => void
    segmentName: string
    setSegmentName: (name: string) => void
    addingToSegment: boolean
    handleConfirmAddToSegment: () => void
    addToWorkflowDialogOpen: boolean
    setAddToWorkflowDialogOpen: (open: boolean) => void
    selectedWorkflowId: string
    setSelectedWorkflowId: (id: string) => void
    addingToWorkflow: boolean
    handleConfirmAddToWorkflow: () => void
}) => (
    <>
        <UnlockEmailModal
            open={emailModalOpen}
            onOpenChange={setEmailModalOpen}
            leadId={selectedLeadId}
        onUnlockSuccess={() => {
            unlockEmailCallbackRef.current?.()
            }}
        />
        <UnlockPhoneModal
            open={phoneModalOpen}
            onOpenChange={setPhoneModalOpen}
            leadId={selectedLeadId}
        onUnlockSuccess={() => {
            unlockPhoneCallbackRef.current?.()
        }}
    />
    {selectedLeadForDatabase && (
        <TwoStepDatabaseMapping
            open={addToDatabaseDialogOpen}
            onOpenChange={setAddToDatabaseDialogOpen}
            lead={selectedLeadForDatabase}
            onConfirm={handleConfirmAddToDatabase}
            loading={addingToDatabase}
        />
    )}
    <AddToSegmentDialog
        open={addToSegmentDialogOpen}
        onOpenChange={setAddToSegmentDialogOpen}
        segmentName={segmentName}
        setSegmentName={setSegmentName}
        addingToSegment={addingToSegment}
        handleConfirmAddToSegment={handleConfirmAddToSegment}
    />
    <AddToWorkflowDialog
        open={addToWorkflowDialogOpen}
        onOpenChange={setAddToWorkflowDialogOpen}
        selectedWorkflowId={selectedWorkflowId}
        setSelectedWorkflowId={setSelectedWorkflowId}
        addingToWorkflow={addingToWorkflow}
        handleConfirmAddToWorkflow={handleConfirmAddToWorkflow}
    />
    <SendEmailDialog
        open={sendEmailDialogOpen}
        onOpenChange={setSendEmailDialogOpen}
        selectedLeadForEmail={selectedLeadForEmail}
        emailSubject={emailSubject}
        setEmailSubject={setEmailSubject}
        emailBody={emailBody}
        setEmailBody={setEmailBody}
        sendingEmail={sendingEmail}
        handleConfirmSendEmail={handleConfirmSendEmail}
    />
</>
)


const People = ({ activeSubTab, onLeadCreated }: PeopleProps) => {
    const leadManagement = useLeadManagement()
    const { 
        leadActions,
        emailModalOpen,
        setEmailModalOpen,
        phoneModalOpen,
        setPhoneModalOpen,
        selectedLeadId,
        unlockEmailCallbackRef,
        unlockPhoneCallbackRef,
        addToDatabaseDialogOpen,
        setAddToDatabaseDialogOpen,
        selectedLeadForDatabase,
        setSelectedLeadForDatabase,
        addingToDatabase,
        handleConfirmAddToDatabase,
        sendEmailDialogOpen,
        setSendEmailDialogOpen,
        emailSubject,
        setEmailSubject,
        emailBody,
        setEmailBody,
        sendingEmail,
        selectedLeadForEmail,
        handleConfirmSendEmail,
        addToSegmentDialogOpen,
        setAddToSegmentDialogOpen,
        segmentName,
        setSegmentName,
        addingToSegment,
        handleConfirmAddToSegment,
        addToWorkflowDialogOpen,
        setAddToWorkflowDialogOpen,
        selectedWorkflowId,
        setSelectedWorkflowId,
        addingToWorkflow,
        handleConfirmAddToWorkflow,
        selectedLeadIdForAction,
        setSelectedLeadIdForAction
    } = leadManagement
    const { token } = useAuth()
    const { workspace } = useWorkspace()

    const sharedProps = {
        leadActions,
        onLeadCreated,
        token: token?.token,
        workspaceId: workspace?.workspace?.id,
        ActionButton,
        ViewLinksModal,
        LeadActionsDropdown,
        leadManagement,
        selectedLeadIdForAction,
        setSelectedLeadIdForAction
    }

    const renderContent = () => {
        switch (activeSubTab) {
            case 'my-leads':
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <SharedModals 
                            emailModalOpen={emailModalOpen}
                            setEmailModalOpen={setEmailModalOpen}
                            phoneModalOpen={phoneModalOpen}
                            setPhoneModalOpen={setPhoneModalOpen}
                            selectedLeadId={selectedLeadId}
                            unlockEmailCallbackRef={unlockEmailCallbackRef}
                            unlockPhoneCallbackRef={unlockPhoneCallbackRef}
                            addToDatabaseDialogOpen={addToDatabaseDialogOpen}
                            setAddToDatabaseDialogOpen={setAddToDatabaseDialogOpen}
                            selectedLeadForDatabase={selectedLeadForDatabase}
                            setSelectedLeadForDatabase={setSelectedLeadForDatabase}
                            addingToDatabase={addingToDatabase}
                            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
                            selectedLeadIdForAction={selectedLeadIdForAction}
                            setSelectedLeadIdForAction={setSelectedLeadIdForAction}
                            sendEmailDialogOpen={sendEmailDialogOpen}
                            setSendEmailDialogOpen={setSendEmailDialogOpen}
                            selectedLeadForEmail={selectedLeadForEmail}
                            emailSubject={emailSubject}
                            setEmailSubject={setEmailSubject}
                            emailBody={emailBody}
                            setEmailBody={setEmailBody}
                            sendingEmail={sendingEmail}
                            handleConfirmSendEmail={handleConfirmSendEmail}
                            addToSegmentDialogOpen={addToSegmentDialogOpen}
                            setAddToSegmentDialogOpen={setAddToSegmentDialogOpen}
                            segmentName={segmentName}
                            setSegmentName={setSegmentName}
                            addingToSegment={addingToSegment}
                            handleConfirmAddToSegment={handleConfirmAddToSegment}
                            addToWorkflowDialogOpen={addToWorkflowDialogOpen}
                            setAddToWorkflowDialogOpen={setAddToWorkflowDialogOpen}
                            selectedWorkflowId={selectedWorkflowId}
                            setSelectedWorkflowId={setSelectedWorkflowId}
                            addingToWorkflow={addingToWorkflow}

                            handleConfirmAddToWorkflow={handleConfirmAddToWorkflow}
                        />
                    </>
                )
            case 'find-leads':
                return (
                    <>
                        <FindLeads {...sharedProps} />
                        <SharedModals 
                            emailModalOpen={emailModalOpen}
                            setEmailModalOpen={setEmailModalOpen}
                            phoneModalOpen={phoneModalOpen}
                            setPhoneModalOpen={setPhoneModalOpen}
                            selectedLeadId={selectedLeadId}
                            unlockEmailCallbackRef={unlockEmailCallbackRef}
                            unlockPhoneCallbackRef={unlockPhoneCallbackRef}
                            addToDatabaseDialogOpen={addToDatabaseDialogOpen}
                            setAddToDatabaseDialogOpen={setAddToDatabaseDialogOpen}
                            selectedLeadForDatabase={selectedLeadForDatabase}
                            setSelectedLeadForDatabase={setSelectedLeadForDatabase}
                            addingToDatabase={addingToDatabase}
                            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
                            selectedLeadIdForAction={selectedLeadIdForAction}
                            setSelectedLeadIdForAction={setSelectedLeadIdForAction}
                            sendEmailDialogOpen={sendEmailDialogOpen}
                            setSendEmailDialogOpen={setSendEmailDialogOpen}
                            selectedLeadForEmail={selectedLeadForEmail}
                            emailSubject={emailSubject}
                            setEmailSubject={setEmailSubject}
                            emailBody={emailBody}
                            setEmailBody={setEmailBody}
                            sendingEmail={sendingEmail}
                            handleConfirmSendEmail={handleConfirmSendEmail}
                            addToSegmentDialogOpen={addToSegmentDialogOpen}
                            setAddToSegmentDialogOpen={setAddToSegmentDialogOpen}
                            segmentName={segmentName}
                            setSegmentName={setSegmentName}
                            addingToSegment={addingToSegment}
                            handleConfirmAddToSegment={handleConfirmAddToSegment}
                            addToWorkflowDialogOpen={addToWorkflowDialogOpen}
                            setAddToWorkflowDialogOpen={setAddToWorkflowDialogOpen}
                            selectedWorkflowId={selectedWorkflowId}
                            setSelectedWorkflowId={setSelectedWorkflowId}
                            addingToWorkflow={addingToWorkflow}

                            handleConfirmAddToWorkflow={handleConfirmAddToWorkflow}
                        />
                    </>
                )
            case 'saved-search':
                return (
                    <>
                        <SavedSearch {...sharedProps} />
                        <SharedModals 
                            emailModalOpen={emailModalOpen}
                            setEmailModalOpen={setEmailModalOpen}
                            phoneModalOpen={phoneModalOpen}
                            setPhoneModalOpen={setPhoneModalOpen}
                            selectedLeadId={selectedLeadId}
                            unlockEmailCallbackRef={unlockEmailCallbackRef}
                            unlockPhoneCallbackRef={unlockPhoneCallbackRef}
                            addToDatabaseDialogOpen={addToDatabaseDialogOpen}
                            setAddToDatabaseDialogOpen={setAddToDatabaseDialogOpen}
                            selectedLeadForDatabase={selectedLeadForDatabase}
                            setSelectedLeadForDatabase={setSelectedLeadForDatabase}
                            addingToDatabase={addingToDatabase}
                            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
                            selectedLeadIdForAction={selectedLeadIdForAction}
                            setSelectedLeadIdForAction={setSelectedLeadIdForAction}
                            sendEmailDialogOpen={sendEmailDialogOpen}
                            setSendEmailDialogOpen={setSendEmailDialogOpen}
                            selectedLeadForEmail={selectedLeadForEmail}
                            emailSubject={emailSubject}
                            setEmailSubject={setEmailSubject}
                            emailBody={emailBody}
                            setEmailBody={setEmailBody}
                            sendingEmail={sendingEmail}
                            handleConfirmSendEmail={handleConfirmSendEmail}
                            addToSegmentDialogOpen={addToSegmentDialogOpen}
                            setAddToSegmentDialogOpen={setAddToSegmentDialogOpen}
                            segmentName={segmentName}
                            setSegmentName={setSegmentName}
                            addingToSegment={addingToSegment}
                            handleConfirmAddToSegment={handleConfirmAddToSegment}
                            addToWorkflowDialogOpen={addToWorkflowDialogOpen}
                            setAddToWorkflowDialogOpen={setAddToWorkflowDialogOpen}
                            selectedWorkflowId={selectedWorkflowId}
                            setSelectedWorkflowId={setSelectedWorkflowId}
                            addingToWorkflow={addingToWorkflow}

                            handleConfirmAddToWorkflow={handleConfirmAddToWorkflow}
                        />
                    </>
                )
            default:
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <SharedModals 
                            emailModalOpen={emailModalOpen}
                            setEmailModalOpen={setEmailModalOpen}
                            phoneModalOpen={phoneModalOpen}
                            setPhoneModalOpen={setPhoneModalOpen}
                            selectedLeadId={selectedLeadId}
                            unlockEmailCallbackRef={unlockEmailCallbackRef}
                            unlockPhoneCallbackRef={unlockPhoneCallbackRef}
                            addToDatabaseDialogOpen={addToDatabaseDialogOpen}
                            setAddToDatabaseDialogOpen={setAddToDatabaseDialogOpen}
                            selectedLeadForDatabase={selectedLeadForDatabase}
                            setSelectedLeadForDatabase={setSelectedLeadForDatabase}
                            addingToDatabase={addingToDatabase}
                            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
                            selectedLeadIdForAction={selectedLeadIdForAction}
                            setSelectedLeadIdForAction={setSelectedLeadIdForAction}
                            sendEmailDialogOpen={sendEmailDialogOpen}
                            setSendEmailDialogOpen={setSendEmailDialogOpen}
                            selectedLeadForEmail={selectedLeadForEmail}
                            emailSubject={emailSubject}
                            setEmailSubject={setEmailSubject}
                            emailBody={emailBody}
                            setEmailBody={setEmailBody}
                            sendingEmail={sendingEmail}
                            handleConfirmSendEmail={handleConfirmSendEmail}
                            addToSegmentDialogOpen={addToSegmentDialogOpen}
                            setAddToSegmentDialogOpen={setAddToSegmentDialogOpen}
                            segmentName={segmentName}
                            setSegmentName={setSegmentName}
                            addingToSegment={addingToSegment}
                            handleConfirmAddToSegment={handleConfirmAddToSegment}
                            addToWorkflowDialogOpen={addToWorkflowDialogOpen}
                            setAddToWorkflowDialogOpen={setAddToWorkflowDialogOpen}
                            selectedWorkflowId={selectedWorkflowId}
                            setSelectedWorkflowId={setSelectedWorkflowId}
                            addingToWorkflow={addingToWorkflow}

                            handleConfirmAddToWorkflow={handleConfirmAddToWorkflow}
                        />
                    </>
                )
        }
    }

    return (
        <>
            {renderContent()}
        </>
    )
}

export default People
