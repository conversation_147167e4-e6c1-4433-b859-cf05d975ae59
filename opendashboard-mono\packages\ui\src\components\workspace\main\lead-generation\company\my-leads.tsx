"use client"

import React, { useState, use<PERSON>em<PERSON>, useEffect } from "react"
import { But<PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { ScrollArea } from "@ui/components/ui/scroll-area"
import { PlusIcon, UserGroupIcon, EnvelopeIcon, PhoneIcon, EllipsisVerticalIcon, TrashIcon, ChevronRightIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon, MagnifyingGlassIcon } from "@ui/components/icons/FontAwesomeRegular"
import { MagnifyingGlassCircleIcon } from "@heroicons/react/24/outline"
import { Loader } from "@ui/components/custom-ui/loader"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { DbRecordFilter, Match } from "@repo/app-db-utils/src/typings/db"
import { getMyCompanyLeads } from "@ui/api/leads"
import { Lead } from "@ui/typings/lead"
import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"
import { CompanyTableHeader } from "./index"

interface MyLeadsProps {
    onLeadCreated?: (lead: any) => void
    token?: string
    workspaceId?: string

    ActionButton?: any
    ViewLinksModal?: any
    LeadActionsDropdown?: any
    leadActions?: any
    leadManagement?: any
}

// All components are now imported from index

// All components are now imported from index

const MyLeads = ({ onLeadCreated, token, workspaceId, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement }: MyLeadsProps) => {
    const router = useRouter()
    const params = useParams()
    const { toast } = useAlert()
    

    const [leads, setLeads] = useState<Lead[]>([])
    const [searchQuery, setSearchQuery] = useState("")
    const [filter, setFilter] = useState<DbRecordFilter>({ match: Match.All, conditions: [] })
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    

    React.useEffect(() => {
        if (leadManagement?.setUnlockEmailCallback) {
            leadManagement.setUnlockEmailCallback((unlockedLead: any) => {
                console.log('🔍 [MY-LEADS] Email unlock success callback called')
                console.log('🔍 [MY-LEADS] Unlocked lead data:', unlockedLead)
                
                const normalizedData = unlockedLead?.normalizedData || unlockedLead?.apolloData?.normalizedData
                console.log('🔍 [MY-LEADS] Normalized data:', normalizedData)
                console.log('🔍 [MY-LEADS] Selected lead ID:', leadManagement?.selectedLeadId)
                
                // Update the lead in the list with unlocked data
                setLeads(prev => {
                    console.log('🔍 [MY-LEADS] Current leads before update:', prev.length)
                    const updated = prev.map(lead => {
                        if (lead.id === leadManagement?.selectedLeadId) {
                            console.log('🔍 [MY-LEADS] Updating lead:', lead.name)
                            return { 
                                ...lead, 
                                email: normalizedData?.email || "unlock",
                                normalizedData: {
                                    ...(lead as any).normalizedData,
                                    email: normalizedData?.email || (lead as any).normalizedData?.email,
                                    isEmailVisible: true // Set to true after successful unlock
                                }
                            }
                        }
                        return lead
                    })
                    console.log('🔍 [MY-LEADS] Updated leads:', updated.length)
                    return updated
                })
            })
        }
        
        if (leadManagement?.setUnlockPhoneCallback) {
            leadManagement.setUnlockPhoneCallback((unlockedLead: any) => {
                console.log('🔍 [MY-LEADS] Phone unlock success callback called')
                console.log('🔍 [MY-LEADS] Unlocked lead data:', unlockedLead)
                
                const normalizedData = unlockedLead?.normalizedData || unlockedLead?.apolloData?.normalizedData
                console.log('🔍 [MY-LEADS] Normalized data:', normalizedData)
                console.log('🔍 [MY-LEADS] Selected lead ID:', leadManagement?.selectedLeadId)
                
                // Update the lead in the list with unlocked data
                setLeads(prev => {
                    console.log('🔍 [MY-LEADS] Current leads before update:', prev.length)
                    const updated = prev.map(lead => {
                        if (lead.id === leadManagement?.selectedLeadId) {
                            console.log('🔍 [MY-LEADS] Updating lead:', lead.name)
                            return { 
                                ...lead, 
                                phone: normalizedData?.phone || "unlock",
                                email: normalizedData?.email || lead.email,
                                normalizedData: {
                                    ...(lead as any).normalizedData,
                                    email: normalizedData?.email || (lead as any).normalizedData?.email,
                                    phone: normalizedData?.phone || (lead as any).normalizedData?.phone,
                                    isEmailVisible: normalizedData?.isEmailVisible || (lead as any).normalizedData?.isEmailVisible,
                                    isPhoneVisible: true // Set to true after successful unlock
                                }
                            }
                        }
                        return lead
                    })
                    console.log('🔍 [MY-LEADS] Updated leads:', updated.length)
                    return updated
                })
            })
        }
    }, [leadManagement, leads.length])
    
    // Fetch my company leads when token and workspaceId are available
    useEffect(() => {
        const fetchMyCompanyLeads = async () => {
            if (!token || !workspaceId) return;
            
            setLoading(true);
            setError(null);
            
            try {
                const response = await getMyCompanyLeads(token, workspaceId, {
                    page: 1,
                    limit: 100, // Get reasonable amount for local filtering
                    search: searchQuery || undefined
                });
                
                if (response.isSuccess && response.data?.data?.leads) {
                    console.log('🔍 [MY-LEADS] Raw API response:', response.data.data.leads.length, 'leads')
                    const convertedLeads = leadManagement?.convertApiLeadsToUI(response.data.data.leads) || response.data.data.leads;
                    console.log('🔍 [MY-LEADS] Converted leads:', convertedLeads.length, 'leads')
                    setLeads(convertedLeads);
                } else {
                    setError(response.error || "Failed to load my company leads");
                    toast.error("Error", {
                        description: response.error || "Failed to load my company leads"
                    });
                }
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
                setError(errorMessage);
                toast.error("Error", {
                    description: errorMessage
                });
            } finally {
                setLoading(false);
            }
        };
        
        fetchMyCompanyLeads();
    }, [token, workspaceId, searchQuery, toast]);
    
        // All handlers are now provided by shared hook
    

    // Generate contact links based on real lead data
    // getContactLinks is now provided by shared hook

    const handleNameClick = (lead: Lead) => {
        // Navigate to company details page using router
        const domain = params.domain
        router.push(`/${domain}/lead-generation/companies/details/${lead.id}`)
    }
    
    // filteredLeads is now provided by shared hook
    const filteredLeads = leadManagement?.getFilteredLeads(leads, searchQuery, undefined) || leads

    return (
        <>
            <div className="flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white">
                <div className="flex items-center">
                    <h1 className="text-xs font-semibold text-black">My Companies</h1>
                </div>
                <div className="flex items-center space-x-2">
                    {leadManagement?.selectedLeads.length > 0 && (
                        <Button variant="ghost" className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer" onClick={() => { setLeads(leads.filter(lead => !leadManagement?.selectedLeads.includes(lead.id))); leadManagement?.setSelectedLeads([]) }}>
                            <TrashIcon className="size-3"/>Delete {leadManagement?.selectedLeads.length}
                        </Button>
                    )}

                    <div className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer">
                        <MagnifyingGlassCircleIcon className="size-4"/>
                        <Input placeholder="Search company contacts" value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground" />
                    </div>
                </div>
            </div>
            

            
            {/* Main Content Area */}
            <div className="flex-1 flex overflow-hidden">

                
                {/* Table Container */}
                <div className="flex-1 overflow-hidden">
                    {(loading || error || filteredLeads.length === 0) ? (
                        /* Empty State */
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                {loading ? (
                                    <>
                                        <Loader theme="dark" className="size-8 mx-auto mb-4" />
                                        <p className="text-sm text-muted-foreground">Loading my companies...</p>
                                    </>
                                ) : error ? (
                                    <>
                                        <UserGroupIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-neutral-900 mb-2">Error loading companies</h3>
                                        <p className="text-red-600 text-sm mb-4">{error}</p>
                                    </>
                                ) : (
                                    <>
                                        <MagnifyingGlassIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-neutral-900 mb-2">
                                            {searchQuery ? "No companies found" : "No companies in your leads yet"}
                                        </h3>
                                        <p className="text-neutral-500 mb-4 text-sm">
                                            {searchQuery 
                                                ? "Try adjusting your search query or filters to find more results" 
                                                : "Use the search box above or apply filters on the left to discover companies"
                                            }
                                        </p>
                                        {!searchQuery && (
                                            <Button 
                                                onClick={() => {
                                                    const domain = params.domain;
                                                    router.push(`/${domain}/lead-generation/companies/find-leads`);
                                                }}
                                                size="sm" 
                                                className="flex items-center space-x-2 mx-auto"
                                            >
                                                <MagnifyingGlassIcon className="size-4" />
                                                <span>Find Companies</span>
                                            </Button>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    ) : (
                        /* Table with Results */
                        <ScrollArea className="size-full scrollBlockChild">
                            <Table>
                                <CompanyTableHeader 
                                    selectedLeads={leadManagement?.selectedLeads || []}
                                    filteredLeads={filteredLeads}
                                    handleSelectAll={leadManagement?.handleSelectAll || (() => {})}
                                />
                                <TableBody>
                                    {filteredLeads.map((lead) => {
                                        // Type guard to ensure we're working with company data
                                        const isCompanyLead = lead?.type === 'company';
                                        const apolloCompanyData = isCompanyLead ? (lead.apolloData as any) : null;
                                        
                                        return (
                                        <TableRow key={lead.id} className="border-b border-neutral-200 hover:bg-neutral-50 transition-colors group">
                                            <TableCell className="w-12 px-3 relative">
                                                <Checkbox checked={leadManagement?.selectedLeads.includes(lead.id)} onCheckedChange={(checked: boolean) => leadManagement?.handleSelectLead(lead.id, checked)} className={`${leadManagement?.selectedLeads.includes(lead.id) ? 'opacity-100 visible' : 'invisible opacity-0 group-hover:opacity-100 group-hover:visible'}`} />
                                            </TableCell>
                                            <TableCell className="px-1">
                                                <button 
                                                    className="text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0" 
                                                    onClick={() => handleNameClick(lead)}
                                                >
                                                    {lead.name}
                                                </button>
                                            </TableCell>
                                            <TableCell className="px-1 text-xs text-muted-foreground">{lead.industry || '-'}</TableCell>
                                            <TableCell className="px-1 text-xs text-muted-foreground">{lead.location || '-'}</TableCell>
                                            <TableCell className="px-1">
                                                {lead.normalizedData?.email && lead.normalizedData?.isEmailVisible ? (
                                                    <div className="text-xs text-black font-medium truncate max-w-[120px]" title={lead.normalizedData.email}>
                                                        {lead.normalizedData.email}
                                                    </div>
                                                ) : (
                                                    <ActionButton icon={EnvelopeIcon} onClick={() => leadManagement?.handleUnlockEmail(lead.id)}>Unlock Email</ActionButton>
                                                )}
                                            </TableCell>
                                            <TableCell className="px-1">
                                                {lead.normalizedData?.phone && lead.normalizedData?.isPhoneVisible ? (
                                                    <div className="text-xs text-black font-medium truncate max-w-[120px]" title={lead.normalizedData.phone}>
                                                        {lead.normalizedData.phone}
                                                    </div>
                                                ) : (
                                                    <ActionButton icon={PhoneIcon} onClick={() => leadManagement?.handleUnlockPhone(lead.id)}>Unlock Mobile</ActionButton>
                                                )}
                                            </TableCell>
                                            <TableCell className="px-1">
                                                <ViewLinksModal trigger={<button className="text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0">View links<ChevronRightIcon className="size-3" /></button>} links={leadManagement?.getContactLinks(lead) || []} />
                                            </TableCell>
                                            <TableCell className="w-12 px-2 relative">
                                                                                                <LeadActionsDropdown
                                                    trigger={
                                                        <Button 
                                                            variant="ghost" 
                                                            size="sm" 
                                                            className="h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible"
                                                        >
                                                            <EllipsisVerticalIcon className="size-4" />
                                                        </Button>
                                                    } 
                                                    leadData={lead}
                                                    onSendEmail={() => leadActions?.handleSendEmail(lead.id, lead)}
                                                    onAddToSegments={() => leadActions?.handleAddToSegments(lead.id)}
                                                    onAddToDatabase={() => leadActions?.handleAddToDatabase(lead.id, lead)}
                                                    onAddToWorkflow={() => leadActions?.handleAddToWorkflow(lead.id)}
                                                />
                                            </TableCell>
                                        </TableRow>
                                        )
                                    })}
                                </TableBody>
                            </Table>
                            <div className="border-b border-neutral-200"></div>
                        </ScrollArea>
                    )}
                </div>
            </div>
        
         {/* Unlock modals are now handled by SharedModals in the index */}
        </>
    )
}

export default MyLeads
