"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/lead-generation/companies/saved-search/page",{

/***/ "(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/saved-search.tsx":
/*!************************************************************************************************!*\
  !*** ../../packages/ui/src/components/workspace/main/lead-generation/company/saved-search.tsx ***!
  \************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ui/components/ui/button */ \"(app-pages-browser)/../../packages/ui/src/components/ui/button.tsx\");\n/* harmony import */ var _ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ui/components/ui/checkbox */ \"(app-pages-browser)/../../packages/ui/src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ui/components/ui/table */ \"(app-pages-browser)/../../packages/ui/src/components/ui/table.tsx\");\n/* harmony import */ var _ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ui/components/ui/scroll-area */ \"(app-pages-browser)/../../packages/ui/src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ui/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/../../packages/ui/src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ui/components/custom-ui/loader */ \"(app-pages-browser)/../../packages/ui/src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _ui_api_leads__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ui/api/leads */ \"(app-pages-browser)/../../packages/ui/src/api/leads.ts\");\n/* harmony import */ var _ui_providers_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ui/providers/alert */ \"(app-pages-browser)/../../packages/ui/src/providers/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// All components are now imported from index\n// All components are now imported from index\n// All components are now imported from index\nconst SavedSearch = (param)=>{\n    let { onLeadCreated, token, workspaceId, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useParams)();\n    const [savedSearches, setSavedSearches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for showing leads from a saved search\n    const [selectedSearch, setSelectedSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [executingSearch, setExecutingSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingSearch, setDeletingSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_ui_providers_alert__WEBPACK_IMPORTED_MODULE_9__.useAlert)();\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockEmailCallback) {\n            leadManagement.setUnlockEmailCallback((unlockedLead)=>{\n                var _unlockedLead_apolloData;\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCE7 Email unlock success callback called\");\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCE7 Unlocked lead data:\", unlockedLead);\n                // Extract normalizedData from the correct location\n                const normalizedData = (unlockedLead === null || unlockedLead === void 0 ? void 0 : unlockedLead.normalizedData) || (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_apolloData = unlockedLead.apolloData) === null || _unlockedLead_apolloData === void 0 ? void 0 : _unlockedLead_apolloData.normalizedData);\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCE7 Normalized data:\", normalizedData);\n                // Update the lead in the list with unlocked data\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _lead_normalizedData, _lead_normalizedData1;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || \"unlock\",\n                            normalizedData: {\n                                ...lead.normalizedData,\n                                email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email),\n                                isEmailVisible: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.isEmailVisible) || ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.isEmailVisible)\n                            }\n                        } : lead;\n                    }));\n            });\n        }\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockPhoneCallback) {\n            leadManagement.setUnlockPhoneCallback((unlockedLead)=>{\n                var _unlockedLead_apolloData;\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCF1 Phone unlock success callback called\");\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCF1 Unlocked lead data:\", unlockedLead);\n                // Extract normalizedData from the correct location\n                const normalizedData = (unlockedLead === null || unlockedLead === void 0 ? void 0 : unlockedLead.normalizedData) || (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_apolloData = unlockedLead.apolloData) === null || _unlockedLead_apolloData === void 0 ? void 0 : _unlockedLead_apolloData.normalizedData);\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCF1 Normalized data:\", normalizedData);\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCF1 Unlocked phone:\", normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.phone);\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCF1 Unlocked email:\", normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email);\n                console.log(\"\\uD83D\\uDD0D [COMPANY SAVED SEARCH] \\uD83D\\uDCF1 Selected lead ID:\", leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId);\n                // Update the lead in the list with unlocked data\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _lead_normalizedData, _lead_normalizedData1, _lead_normalizedData2, _lead_normalizedData3;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            phone: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.phone) || \"unlock\",\n                            email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || lead.email,\n                            normalizedData: {\n                                ...lead.normalizedData,\n                                email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email),\n                                phone: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.phone) || ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.phone),\n                                isEmailVisible: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.isEmailVisible) || ((_lead_normalizedData2 = lead.normalizedData) === null || _lead_normalizedData2 === void 0 ? void 0 : _lead_normalizedData2.isEmailVisible),\n                                isPhoneVisible: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.isPhoneVisible) || ((_lead_normalizedData3 = lead.normalizedData) === null || _lead_normalizedData3 === void 0 ? void 0 : _lead_normalizedData3.isPhoneVisible)\n                            }\n                        } : lead;\n                    }));\n            });\n        }\n    }, [\n        leadManagement,\n        leads.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSavedSearches = async ()=>{\n            if (!token || !workspaceId) return;\n            setLoading(true);\n            setError(null);\n            try {\n                var _response_data, _response_data_data, _response_data1, _response_data_data1, _response_data2;\n                const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_8__.getSavedSearches)(token, workspaceId, {\n                    searchType: \"company\"\n                });\n                console.log(\"\\uD83D\\uDD0D [SAVED SEARCHES] \\uD83D\\uDD0D Full response:\", response);\n                console.log(\"\\uD83D\\uDD0D [SAVED SEARCHES] \\uD83D\\uDD0D Response keys:\", Object.keys(response));\n                console.log(\"\\uD83D\\uDD0D [SAVED SEARCHES] \\uD83D\\uDD0D isSuccess:\", response.isSuccess);\n                console.log(\"\\uD83D\\uDD0D [SAVED SEARCHES] \\uD83D\\uDD0D error:\", response.error);\n                console.log(\"\\uD83D\\uDD0D [SAVED SEARCHES] \\uD83D\\uDD0D data:\", response.data);\n                console.log(\"\\uD83D\\uDD0D [SAVED SEARCHES] \\uD83D\\uDD0D data.data:\", (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data);\n                console.log(\"\\uD83D\\uDD0D [SAVED SEARCHES] \\uD83D\\uDD0D searches:\", (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.searches);\n                if (response.isSuccess && ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : (_response_data_data1 = _response_data2.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.searches)) {\n                    setSavedSearches(response.data.data.searches);\n                    console.log(\"\\uD83D\\uDD0D [SAVED SEARCHES] ✅ Loaded \".concat(response.data.data.searches.length, \" saved searches\"));\n                } else {\n                    console.error(\"\\uD83D\\uDD0D [SAVED SEARCHES] ❌ Failed to load - isSuccess: \".concat(response.isSuccess, \", error: \").concat(response.error));\n                    setError(response.error || \"Failed to load saved searches\");\n                    toast.error(\"Failed to load saved searches\");\n                }\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : \"An unknown error occurred\";\n                setError(errorMessage);\n                toast.error(\"Failed to load saved searches\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSavedSearches();\n    }, [\n        token,\n        workspaceId,\n        toast\n    ]);\n    // Delete saved search\n    const handleDeleteSearch = async (searchId, event)=>{\n        event.stopPropagation() // Prevent card click\n        ;\n        if (!token || !workspaceId) {\n            toast.error(\"Authentication required\");\n            return;\n        }\n        setDeletingSearch(searchId);\n        try {\n            console.log(\"\\uD83D\\uDDD1️ [DELETE SEARCH] \\uD83D\\uDE80 Deleting saved search:\", searchId);\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_8__.deleteSavedSearch)(token, workspaceId, searchId);\n            console.log(\"\\uD83D\\uDDD1️ [DELETE SEARCH] \\uD83D\\uDCCA Response:\", response);\n            if (response.isSuccess) {\n                // Remove from local state\n                setSavedSearches((prev)=>prev.filter((search)=>search.id !== searchId));\n                toast.success(\"Saved search deleted successfully\");\n                console.log(\"\\uD83D\\uDDD1️ [DELETE SEARCH] ✅ Successfully deleted saved search\");\n            } else {\n                toast.error(\"Failed to delete saved search\");\n                console.error(\"\\uD83D\\uDDD1️ [DELETE SEARCH] ❌ Failed to delete:\", response.error);\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDDD1️ [DELETE SEARCH] ❌ Error deleting saved search:\", error);\n            toast.error(\"Failed to delete saved search\");\n        } finally{\n            setDeletingSearch(null);\n        }\n    };\n    // Execute saved search and show results in table\n    const handleExecuteSearch = async (savedSearch)=>{\n        if (!token || !workspaceId) {\n            toast.error(\"Authentication required\");\n            return;\n        }\n        setExecutingSearch(true);\n        setSelectedSearch(savedSearch);\n        try {\n            var _response_data_data, _response_data;\n            console.log(\"\\uD83D\\uDD0D [SAVED SEARCH] \\uD83D\\uDE80 Executing saved search:\", savedSearch.id);\n            console.log(\"\\uD83D\\uDD0D [SAVED SEARCH] Filters:\", savedSearch.filters);\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_8__.executeSavedSearch)(token, workspaceId, savedSearch.id, {\n                pagination: {\n                    page: 1,\n                    limit: 50\n                },\n                searchType: \"company\"\n            });\n            console.log(\"\\uD83D\\uDD0D [SAVED SEARCH] \\uD83D\\uDCCA Response:\", response);\n            if (response.isSuccess && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.leads)) {\n                const apiLeads = response.data.data.leads;\n                // Convert API leads to UI format using shared logic\n                const convertedLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.convertApiLeadsToUI(apiLeads)) || [];\n                setLeads(convertedLeads);\n                leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setSelectedLeads([]);\n                toast.success(\"Loaded \".concat(convertedLeads.length, ' company leads from \"').concat(savedSearch.name, '\"'));\n                console.log(\"\\uD83D\\uDD0D [SAVED SEARCH] ✅ Successfully loaded company leads:\", convertedLeads.length);\n            } else {\n                toast.error(\"Failed to execute saved search\");\n                console.error(\"\\uD83D\\uDD0D [SAVED SEARCH] ❌ Failed to execute:\", response.error);\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [SAVED SEARCH] ❌ Error executing saved search:\", error);\n            toast.error(\"Failed to execute saved search\");\n        } finally{\n            setExecutingSearch(false);\n        }\n    };\n    // Generate contact links based on lead data\n    // getContactLinks is now provided by shared hook\n    // Event handlers\n    // Selection handlers are now provided by shared hook\n    // All handlers are now provided by shared hook\n    // If we have a selected search and leads, show the table\n    if (selectedSearch && leads.length > 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        setSelectedSearch(null);\n                                        setLeads([]);\n                                        leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setSelectedLeads([]);\n                                    },\n                                    className: \"text-xs rounded-full h-auto px-3 py-1.5 gap-1.5\",\n                                    children: \"← Back to Saved Searches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xs font-semibold text-black\",\n                                    children: selectedSearch.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer\",\n                                onClick: ()=>{\n                                    setLeads(leads.filter((lead)=>!(leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id))));\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setSelectedLeads([]);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.TrashIcon, {\n                                        className: \"size-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    \"Delete \",\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                        className: \"size-full scrollBlockChild\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                            className: \"border-b border-neutral-200 bg-white sticky top-0 z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"w-12 h-10 px-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                        checked: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length) === leads.length && leads.length > 0,\n                                                        onCheckedChange: (checked)=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectAll(checked, leads)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"h-10 px-1 text-left font-bold text-black\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"h-10 px-1 text-left font-bold text-black\",\n                                                    children: \"Industry\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"h-10 px-1 text-left font-bold text-black\",\n                                                    children: \"Location\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"h-10 px-1 text-left font-bold text-black\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"h-10 px-1 text-left font-bold text-black\",\n                                                    children: \"Phone number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"h-10 px-1 text-left font-bold text-black\",\n                                                    children: \"Social Links\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableHead, {\n                                                    className: \"w-12 h-10 px-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableBody, {\n                                        children: leads.map((lead)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableRow, {\n                                                className: \"border-b border-neutral-200 hover:bg-neutral-50 transition-colors group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"w-12 px-3 relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                            checked: leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id),\n                                                            onCheckedChange: (checked)=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectLead(lead.id, checked),\n                                                            className: \"\".concat((leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id)) ? \"opacity-100 visible\" : \"invisible opacity-0 group-hover:opacity-100 group-hover:visible\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"px-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0\",\n                                                            onClick: ()=>{\n                                                                const domain = params.domain;\n                                                                router.push(\"/\".concat(domain, \"/lead-generation/companies/details/\").concat(lead.id));\n                                                            },\n                                                            children: lead.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"px-1 text-xs text-muted-foreground\",\n                                                        children: lead.industry\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"px-1 text-xs text-muted-foreground\",\n                                                        children: lead.location\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"px-1\",\n                                                        children: lead.email && lead.email !== \"unlock\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                            title: lead.email,\n                                                            children: lead.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 49\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                            icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.EnvelopeIcon,\n                                                            onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockEmail(lead.id),\n                                                            children: \"Unlock Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"px-1\",\n                                                        children: lead.phone && lead.phone !== \"unlock\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                            title: lead.phone,\n                                                            children: lead.phone\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 49\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                            icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon,\n                                                            onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockPhone(lead.id),\n                                                            children: \"Unlock Mobile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"px-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewLinksModal, {\n                                                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0\",\n                                                                children: [\n                                                                    \"View links\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.ChevronRightIcon, {\n                                                                        className: \"size-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 57\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 53\n                                                            }, void 0),\n                                                            links: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getContactLinks(lead)) || []\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_4__.TableCell, {\n                                                        className: \"w-12 px-2 relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeadActionsDropdown, {\n                                                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                className: \"h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.EllipsisVerticalIcon, {\n                                                                    className: \"size-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 57\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 53\n                                                            }, void 0),\n                                                            leadData: lead,\n                                                            onSendEmail: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleSendEmail(lead.id, lead),\n                                                            onAddToSegments: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToSegments(lead.id),\n                                                            onAddToDatabase: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToDatabase(lead.id, lead),\n                                                            onAddToWorkflow: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToWorkflow(lead.id)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, lead.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-neutral-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true);\n    }\n    // Show saved searches as cards\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xs font-semibold text-black\",\n                        children: \"Saved Searches\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                lineNumber: 416,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_7__.Loader, {\n                                theme: \"dark\",\n                                className: \"size-8 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-500 text-sm\",\n                                children: \"Loading saved searches...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 25\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.MagnifyingGlassIcon, {\n                                className: \"size-12 text-neutral-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-neutral-900 mb-2\",\n                                children: \"Error loading saved searches\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-500 mb-4 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 25\n                }, undefined) : savedSearches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.BookmarkIcon, {\n                                className: \"size-12 text-neutral-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-neutral-900 mb-2\",\n                                children: \"No saved searches\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-500 mb-4 text-sm\",\n                                children: \"Save searches from the Find Companies page to see them here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                    className: \"size-full scrollBlockChild\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: savedSearches.map((search)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-neutral-200 rounded-lg p-4 hover:border-neutral-300 hover:shadow-sm transition-all cursor-pointer bg-white relative group\",\n                                onClick: ()=>handleExecuteSearch(search),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.BookmarkIcon, {\n                                                        className: \"size-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 53\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold text-black truncate\",\n                                                        children: search.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    executingSearch && (selectedSearch === null || selectedSearch === void 0 ? void 0 : selectedSearch.id) === search.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_7__.Loader, {\n                                                        theme: \"dark\",\n                                                        className: \"size-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleDeleteSearch(search.id, e),\n                                                        disabled: deletingSearch === search.id,\n                                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-red-50 rounded-full text-red-500 hover:text-red-600 disabled:opacity-50\",\n                                                        title: \"Delete saved search\",\n                                                        children: deletingSearch === search.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_7__.Loader, {\n                                                            theme: \"dark\",\n                                                            className: \"size-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 53\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.TrashIcon, {\n                                                            className: \"size-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    search.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-neutral-600 mb-3 line-clamp-2\",\n                                        children: search.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-xs text-neutral-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Saved \",\n                                                    (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_11__.formatDistanceToNow)(new Date(search.createdAt), {\n                                                        addSuffix: true\n                                                    })\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_6__.ChevronRightIcon, {\n                                                className: \"size-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, search.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 33\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\saved-search.tsx\",\n                lineNumber: 423,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(SavedSearch, \"DKz6Ow07YVkcg6fQuQyqTtrif/k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useParams,\n        _ui_providers_alert__WEBPACK_IMPORTED_MODULE_9__.useAlert\n    ];\n});\n_c = SavedSearch;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SavedSearch);\nvar _c;\n$RefreshReg$(_c, \"SavedSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/saved-search.tsx\n"));

/***/ })

});