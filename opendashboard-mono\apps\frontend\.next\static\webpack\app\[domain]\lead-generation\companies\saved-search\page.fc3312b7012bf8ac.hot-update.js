"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/lead-generation/companies/saved-search/page",{

/***/ "(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/people/find-leads.tsx":
/*!*********************************************************************************************!*\
  !*** ../../packages/ui/src/components/workspace/main/lead-generation/people/find-leads.tsx ***!
  \*********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ui/components/ui/button */ \"(app-pages-browser)/../../packages/ui/src/components/ui/button.tsx\");\n/* harmony import */ var _ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ui/components/ui/input */ \"(app-pages-browser)/../../packages/ui/src/components/ui/input.tsx\");\n/* harmony import */ var _ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ui/components/ui/checkbox */ \"(app-pages-browser)/../../packages/ui/src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ui/components/ui/table */ \"(app-pages-browser)/../../packages/ui/src/components/ui/table.tsx\");\n/* harmony import */ var _ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ui/components/ui/scroll-area */ \"(app-pages-browser)/../../packages/ui/src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ui/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/../../packages/ui/src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassCircleIcon.js\");\n/* harmony import */ var _ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ui/components/custom-ui/loader */ \"(app-pages-browser)/../../packages/ui/src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _ui_components_workspace_main_lead_generation_filters_people__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ui/components/workspace/main/lead-generation/filters/people */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/filters/people.tsx\");\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./index */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/people/index.tsx\");\n/* harmony import */ var _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ui/context/routerContext */ \"(app-pages-browser)/../../packages/ui/src/context/routerContext.tsx\");\n/* harmony import */ var _ui_api_leads__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ui/api/leads */ \"(app-pages-browser)/../../packages/ui/src/api/leads.ts\");\n/* harmony import */ var _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @ui/providers/workspace */ \"(app-pages-browser)/../../packages/ui/src/providers/workspace.tsx\");\n/* harmony import */ var _ui_providers_user__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ui/providers/user */ \"(app-pages-browser)/../../packages/ui/src/providers/user.tsx\");\n/* harmony import */ var _ui_providers_alert__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @ui/providers/alert */ \"(app-pages-browser)/../../packages/ui/src/providers/alert.tsx\");\n/* harmony import */ var _ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @ui/components/ui/dialog */ \"(app-pages-browser)/../../packages/ui/src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst FindLeads = (param)=>{\n    let { onLeadCreated, sidebarState, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement } = param;\n    _s();\n    const router = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const params = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__.useParams)();\n    const { workspace } = (0,_ui_providers_workspace__WEBPACK_IMPORTED_MODULE_13__.useWorkspace)();\n    const { token } = (0,_ui_providers_user__WEBPACK_IMPORTED_MODULE_14__.useAuth)();\n    const { toast } = (0,_ui_providers_alert__WEBPACK_IMPORTED_MODULE_15__.useAlert)();\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchFilters, setSearchFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        person: {},\n        company: {},\n        signals: {},\n        customFilters: {}\n    });\n    const [excludeMyLeads, setExcludeMyLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockEmailCallback) {\n            leadManagement.setUnlockEmailCallback((unlockedLead)=>{\n                // Update the lead in the list with unlocked data\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _unlockedLead_normalizedData, _unlockedLead_normalizedData1, _lead_normalizedData, _unlockedLead_normalizedData2, _lead_normalizedData1;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            email: (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_normalizedData = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData === void 0 ? void 0 : _unlockedLead_normalizedData.email) || \"unlock\",\n                            normalizedData: {\n                                ...lead.normalizedData,\n                                email: (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_normalizedData1 = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData1 === void 0 ? void 0 : _unlockedLead_normalizedData1.email) || ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email),\n                                isEmailVisible: (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_normalizedData2 = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData2 === void 0 ? void 0 : _unlockedLead_normalizedData2.isEmailVisible) || ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.isEmailVisible)\n                            }\n                        } : lead;\n                    }));\n            });\n        }\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockPhoneCallback) {\n            leadManagement.setUnlockPhoneCallback((unlockedLead)=>{\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _unlockedLead_normalizedData, _unlockedLead_normalizedData1;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            phone: ((_unlockedLead_normalizedData = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData === void 0 ? void 0 : _unlockedLead_normalizedData.phone) || \"unlock\",\n                            email: ((_unlockedLead_normalizedData1 = unlockedLead.normalizedData) === null || _unlockedLead_normalizedData1 === void 0 ? void 0 : _unlockedLead_normalizedData1.email) || lead.email\n                        } : lead;\n                    }));\n            });\n        }\n    }, [\n        leadManagement,\n        leads.length\n    ]);\n    const [isSavingSearch, setIsSavingSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSearchId, setCurrentSearchId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [saveDialogOpen, setSaveDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchName, setSearchName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const defaultFilters = {\n            person: {},\n            company: {},\n            signals: {},\n            customFilters: {}\n        };\n        setSearchFilters(defaultFilters);\n    }, []);\n    const searchLeads = async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : searchFilters, page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        var _workspace_workspace;\n        if (!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || !(token === null || token === void 0 ? void 0 : token.token)) {\n            toast.error(\"Authentication required\");\n            return;\n        }\n        if (false) {}\n        setIsSearching(true);\n        try {\n            var _response_data_data, _response_data, _response_data_data1, _response_data1, _response_data_data2, _response_data2, _response_data_data3, _response_data3, _response_data_data4, _response_data4, _response_data_data5, _response_data5, _response_data_data_metadata, _response_data_data6, _response_data6;\n            const cleanFilters = {\n                ...searchFilters\n            };\n            const searchRequest = {\n                filters: cleanFilters,\n                excludeMyLeads,\n                pagination: {\n                    page: page,\n                    limit: 50\n                }\n            };\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_12__.searchPeopleLeads)(token.token, workspace.workspace.id, searchRequest);\n            if (response.error) {\n                // Provide user-friendly error messages\n                const errorMessage = response.error.toLowerCase();\n                if (errorMessage.includes(\"unauthorized\") || errorMessage.includes(\"authentication\")) {\n                    toast.error(\"Session expired. Please log in again.\");\n                } else if (errorMessage.includes(\"network\") || errorMessage.includes(\"connection\")) {\n                    toast.error(\"Connection error. Please check your internet and try again.\");\n                } else if (errorMessage.includes(\"env\") || errorMessage.includes(\"undefined\")) {\n                    toast.error(\"Search service is currently unavailable. Please try again later.\");\n                } else {\n                    toast.error(\"Failed to search leads. Please try again.\");\n                }\n                return;\n            }\n            if (!response.data || !response.data.data) {\n                toast.error(\"No search results found. Please try different search criteria.\");\n                return;\n            }\n            const apiLeads = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.leads) || [];\n            const convertedLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.convertApiLeadsToUI(apiLeads)) || [];\n            setLeads(convertedLeads);\n            setSearchResults({\n                totalCount: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data1 = _response_data1.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.totalCount,\n                metadata: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : (_response_data_data2 = _response_data2.data) === null || _response_data_data2 === void 0 ? void 0 : _response_data_data2.metadata) || {},\n                hasNextPage: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data3 = _response_data3.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.hasNextPage\n            });\n            const searchId = ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data4 = _response_data4.data) === null || _response_data_data4 === void 0 ? void 0 : _response_data_data4.searchId) || \"\";\n            setCurrentSearchId(searchId);\n            const responseTotalCount = ((_response_data5 = response.data) === null || _response_data5 === void 0 ? void 0 : (_response_data_data5 = _response_data5.data) === null || _response_data_data5 === void 0 ? void 0 : _response_data_data5.totalCount) || 0;\n            let availablePages = 1;\n            const totalPagesAvailable = ((_response_data6 = response.data) === null || _response_data6 === void 0 ? void 0 : (_response_data_data6 = _response_data6.data) === null || _response_data_data6 === void 0 ? void 0 : (_response_data_data_metadata = _response_data_data6.metadata) === null || _response_data_data_metadata === void 0 ? void 0 : _response_data_data_metadata.totalPagesAvailable) || 1;\n            availablePages = totalPagesAvailable + 1;\n            setTotalCount(responseTotalCount);\n            setTotalPages(availablePages);\n            setCurrentPage(page);\n            setHasSearched(true);\n            toast.success(\"Found \".concat(convertedLeads.length, \" leads\"));\n        } catch (error) {\n            toast.error(\"Search service is temporarily unavailable. Please try again later.\");\n        } finally{\n            setIsSearching(false);\n        }\n    };\n    const handleFilterChange = (filters)=>{\n        setCurrentPage(1);\n        setTotalPages(1);\n        setTotalCount(0);\n        setCurrentSearchId(\"\");\n        setHasSearched(false);\n        setLeads([]);\n        setSearchResults(null);\n        setSearchFilters(filters);\n    };\n    const handlePageChange = (page)=>{\n        if (page < 1) {\n            return;\n        }\n        setCurrentPage(page);\n        searchLeads(searchFilters, page);\n    };\n    const calculateTotalPages = function(totalLeads) {\n        let leadsPerPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        return Math.ceil(totalLeads / leadsPerPage);\n    };\n    const generatePageNumbers = ()=>{\n        const pages = [];\n        const maxVisiblePages = 5;\n        if (totalPages <= maxVisiblePages) {\n            for(let i = 1; i <= totalPages; i++){\n                pages.push(i);\n            }\n        } else {\n            if (currentPage <= 3) {\n                for(let i = 1; i <= 5; i++){\n                    pages.push(i);\n                }\n                pages.push(\"...\");\n                pages.push(totalPages);\n            } else if (currentPage >= totalPages - 2) {\n                pages.push(1);\n                pages.push(\"...\");\n                for(let i = totalPages - 4; i <= totalPages; i++){\n                    pages.push(i);\n                }\n            } else {\n                pages.push(1);\n                pages.push(\"...\");\n                for(let i = currentPage - 1; i <= currentPage + 1; i++){\n                    pages.push(i);\n                }\n                pages.push(\"...\");\n                pages.push(totalPages);\n            }\n        }\n        return pages;\n    };\n    const handleSearch = ()=>{\n        const filtersToSend = {\n            ...searchFilters\n        };\n        searchLeads(filtersToSend, currentPage);\n    };\n    const handleSaveLeads = ()=>{\n        var _workspace_workspace;\n        if (!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || !(token === null || token === void 0 ? void 0 : token.token)) {\n            toast.error(\"Authentication required\");\n            return;\n        }\n        if (!hasSearched || leads.length === 0) {\n            toast.error(\"No search results to save\");\n            return;\n        }\n        const defaultName = \"Search Results - \".concat(new Date().toLocaleDateString());\n        setSearchName(defaultName);\n        setSaveDialogOpen(true);\n    };\n    // Handle the actual save operation\n    const handleConfirmSave = async ()=>{\n        var _workspace_workspace;\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDE80 User confirmed save operation\");\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDCCA Save details:\", {\n            searchName: searchName === null || searchName === void 0 ? void 0 : searchName.trim(),\n            leadsCount: leads.length,\n            currentPage,\n            currentSearchId,\n            hasToken: !!(token === null || token === void 0 ? void 0 : token.token),\n            hasWorkspace: !!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id)\n        });\n        if (!searchName || searchName.trim() === \"\") {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Empty search name\");\n            toast.error(\"Search name cannot be empty\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ✅ Starting save process...\");\n        setIsSavingSearch(true);\n        setSaveDialogOpen(false);\n        try {\n            var _response_data;\n            const saveRequest = {\n                name: searchName.trim(),\n                description: \"Saved search with \".concat(leads.length, \" leads from page \").concat(currentPage),\n                filters: searchFilters,\n                searchId: currentSearchId || undefined,\n                searchType: \"people\"\n            };\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDCE4 Sending save request:\", saveRequest);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD17 API call: saveSearch()\");\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_12__.saveSearch)(token.token, workspace.workspace.id, saveRequest);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDCE5 Received response from backend\");\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D Full response:\", response);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D Response keys:\", Object.keys(response));\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D isSuccess:\", response.isSuccess);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D error:\", response.error);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D data:\", response.data);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDD0D data.data:\", (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data);\n            if (response.isSuccess) {\n                var _response_data_data_savedSearch, _response_data_data, _response_data1;\n                console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ✅ Save operation successful!\");\n                console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83D\\uDCCB Saved search details:\", {\n                    name: searchName,\n                    leadsCount: leads.length,\n                    searchId: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : (_response_data_data_savedSearch = _response_data_data.savedSearch) === null || _response_data_data_savedSearch === void 0 ? void 0 : _response_data_data_savedSearch.id,\n                    searchType: \"people\"\n                });\n                toast.success('Saved \"'.concat(searchName, '\" with ').concat(leads.length, \" leads\"));\n            } else {\n                console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Save operation failed\");\n                console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Error details:\", {\n                    isSuccess: response.isSuccess,\n                    error: response.error,\n                    status: response.status\n                });\n                toast.error(response.error || \"Failed to save search\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Exception during save operation:\", error);\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Error type:\", typeof error);\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] ❌ Error message:\", error instanceof Error ? error.message : \"Unknown error\");\n            toast.error(\"Failed to save search. Please try again.\");\n        } finally{\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE] \\uD83C\\uDFC1 Save operation completed, resetting UI state\");\n            setIsSavingSearch(false);\n        }\n    };\n    // Filter and search functionality - simplified to work with real API data\n    // filteredLeads is now provided by lead management hook\n    const filteredLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getFilteredLeads(leads, undefined, undefined)) || leads;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xs font-semibold text-black\",\n                            children: \"Find People\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer\",\n                                onClick: ()=>{\n                                    setLeads(leads.filter((lead)=>!(leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id))));\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setSelectedLeads([]);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.TrashIcon, {\n                                        className: \"size-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    \"Delete \",\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-neutral-600 whitespace-nowrap\",\n                                                children: [\n                                                    \"Page \",\n                                                    currentPage,\n                                                    \" of \",\n                                                    totalPages,\n                                                    totalCount > 0 && \" (\".concat(totalCount, \" total)\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            totalPages > 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handlePageChange(Math.max(1, currentPage - 1)),\n                                                        disabled: currentPage === 1,\n                                                        className: \"h-6 w-6 p-0 text-xs\",\n                                                        children: \"←\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: generatePageNumbers().map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                children: page === \"...\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-1 text-neutral-400 text-xs\",\n                                                                    children: \"...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 57\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: currentPage === page ? \"default\" : \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handlePageChange(page),\n                                                                    className: \"h-6 w-6 p-0 text-xs \".concat(currentPage === page ? \"bg-primary text-white\" : \"hover:bg-neutral-50\"),\n                                                                    children: page\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 49\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handlePageChange(Math.min(totalPages, currentPage + 1)),\n                                                        disabled: currentPage === totalPages,\n                                                        className: \"h-6 w-6 p-0 text-xs\",\n                                                        children: \"→\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 37\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-neutral-500\",\n                                                children: \"Single page\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"size-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                placeholder: \"Search and find people\",\n                                                // value={searchQuery} // searchQuery is removed\n                                                // onChange={e => setSearchQuery(e.target.value)} // searchQuery is removed\n                                                onKeyDown: (e)=>{\n                                                    if (e.key === \"Enter\") {\n                                                        handleSearch();\n                                                    }\n                                                },\n                                                className: \"text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            isSearching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_8__.Loader, {\n                                                theme: \"dark\",\n                                                className: \"size-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSearch,\n                                        disabled: isSearching,\n                                        size: \"sm\",\n                                        className: \"text-xs rounded-full h-auto px-3 py-1.5\",\n                                        children: isSearching ? \"Searching...\" : \"Search\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    hasSearched && leads.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSaveLeads,\n                                        disabled: isSavingSearch,\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        className: \"text-xs rounded-full h-auto px-3 py-1.5 gap-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.DatabaseIcon, {\n                                                className: \"size-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            isSavingSearch ? \"Saving...\" : \"Save \".concat(leads.length, \" Leads\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                lineNumber: 389,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_workspace_main_lead_generation_filters_people__WEBPACK_IMPORTED_MODULE_9__.PeopleFilter, {\n                        forceSidebar: true,\n                        onFilterChange: handleFilterChange,\n                        excludeMyLeads: excludeMyLeads,\n                        onExcludeMyLeadsChange: (value)=>{\n                            setExcludeMyLeads(value);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 29\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: filteredLeads.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.MagnifyingGlassIcon, {\n                                        className: \"size-12 text-neutral-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-neutral-900 mb-2\",\n                                        children: hasSearched ? \"No people found\" : \"Ready to find people\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-neutral-500 mb-4 text-sm\",\n                                        children: hasSearched ? \"Try adjusting your search query or filters to find more results\" : \"Use the search box above or apply filters on the left to discover people. Click the Search button to start.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                className: \"size-full scrollBlockChild\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_10__.PeopleTableHeader, {\n                                                selectedLeads: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads) || [],\n                                                filteredLeads: filteredLeads,\n                                                handleSelectAll: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectAll) || (()=>{})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                                children: (()=>{\n                                                    return filteredLeads.map((lead)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                            className: \"border-b border-neutral-200 hover:bg-neutral-50 transition-colors group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"w-12 px-3 relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__.Checkbox, {\n                                                                        checked: leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id),\n                                                                        onCheckedChange: (checked)=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectLead(lead.id, checked),\n                                                                        className: \"\".concat((leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id)) ? \"opacity-100 visible\" : \"invisible opacity-0 group-hover:opacity-100 group-hover:visible\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0\",\n                                                                        onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleNameClick(lead),\n                                                                        children: lead.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1 text-xs text-muted-foreground\",\n                                                                    children: lead.jobTitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1 text-xs text-muted-foreground\",\n                                                                    children: lead.company\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1\",\n                                                                    children: lead.email && lead.email !== \"unlock\" ? // Show unlocked email\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                                        title: lead.email,\n                                                                        children: lead.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 49\n                                                                    }, undefined) : // Show unlock button\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                                        icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.EnvelopeIcon,\n                                                                        onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockEmail(lead.id),\n                                                                        children: \"Unlock Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1\",\n                                                                    children: lead.phone && lead.phone !== \"unlock\" ? // Show unlocked phone\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                                        title: lead.phone,\n                                                                        children: lead.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 49\n                                                                    }, undefined) : // Show unlock button\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                                        icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.PhoneIcon,\n                                                                        onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockPhone(lead.id),\n                                                                        children: \"Unlock Mobile\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"px-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewLinksModal, {\n                                                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0\",\n                                                                            children: [\n                                                                                \"View links\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.ChevronRightIcon, {\n                                                                                    className: \"size-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                                    lineNumber: 620,\n                                                                                    columnNumber: 57\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                            lineNumber: 618,\n                                                                            columnNumber: 53\n                                                                        }, void 0),\n                                                                        links: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getContactLinks(lead)) || []\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 616,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 41\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                    className: \"w-12 px-2 relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeadActionsDropdown, {\n                                                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.EllipsisVerticalIcon, {\n                                                                                className: \"size-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 57\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                            lineNumber: 629,\n                                                                            columnNumber: 53\n                                                                        }, void 0),\n                                                                        leadData: lead,\n                                                                        onSendEmail: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleSendEmail(lead.id, lead),\n                                                                        onAddToSegments: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToSegments(lead.id),\n                                                                        onAddToDatabase: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToDatabase(lead.id, lead),\n                                                                        onAddToWorkflow: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToWorkflow(lead.id)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 45\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            ]\n                                                        }, lead.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 37\n                                                        }, undefined));\n                                                })()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b border-neutral-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                        lineNumber: 535,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                lineNumber: 522,\n                columnNumber: 13\n            }, undefined),\n            (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.Dialog, {\n                open: saveDialogOpen,\n                onOpenChange: setSaveDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.DialogContent, {\n                    className: \"max-w-[95vw] sm:max-w-[600px] !rounded-none p-4\",\n                    \"aria-describedby\": \"save-search-description\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.DialogTitle, {\n                                children: \"Save Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    id: \"save-search-description\",\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: \"Enter a name for this saved search:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: searchName,\n                                    onChange: (e)=>setSearchName(e.target.value),\n                                    placeholder: \"Enter search name\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === \"Enter\") {\n                                            handleConfirmSave();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                    lineNumber: 674,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_16__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: ()=>setSaveDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: handleConfirmSave,\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\people\\\\find-leads.tsx\",\n                lineNumber: 667,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(FindLeads, \"zwt0g14gGZIrDzOVWqIYR9d0VQQ=\", false, function() {\n    return [\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_11__.useParams,\n        _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_13__.useWorkspace,\n        _ui_providers_user__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        _ui_providers_alert__WEBPACK_IMPORTED_MODULE_15__.useAlert\n    ];\n});\n_c = FindLeads;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FindLeads);\nvar _c;\n$RefreshReg$(_c, \"FindLeads\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/people/find-leads.tsx\n"));

/***/ })

});