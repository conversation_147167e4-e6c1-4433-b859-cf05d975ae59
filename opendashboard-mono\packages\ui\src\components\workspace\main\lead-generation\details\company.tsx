"use client"

import React, { useState, useEffect, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON> } from "react"
import { <PERSON><PERSON> } from "@ui/components/ui/button"
import { ChevronDownIcon, ChevronUpIcon, GearIcon, LinkIcon, ArrowLeftIcon, BriefcaseIcon, CrownIcon, StarIcon, UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, GlobeIcon, CalendarIcon, BuildingIcon, UsersIcon, DollarSignIcon, RocketIcon, ChartLineIcon } from "@ui/components/icons/FontAwesomeRegular"
import { ExternalLinkIcon } from "@radix-ui/react-icons"
import { HandThumbUpIcon, HandThumbDownIcon, CheckCircleIcon, XCircleIcon, ClockIcon, AcademicCapIcon, BriefcaseIcon as BriefcaseSolidIcon } from "@heroicons/react/24/outline"
import { cn } from "@ui/lib/utils"
import { useScreenSize } from "@ui/providers/screenSize"
import { getLead, sendEmailToLead } from "@ui/api/leads"
import { Lead, LeadMeta } from "@ui/typings/lead"
import { useAlert } from "@ui/providers/alert"
import { VoteLeadModal } from "../VoteLeadModal"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { getMetaProperty,  getMetaArrayProperty,  getApolloProperty,  formatDate,  getTimeAgo,  formatRevenue,  formatGrowthPercentage,safeText, getNestedProperty} from "../../../../../utils/lead"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@ui/components/ui/dialog"
import { Input } from "@ui/components/ui/input"
import { Textarea } from "@ui/components/ui/textarea"
import { Loader } from "@ui/components/custom-ui/loader"


interface DetailsProps {
  leadId?: string
  token?: string
  workspaceId?: string
  onBack?: () => void
  handleSendEmail?: (leadId: string, leadData?: any) => void
}



export const Details = forwardRef<{ handleUnlockSuccess: (unlockedLead: any) => void }, DetailsProps>(({ 
  leadId,
  token,
  workspaceId,
  onBack,
  handleSendEmail: parentHandleSendEmail
}, ref) => {
  const { isMobile } = useScreenSize()
  const { toast } = useAlert()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [leadData, setLeadData] = useState<Lead | null>(null)
  const [voteModalOpen, setVoteModalOpen] = useState(false)
  
  // Unlock modal states
  const [emailModalOpen, setEmailModalOpen] = useState(false)
  const [phoneModalOpen, setPhoneModalOpen] = useState(false)
  
  // Send email modal states
  const [sendEmailDialogOpen, setSendEmailDialogOpen] = useState(false)
  const [emailSubject, setEmailSubject] = useState('')
  const [emailBody, setEmailBody] = useState('')
  const [sendingEmail, setSendingEmail] = useState(false)
  const [selectedLeadForEmail, setSelectedLeadForEmail] = useState<any>(null)
  

  
  // Fetch lead data function
  const fetchLeadData = async () => {
    if (!leadId || !token || !workspaceId) return
    
    setLoading(true)
    setError(null)
    
    try {
      const response = await getLead(token, workspaceId, leadId)
      
      if (response.isSuccess && response.data?.data) {
        setLeadData(response.data.data)
      } else {
        setError(response.error || "Failed to load lead data")
        toast.error("Error", {
          description: response.error || "Failed to load lead data"
        })
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred"
      setError(errorMessage)
      toast.error("Error", {
        description: errorMessage
      })
    } finally {
      setLoading(false)
    }
  }

  // Fetch lead data on mount
  useEffect(() => {
    fetchLeadData()
  }, [leadId, token, workspaceId, toast])

  // Handle unlock success by refreshing lead data
  const handleUnlockSuccess = (unlockedLead: any) => {
    
    if (unlockedLead) {
      // For company leads, the normalizedData might be nested inside apolloData
      // Let's ensure we have the correct structure
      const processedLead = {
        ...unlockedLead,
        normalizedData: unlockedLead.normalizedData || unlockedLead.apolloData?.normalizedData
      }
      
      
      setLeadData(processedLead)
    } else {
      // If no unlocked lead data provided, refresh from server
      fetchLeadData()
    }
  }

  // Expose handleUnlockSuccess to parent component
  useImperativeHandle(ref, () => ({
    handleUnlockSuccess
  }), [])
  
  const handleVoteClick = () => {
    if (!leadId) {
      toast.error("Error", {
        description: "No lead selected for voting"
      })
      return
    }
    
    setVoteModalOpen(true)
  }

  const handleVoteSuccess = () => {
    toast.success("Success", {
      description: "Your vote has been recorded"
    })
    // Refresh lead data to show updated vote history
    fetchLeadData()
  }

  // Local unlock handlers that open modals
  const handleUnlockEmailClick = () => {
    setEmailModalOpen(true)
  }

  const handleUnlockPhoneClick = () => {
    setPhoneModalOpen(true)
  }

  // Handle unlock success from modals
  const handleUnlockSuccessFromModal = (unlockedLead: any) => {
    handleUnlockSuccess(unlockedLead)
  }

  
  const handleSendEmail = () => {
    
    if (!leadData) {
      return
    }
    
    const email = leadData.normalizedData?.email || (leadData.apolloData as any)?.email || leadData.email
    const isEmailVisible = leadData.normalizedData?.isEmailVisible || (leadData.apolloData as any)?.normalizedData?.isEmailVisible
    
    if (!email || !isEmailVisible) {
      toast.error("You have to unlock the email first before sending an email.")
      return
    }
    
    setSelectedLeadForEmail({ ...leadData, email })
    setEmailSubject('')
    setEmailBody('')
    setSendEmailDialogOpen(true)
  }
  
  const handleConfirmSendEmail = async () => {
    if (!selectedLeadForEmail || !emailSubject.trim() || !emailBody.trim()) {
      toast.error("Please fill in both subject and body")
      return
    }

    setSendingEmail(true)
    try {
      const response = await sendEmailToLead(token || '', workspaceId || '', selectedLeadForEmail.id, {
        subject: emailSubject.trim(),
        body: emailBody.trim()
      })
      
      if (response.isSuccess) {
        toast.success("Email sent successfully!")
        setSendEmailDialogOpen(false)
        setEmailSubject('')
        setEmailBody('')
        setSelectedLeadForEmail(null)
      } else {
        toast.error(response.error || "Failed to send email")
      }
    } catch (error) {
      console.error("Failed to send email:", error)
      toast.error("Failed to send email")
    } finally {
      setSendingEmail(false)
    }
  }

  // Loading state
  if (loading) {
  return (
      <div className="w-full h-full bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-slate-600 font-medium">Loading company details...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="w-full h-full bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <XCircleIcon className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold text-red-900 mb-2">Error loading company details</h3>
          <p className="text-red-700 mb-6">{error}</p>
          <Button variant="outline" onClick={onBack} className="border-red-300 text-red-700 hover:bg-red-50">
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  // Loading state protection - don't render if data isn't ready
  if (!leadData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading lead data...</p>
        </div>
      </div>
    );
  }

  // Enhanced type guard - handle both explicit "company" type and data structure detection
  const hasCompanyData = !!leadData?.apolloData?.name && 
                        !!(leadData?.apolloData as any)?.website_url && 
                        !!(leadData?.apolloData as any)?.organization_revenue;
  
  const isCompanyLead = leadData?.type === 'company' || hasCompanyData;
  const apolloCompanyData = isCompanyLead ? (leadData.apolloData as any) : null;
  

  // Main content
  return (
    <div className="w-full h-full bg-white overflow-auto">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-md border-b border-neutral-300">
        <div className="flex items-center justify-between px-6 py-1.5">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack} className="p-1.5 hover:bg-slate-100">
              <ArrowLeftIcon className="w-4 h-4" />
            </Button>
          <div>
              <h1 className="text-base font-semibold text-slate-900 leading-tight">
                {apolloCompanyData?.name || leadData?.normalizedData?.name || "Company Details"}
              </h1>
              <p className="text-xs text-slate-500 leading-tight">
                {apolloCompanyData?.primary_domain || "Domain not specified"}
              </p>
          </div>
        </div>

          <div className="flex items-center gap-2">
            <Button size="sm" className="bg-black hover:bg-gray-800 text-white px-3 py-1.5 text-xs" onClick={handleSendEmail}>
              <EnvelopeIcon className="w-3 h-3 mr-1.5" />
              Send Email
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4">
        <div className="w-full">
          {/* Hero Section */}
          <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Company Logo & Basic Info */}
              <div className="flex flex-col lg:flex-row items-start gap-4">
                {/* Company Logo */}
                <div className="relative">
                  {apolloCompanyData?.logo_url ? (
                    <img 
                      src={String(apolloCompanyData.logo_url)} 
                      alt={apolloCompanyData?.name || "Company Logo"} 
                      className="w-20 h-20 rounded-xl object-cover border-2 border-slate-100"
                    />
                  ) : (
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center border-2 border-slate-100">
                      <BuildingIcon className="w-5 h-5 text-white" />
                    </div>
                  )}
                  
                  {/* Company Status Indicator */}
                  <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                  </div>
                </div>

                {/* Basic Information */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 text-sm text-slate-600 mb-3">
                    <GlobeIcon className="w-4 h-4" />
                    <span>{apolloCompanyData?.website_url || "Website not available"}</span>
                  </div>

                  {/* Location & Founded Year */}
                  <div className="flex items-center gap-4 text-xs text-slate-500">
                    {apolloCompanyData?.founded_year && (
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="w-3 h-3" />
                        <span>Founded {apolloCompanyData.founded_year}</span>
                      </div>
                    )}
                    
                    {apolloCompanyData?.primary_domain && (
                      <div className="flex items-center gap-1">
                        <GlobeIcon className="w-3 h-3" />
                        <span>{apolloCompanyData.primary_domain}</span>
                    </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Side - Stats & Actions */}
              <div className="lg:ml-auto flex flex-col gap-4">
                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white rounded-xl p-4 text-center border border-neutral-300">
                    <div className="text-2xl font-bold text-slate-900">
                      {formatRevenue(apolloCompanyData?.organization_revenue)}
                    </div>
                    <div className="text-xs text-slate-600 font-medium">Revenue</div>
          </div>

                  <div className="bg-white rounded-xl p-4 text-center border border-neutral-300">
                    <div className="text-2xl font-bold text-slate-900">
                      {apolloCompanyData?.organization_headcount_twenty_four_month_growth ? 
                        formatGrowthPercentage(apolloCompanyData.organization_headcount_twenty_four_month_growth) : 
                        'N/A'
                      }
              </div>
                    <div className="text-xs text-slate-600 font-medium">24M Growth</div>
            </div>
                </div>


              </div>
            </div>

            {/* Company Description */}
            {apolloCompanyData?.short_description && (
              <div className="mt-6 pt-6 border-t border-neutral-300">
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Company Description</h3>
                                  <p className="text-sm text-slate-600 leading-relaxed">
                  {apolloCompanyData.short_description}
                </p>
              </div>
            )}
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content - Left Column */}
            <div className="lg:col-span-2 space-y-6">
              {/* Company Information */}
              <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-slate-900">Company Information</h3>
                </div>
                
                {/* Company Header with Logo */}
                <div className="flex items-center gap-4 mb-6 p-4 bg-white rounded-xl border border-neutral-300">
                  {apolloCompanyData?.logo_url && (
                    <img 
                      src={apolloCompanyData.logo_url} 
                      alt={`${apolloCompanyData.name || 'Company'} logo`}
                      className="w-16 h-16 rounded-lg object-cover border border-neutral-300"
                    />
                  )}
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-slate-900">
                      {apolloCompanyData?.name || 'N/A'}
                    </h4>
                    {apolloCompanyData?.primary_domain && (
                      <p className="text-sm text-slate-600">
                        {apolloCompanyData.primary_domain}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-semibold text-slate-700 mb-2">Company Details</h4>
                      <div className="space-y-2 text-sm text-slate-600">
                        <div className="flex items-center gap-2">
                          <BuildingIcon className="w-4 h-4 text-slate-400" />
                          <span>{String(leadData?.apolloData?.name || 'N/A')}</span>
                        </div>
                        {apolloCompanyData?.website_url && (
                          <div className="flex items-center gap-2">
                            <GlobeIcon className="w-4 h-4 text-slate-400" />
                            <a 
                              href={apolloCompanyData.website_url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-700 underline"
                            >
                              Visit Website
                            </a>
                          </div>
                        )}
                        {leadData?.normalizedData?.isPhoneVisible && apolloCompanyData?.phone && (
                          <div className="flex items-center gap-2">
                            <PhoneIcon className="w-4 h-4 text-slate-400" />
                            <span>{apolloCompanyData.phone}</span>
                          </div>
                        )}
                        {leadData?.normalizedData?.isPhoneVisible && apolloCompanyData?.primary_phone && (apolloCompanyData.primary_phone as any).number && (
                          <div className="flex items-center gap-2">
                            <PhoneIcon className="w-4 h-4 text-slate-400" />
                            <span className="text-sm text-slate-600">
                              {(apolloCompanyData.primary_phone as any).number}
                              <span className="text-xs text-slate-400 ml-2">
                                (Source: {(apolloCompanyData.primary_phone as any).source})
                              </span>
                            </span>
                          </div>
                        )}
                      </div>
        </div>

                    {/* Additional Company URLs */}
                    {(apolloCompanyData?.blog_url || apolloCompanyData?.angellist_url || apolloCompanyData?.crunchbase_url) && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Additional Links</h4>
                        <div className="space-y-2 text-sm text-slate-600">
                          {apolloCompanyData?.blog_url && (
                  <div className="flex items-center gap-2">
                              <LinkIcon className="w-4 h-4 text-slate-400" />
                              <a 
                                href={apolloCompanyData.blog_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-700 underline"
                              >
                                Blog
                              </a>
              </div>
                          )}
                          {apolloCompanyData?.angellist_url && (
                            <div className="flex items-center gap-2">
                              <LinkIcon className="w-4 h-4 text-slate-400" />
                              <a 
                                  href={apolloCompanyData.angellist_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-700 underline"
                              >
                                AngelList
                              </a>
            </div>
                          )}
                          {apolloCompanyData?.crunchbase_url && (
                            <div className="flex items-center gap-2">
                              <LinkIcon className="w-4 h-4 text-slate-400" />
                              <a 
                                href={apolloCompanyData.crunchbase_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-700 underline"
                              >
                                Crunchbase
                              </a>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    </div>
                    
                  <div className="space-y-4">
                        <div>
                      <h4 className="text-sm font-semibold text-slate-700 mb-2">Growth Metrics</h4>
                      <div className="space-y-2 text-sm text-slate-600">
                        <div className="flex justify-between">
                          <span>6 Month Growth:</span>
                          <span className="font-medium">
                            {formatGrowthPercentage(apolloCompanyData?.organization_headcount_six_month_growth)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>12 Month Growth:</span>
                          <span className="font-medium">
                            {formatGrowthPercentage(apolloCompanyData?.organization_headcount_twelve_month_growth)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>24 Month Growth:</span>
                          <span className="font-medium">
                            {formatGrowthPercentage(apolloCompanyData?.organization_headcount_twenty_four_month_growth)}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                        <div>
                      <h4 className="text-sm font-semibold text-slate-700 mb-2">Company Details</h4>
                      <div className="space-y-2 text-sm text-slate-600">
                        {apolloCompanyData?.founded_year && (
                          <div className="flex justify-between">
                            <span>Founded:</span>
                            <span className="font-medium">
                              {safeText(apolloCompanyData.founded_year)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.primary_domain && (
                          <div className="flex justify-between">
                            <span>Domain:</span>
                            <span className="font-medium">
                              {String(apolloCompanyData.primary_domain)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.linkedin_uid && (
                          <div className="flex justify-between">
                            <span>LinkedIn ID:</span>
                            <span className="font-medium">
                              {String(apolloCompanyData.linkedin_uid)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.alexa_ranking && (
                          <div className="flex justify-between">
                            <span>Alexa Ranking:</span>
                            <span className="font-medium">
                                #{String(apolloCompanyData.alexa_ranking).toLocaleString()}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.organization_revenue && (
                          <div className="flex justify-between">
                            <span>Revenue:</span>
                            <span className="font-medium">
                              {formatRevenue(apolloCompanyData.organization_revenue)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.market_cap && (
                          <div className="flex justify-between">
                            <span>Market Cap:</span>
                            <span className="font-medium">
                              {String(apolloCompanyData.market_cap)}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.estimated_num_employees && (
                          <div className="flex justify-between">
                            <span>Employees:</span>
                            <span className="font-medium">
                              {Number(apolloCompanyData.estimated_num_employees).toLocaleString()}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.retail_location_count !== undefined && apolloCompanyData.retail_location_count > 0 && (
                          <div className="flex justify-between">
                            <span>Retail Locations:</span>
                            <span className="font-medium">
                              {Number(apolloCompanyData.retail_location_count).toLocaleString()}
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.publicly_traded_symbol && (
                          <div className="flex justify-between">
                            <span>Public Trading:</span>
                            <span className="font-medium">
                              {String(apolloCompanyData.publicly_traded_symbol)} 
                              ({String(apolloCompanyData.publicly_traded_exchange || 'Unknown Exchange')})
                            </span>
                          </div>
                        )}
                        {apolloCompanyData?.owned_by_organization && typeof apolloCompanyData.owned_by_organization === 'object' && 'name' in apolloCompanyData.owned_by_organization && (
                          <div className="flex justify-between">
                            <span>Parent Company:</span>
                            <span className="font-medium">
                              {apolloCompanyData.owned_by_organization.name}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Company Languages */}
                    {apolloCompanyData?.languages && Array.isArray(apolloCompanyData.languages) && apolloCompanyData.languages.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Languages</h4>
                        <div className="flex flex-wrap gap-2">
                          {apolloCompanyData.languages.map((lang: any, index: number) => (
                            <span key={index} className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full font-medium">
                              {String(lang)}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {/* Intent Signals */}
                    {apolloCompanyData?.show_intent && (
                      <div>
                        <h4 className="text-sm font-semibold text-slate-700 mb-2">Intent Signals</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span>Shows Intent:</span>
                            <span className="font-medium text-green-600">
                              {apolloCompanyData.show_intent ? 'Yes' : 'No'}
                            </span>
                          </div>
                          {apolloCompanyData?.intent_strength && (
                            <div className="flex justify-between">
                              <span>Intent Strength:</span>
                              <span className="font-medium">
                                {String(apolloCompanyData.intent_strength)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Company Technologies */}
              {apolloCompanyData?.current_technologies && Array.isArray(apolloCompanyData.current_technologies) && apolloCompanyData.current_technologies.length > 0 && (
                <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Company Technologies</h3>
                  <div className="flex flex-wrap gap-2">
                    {apolloCompanyData.current_technologies.map((tech: any, index: number) => (
                      <span key={index} className="px-3 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full font-medium">
                        {String(tech)}
                          </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Social Media Links */}
              {(apolloCompanyData?.linkedin_url || apolloCompanyData?.twitter_url || apolloCompanyData?.facebook_url) && (
                <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Social Media</h3>
                  <div className="flex flex-wrap gap-4">
                    {apolloCompanyData?.linkedin_url && (
                      <a 
                        href={String(apolloCompanyData.linkedin_url)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
                      >
                        <span>LinkedIn</span>
                        <ExternalLinkIcon className="w-4 h-4" />
                      </a>
                    )}
                      {apolloCompanyData?.twitter_url && (
                      <a 
                        href={String(apolloCompanyData.twitter_url)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
                      >
                        <span>Twitter</span>
                        <ExternalLinkIcon className="w-4 h-4" />
                      </a>
                    )}
                    {apolloCompanyData?.facebook_url && (
                      <a 
                        href={String(apolloCompanyData.facebook_url)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
                      >
                        <span>Facebook</span>
                        <ExternalLinkIcon className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Sidebar - Right Column */}
            <div className="space-y-6">
              {/* Contact Information */}
              <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Contact Information</h3>
                <div className="space-y-4">
                  {/* Email */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <EnvelopeIcon className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-700">Email</span>
                    </div>
                    {leadData?.normalizedData?.isEmailVisible && leadData.normalizedData?.email ? (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-slate-600">{leadData.normalizedData.email}</span>
                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                      </div>
                    ) : (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-xs rounded-full w-full"
                        onClick={handleUnlockEmailClick}
                      >
                        <EnvelopeIcon className="w-3 h-3 mr-2" />
                        Unlock Email
                      </Button>
                    )}
                  </div>

                  {/* Phone */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <PhoneIcon className="w-4 h-4 text-slate-400" />
                      <span className="text-sm font-medium text-slate-700">Phone</span>
                    </div>
                    {leadData?.normalizedData?.isPhoneVisible && leadData.normalizedData?.phone ? (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-slate-600">{leadData.normalizedData.phone}</span>
                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                      </div>
                    ) : (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-xs rounded-full w-full"
                        onClick={handleUnlockPhoneClick}
                      >
                        <PhoneIcon className="w-3 h-3 mr-2" />
                        Unlock Phone
                      </Button>
                    )}
                  </div>

                  {/* Website */}
                  {apolloCompanyData?.website_url && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <GlobeIcon className="w-4 h-4 text-slate-400" />
                        <span className="text-sm font-medium text-slate-700">Website</span>
                      </div>
                      <a 
                        href={String(apolloCompanyData.website_url)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700"
                      >
                        <span>Visit Website</span>
                        <ExternalLinkIcon className="w-3 h-3" />
                      </a>
                    </div>
                  )}
                </div>
              </div>

              {/* Lead Status */}
              <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Lead Status</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Status:</span>
                    <span className="text-sm font-medium text-slate-900">
                      {leadData?.isUnlocked ? 'Unlocked' : 'Locked'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Source:</span>
                    <span className="text-sm font-medium text-slate-900 capitalize">
                      {leadData?.source || 'Unknown'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Created:</span>
                    <span className="text-sm font-medium text-slate-900">
                      {leadData?.createdAt ? getTimeAgo(leadData.createdAt) : 'Unknown'}
                    </span>
                  </div>
                  {leadData?.lastEnrichedAt && (
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-600">Last Updated:</span>
                      <span className="text-sm font-medium text-slate-900">
                        {getTimeAgo(leadData.lastEnrichedAt)}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Apollo Data Insights */}
              {leadData?.apolloData && (
                <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Apollo Insights</h3>
                      <div className="space-y-3">
                    {apolloCompanyData.intent_strength && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Intent Strength:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {String(apolloCompanyData.intent_strength)}
                        </span>
                      </div>
                    )}
                    {apolloCompanyData.show_intent && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Shows Intent:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {apolloCompanyData.show_intent ? 'Yes' : 'No'}
                        </span>
                      </div>
                    )}
                    {apolloCompanyData.organization_revenue && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Revenue:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {formatRevenue(apolloCompanyData.organization_revenue)}
                        </span>
                      </div>
                    )}
                    {apolloCompanyData.market_cap && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Market Cap:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {String(apolloCompanyData.market_cap)}
                        </span>
                      </div>
                    )}
                    {apolloCompanyData.estimated_num_employees && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Employees:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {Number(apolloCompanyData.estimated_num_employees).toLocaleString()}
                        </span>
                      </div>
                    )}
                    {apolloCompanyData.industry && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Industry:</span>
                        <span className="text-sm font-medium text-slate-900 capitalize">
                          {String(apolloCompanyData.industry)}
                        </span>
                      </div>
                    )}
                    {apolloCompanyData?.secondary_industries && Array.isArray(apolloCompanyData.secondary_industries) && apolloCompanyData.secondary_industries.length > 0 && (
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Secondary Industries:</span>
                        <span className="text-sm font-medium text-slate-900">
                          {apolloCompanyData.secondary_industries.join(', ')}
                        </span>
                      </div>
                    )}
                  </div>
              </div>
            )}

              {/* Vote Lead */}
              <div className="bg-white rounded-2xl shadow-sm border border-neutral-300 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Rate This Company</h3>
                <p className="text-sm text-slate-600 mb-4">
                  Help improve lead quality by rating this company
                </p>

                <Button 
                  onClick={() => handleVoteClick()}
                  className="w-full bg-black hover:bg-gray-800 text-white"
                >
                  <HandThumbUpIcon className="w-4 h-4 mr-2" />
                  Rate with Feedback
                </Button>
                        </div>

            </div>
          </div>
        </div>
      </div>
      
      {/* Vote Modal */}
            {leadId && (
        <VoteLeadModal
          open={voteModalOpen}
          onOpenChange={setVoteModalOpen}
          leadId={leadId}
          onVoteSuccess={handleVoteSuccess}
        />
      )}

      {/* Unlock Email Modal */}
      {leadId && (
        <UnlockEmailModal
          open={emailModalOpen}
          onOpenChange={setEmailModalOpen}
          leadId={leadId}
          onUnlockSuccess={handleUnlockSuccessFromModal}
        />
      )}

      {/* Unlock Phone Modal */}
      {leadId && (
        <UnlockPhoneModal
          open={phoneModalOpen}
          onOpenChange={setPhoneModalOpen}
          leadId={leadId}
          onUnlockSuccess={handleUnlockSuccessFromModal}
        />
      )}
      
      <Dialog open={sendEmailDialogOpen} onOpenChange={setSendEmailDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Send Email</DialogTitle>
            <DialogDescription>
              Send an email to {selectedLeadForEmail?.normalizedData?.name || selectedLeadForEmail?.apolloData?.name || 'this lead'}
              {selectedLeadForEmail?.email ? ` (${selectedLeadForEmail.email})` : ''}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Subject:</label>
                <Input
                  value={emailSubject}
                  onChange={(e) => setEmailSubject(e.target.value)}
                  placeholder="Enter email subject"
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Message:</label>
                <Textarea
                  value={emailBody}
                  onChange={(e) => setEmailBody(e.target.value)}
                  placeholder="Enter your message"
                  className="mt-1 min-h-[100px]"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setSendEmailDialogOpen(false)}
              disabled={sendingEmail}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmSendEmail}
              disabled={!emailSubject.trim() || !emailBody.trim() || sendingEmail}
              className="gap-2"
            >
              {sendingEmail ? (
                <>
                  <Loader theme="dark" className="size-4" />
                  Sending...
                </>
              ) : (
                <>
                  <EnvelopeIcon className="size-4" />
                  Send Email
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
})
