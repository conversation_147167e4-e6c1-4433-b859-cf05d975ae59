"use client"

import React from "react"
import { HandThumbUpIcon, HandThumbDownIcon } from "@heroicons/react/24/outline"
import { HandThumbUpIcon as HandThumbUpSolidIcon, HandThumbDownIcon as HandThumbDownSolidIcon } from "@heroicons/react/24/solid"
import { cn } from "@ui/lib/utils"
import { LeadVoteFeedback } from "@ui/typings/lead"

interface VoteHistoryProps {
  votes?: LeadVoteFeedback[]
  className?: string
}

export const VoteHistory = ({ votes = [], className }: VoteHistoryProps) => {
  if (!votes || votes.length === 0) {
    return null
  }

  // Get the most recent vote
  const latestVote = votes[votes.length - 1]
  const upvotes = votes.filter(vote => vote.vote === 'up').length
  const downvotes = votes.filter(vote => vote.vote === 'down').length

  // Helper function to get the vote date (handles both createdAt and votedAt)
  const getVoteDate = (vote: LeadVoteFeedback) => {
    return (vote as any).votedAt || vote.createdAt
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Vote Summary */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <HandThumbUpSolidIcon className="w-4 h-4 text-green-600" />
          <span className="text-sm font-medium text-neutral-900">{upvotes}</span>
          <span className="text-xs text-neutral-500">upvotes</span>
        </div>
        <div className="flex items-center gap-2">
          <HandThumbDownSolidIcon className="w-4 h-4 text-red-600" />
          <span className="text-sm font-medium text-neutral-900">{downvotes}</span>
          <span className="text-xs text-neutral-500">downvotes</span>
        </div>
      </div>

      {/* Latest Vote Details */}
      <div className="border border-neutral-200 rounded-lg p-3">
        <div className="flex items-start gap-3">
          <div className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center",
            latestVote.vote === 'up' 
              ? "bg-green-100 text-green-600" 
              : "bg-red-100 text-red-600"
          )}>
            {latestVote.vote === 'up' ? (
              <HandThumbUpSolidIcon className="w-4 h-4" />
            ) : (
              <HandThumbDownSolidIcon className="w-4 h-4" />
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium text-neutral-900">
                Latest {latestVote.vote === 'up' ? 'Upvote' : 'Downvote'}
              </span>
            </div>
            
            <p className="text-sm text-neutral-700 mb-2">
              "{latestVote.feedback}"
            </p>
            
            <div className="text-xs text-neutral-500">
              {new Date(getVoteDate(latestVote)).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        </div>
      </div>

      {/* All Votes History (if more than one) */}
      {votes.length > 1 && (
        <details className="group">
          <summary className="cursor-pointer text-sm text-primary hover:text-primary/80 flex items-center gap-1">
            <span>View all {votes.length} votes</span>
            <svg 
              className="w-4 h-4 transform transition-transform group-open:rotate-180" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </summary>
          
          <div className="mt-3 space-y-2 max-h-60 overflow-y-auto">
            {votes.slice(0, -1).reverse().map((vote, index) => (
              <div key={index} className="border border-neutral-100 rounded-lg p-2 bg-neutral-50">
                <div className="flex items-start gap-2">
                  <div className={cn(
                    "w-6 h-6 rounded-full flex items-center justify-center",
                    vote.vote === 'up' 
                      ? "bg-green-100 text-green-600" 
                      : "bg-red-100 text-red-600"
                  )}>
                    {vote.vote === 'up' ? (
                      <HandThumbUpIcon className="w-3 h-3" />
                    ) : (
                      <HandThumbDownIcon className="w-3 h-3" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-xs font-medium text-neutral-700">
                        {vote.vote === 'up' ? 'Upvote' : 'Downvote'}
                      </span>
                    </div>
                    
                    <p className="text-xs text-neutral-600 mb-1">
                      "{vote.feedback}"
                    </p>
                    
                    <div className="text-xs text-neutral-400">
                      {new Date(getVoteDate(vote)).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </details>
      )}
    </div>
  )
}