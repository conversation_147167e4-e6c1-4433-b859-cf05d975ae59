"use client"

import React, { useState, useRef, useMemo } from "react"
import MyLeads from "./my-leads"
import FindLeads from "./find-leads"
import SavedSearch from "./saved-search"
import { useAuth } from "@ui/providers/user"
import { useWorkspace } from "@ui/providers/workspace"
import { But<PERSON> } from "@ui/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { EnvelopeIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon } from "@ui/components/icons/FontAwesomeRegular"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@ui/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Input } from "@ui/components/ui/input"
import { Textarea } from "@ui/components/ui/textarea"
import { Label } from "@ui/components/ui/label"
import { addLeadToDatabase, addLeadToSegment, addLeadToWorkflow, sendEmailToLead, unlockLead } from "@ui/api/leads"
import { getDatabases } from "@ui/api/database"
import { getWorkflows } from "@ui/api/workflow"
import { WorkflowTriggerType } from "@repo/app-db-utils/dist/typings/workflow"
import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { DatabaseSelect } from "@ui/components/workspace/main/common/databaseSelect"
import { OnDemandWorkflowSelect } from "@ui/components/workspace/main/common/onDemandWorkflowSelect"
import { TwoStepDatabaseMapping } from "../common/TwoStepDatabaseMapping"
import { Loader } from "@ui/components/custom-ui/loader"

interface CompaniesProps {
    activeSubTab: 'my-leads' | 'find-leads' | 'saved-search'
    onLeadCreated?: (lead: any) => void
}


export const ActionButton = ({ 
    icon: Icon, 
    children, 
    onClick 
}: { 
    icon: React.ComponentType<{ className?: string }>; 
    children: React.ReactNode; 
    onClick: () => void; 
}) => (
    <Button
        variant="outline"
        size="sm"
        onClick={onClick}
        className="text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center"
    >
        <Icon className="size-3" />
        <span className="truncate">{children}</span>
    </Button>
)

export const ViewLinksModal = ({ 
    trigger,
    links 
}: { 
    trigger: React.ReactNode;
    links: Array<{ id: string; title: string; url: string }> 
}) => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
                {trigger}
            </PopoverTrigger>
            <PopoverContent className="w-64 p-4" align="end">
                <h4 className="font-semibold text-sm mb-2">Social Links</h4>
                <div className="space-y-2">
                    {links.length === 0 ? (
                        <div className="text-xs text-muted-foreground">No links available</div>
                    ) : (
                        links.map((link) => (
                            <a
                                key={link.id}
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors p-2 rounded hover:bg-blue-50"
                            >
                                <UpRightFromSquareIcon className="size-3" />
                                <span className="truncate">{link.title}</span>
                            </a>
                        ))
                    )}
                </div>
            </PopoverContent>
        </Popover>
    )
}

interface LeadActionsDropdownProps {
    trigger: React.ReactNode
    onSendEmail?: () => void
    onAddToSegments?: () => void
    onAddToDatabase?: () => void
    onAddToWorkflow?: () => void
    leadData?: any
}

export const LeadActionsDropdown = ({ 
    trigger, 
    onSendEmail, 
    onAddToSegments, 
    onAddToDatabase, 
    onAddToWorkflow,
    leadData 
}: LeadActionsDropdownProps) => {

    const email = leadData?.normalizedData?.email || leadData?.apolloData?.email || leadData?.email
    const hasEmail = !!email
    
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                {trigger}
            </DropdownMenuTrigger>
            <DropdownMenuContent 
                className="w-56 rounded-none text-neutral-800 font-semibold"
                align="end"
                sideOffset={4}
            >
                <DropdownMenuGroup className="p-1 flex flex-col gap-2">
                    <DropdownMenuItem 
                        className={`text-xs rounded-none p-2 py-1.5 flex items-center gap-2 ${hasEmail ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
                        onClick={hasEmail ? onSendEmail : undefined}
                        disabled={!hasEmail}
                    >
                        <EnvelopeIcon className="size-3 text-neutral-600" />
                        <span>Send Email</span>
                        {!hasEmail && <span className="ml-auto text-xs text-gray-400">(Locked)</span>}
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToSegments}
                    >
                        <ChartLineIcon className="size-3 text-neutral-600" />
                        <span>Add to Segments</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToDatabase}
                    >
                        <DatabaseIcon className="size-3 text-neutral-600" />
                        <span>Add to Database</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToWorkflow}
                    >
                        <CodeMergeIcon className="size-3 text-neutral-600" />
                        <span>Add to Workflow</span>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}


export const CompanyTableHeader = ({ 
    selectedLeads, 
    filteredLeads, 
    handleSelectAll 
}: { 
    selectedLeads: string[]
    filteredLeads: any[]
    handleSelectAll: (checked: boolean, leads: any[]) => void
}) => (
    <TableHeader>
        <TableRow className="border-b border-neutral-200 bg-white sticky top-0 z-10">
            <TableHead className="w-12 h-10 px-3">
                <Checkbox
                    checked={selectedLeads.length === filteredLeads.length && filteredLeads.length > 0}
                    onCheckedChange={(checked: boolean) => handleSelectAll(checked, filteredLeads)}
                />
            </TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Name</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Industry</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Location</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Email</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Phone number</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Social Links</TableHead>
            <TableHead className="w-12 h-10 px-1"></TableHead>
        </TableRow>
    </TableHeader>
)


export const useLeadManagement = () => {
    const { toast } = useAlert()
    const { token } = useAuth()
    const { workspace } = useWorkspace()
    const router = useRouter()
    const params = useParams()
    

    const [selectedLeads, setSelectedLeads] = useState<string[]>([])
    const [emailModalOpen, setEmailModalOpen] = useState(false)
    const [phoneModalOpen, setPhoneModalOpen] = useState(false)
    const [selectedLeadId, setSelectedLeadId] = useState<string | null>(null)
    

    const unlockEmailCallbackRef = useRef<((data: any) => void) | null>(null)
    const unlockPhoneCallbackRef = useRef<((data: any) => void) | null>(null)
    

    const [addToDatabaseDialogOpen, setAddToDatabaseDialogOpen] = useState(false)
    const [selectedLeadForDatabase, setSelectedLeadForDatabase] = useState<any>(null)
    const [addingToDatabase, setAddingToDatabase] = useState(false)
    const [selectedLeadIdForAction, setSelectedLeadIdForAction] = useState<string | null>(null)
    

    const [sendEmailDialogOpen, setSendEmailDialogOpen] = useState(false)
    const [emailSubject, setEmailSubject] = useState('')
    const [emailBody, setEmailBody] = useState('')
    const [sendingEmail, setSendingEmail] = useState(false)
    const [selectedLeadForEmail, setSelectedLeadForEmail] = useState<any>(null)


    const [addToSegmentDialogOpen, setAddToSegmentDialogOpen] = useState(false)
    const [segmentName, setSegmentName] = useState('')
    const [addingToSegment, setAddingToSegment] = useState(false)


    const [addToWorkflowDialogOpen, setAddToWorkflowDialogOpen] = useState(false)
    const [selectedWorkflowId, setSelectedWorkflowId] = useState('')
    const [addingToWorkflow, setAddingToWorkflow] = useState(false)
    const [availableWorkflows, setAvailableWorkflows] = useState<any[]>([])
    const [loadingWorkflows, setLoadingWorkflows] = useState(false)
    

    const handleSelectAll = (checked: boolean, leads: any[]) => {
        if (checked) {
            setSelectedLeads(leads.map(lead => lead.id))
        } else {
            setSelectedLeads([])
        }
    }

    const handleSelectLead = (leadId: string, checked: boolean) => {
        if (checked) {
            setSelectedLeads([...selectedLeads, leadId])
        } else {
            setSelectedLeads(selectedLeads.filter(id => id !== leadId))
        }
    }


    const handleUnlockEmail = (leadId: string) => {
        setSelectedLeadId(leadId)
        setEmailModalOpen(true)
    }

    const handleUnlockPhone = (leadId: string) => {
        setSelectedLeadId(leadId)
        setPhoneModalOpen(true)
    }


    const handleNameClick = (lead: any) => {
        const domain = params.domain
        router.push(`/${domain}/lead-generation/company/details/${lead.id}`)
    }

    const handleViewLinks = (leadId: string) => {

    }


    const getContactLinks = (lead: any) => {
        const links = []
        

        if (lead.name) {
            links.push({ 
                id: "linkedin", 
                title: "LinkedIn Profile", 
                url: `https://linkedin.com/search/results/people/?keywords=${encodeURIComponent(lead.name + ' ' + lead.company)}`
            })
        }
        

        if (lead.company) {
            links.push({ 
                id: "company", 
                title: "Company Website", 
                url: `https://www.google.com/search?q=${encodeURIComponent(lead.company + ' official website')}`
            })
        }
        

        if (lead.company) {
            links.push({ 
                id: "company-linkedin", 
                title: "Company LinkedIn", 
                url: `https://linkedin.com/search/results/companies/?keywords=${encodeURIComponent(lead.company)}`
            })
        }
        

        if (lead.company) {
            links.push({ 
                id: "google", 
                title: "Google Search", 
                url: `https://www.google.com/search?q=${encodeURIComponent(lead.company)}`
            })
        }
        

        if (lead.company) {
            links.push({ 
                id: "employees", 
                title: "Company Employees", 
                url: `https://linkedin.com/search/results/people/?keywords=${encodeURIComponent(lead.company)}`
            })
        }
        
        return links
    }


    const handleImportLeads = () => {

    }



    const convertApiLeadsToUI = (apiLeads: any[]): any[] => {
        
        
        return apiLeads.map((apiLead) => {

            

            const isPersonData = apiLead.apolloData && (
                apiLead.apolloData.first_name || 
                apiLead.apolloData.last_name || 
                apiLead.apolloData.person ||
                apiLead.apolloData.title
            );
            

            
            let industry = '-';
            let location = '-';
            let name = 'Unknown';
            let company = 'Unknown';
            
            if (isPersonData) {

                

                industry = apiLead.apolloData?.organization?.industry || '-';
                

                if (apiLead.apolloData.city || apiLead.apolloData.state || apiLead.apolloData.country) {
                    location = [apiLead.apolloData.city, apiLead.apolloData.state, apiLead.apolloData.country]
                        .filter(Boolean)
                        .join(', ') || '-';
                }
                

                const firstName = apiLead.apolloData?.first_name || '';
                const lastName = apiLead.apolloData?.last_name || '';
                name = `${firstName} ${lastName}`.trim() || apiLead.apolloData?.name || 'Unknown Person';
                

                company = apiLead.apolloData?.organization?.name || apiLead.apolloData?.organization?.company_name || 'Unknown Company';
                
            } else {

                

                industry = apiLead.apolloData?.industry || '-';
                

                if (apiLead.apolloData) {
                    const apolloData = apiLead.apolloData;

                    if (apolloData.city || apolloData.state || apolloData.country) {
                        location = [apolloData.city, apolloData.state, apolloData.country]
                            .filter(Boolean)
                            .join(', ') || '-';
                    } else if (apolloData.location) {

                        const loc = apolloData.location;
                        if (typeof loc === 'object' && loc !== null) {
                            location = [loc.city, loc.state, loc.country]
                                .filter(Boolean)
                                .join(', ') || '-';
                        }
                    }
                }
                

                name = apiLead.normalizedData?.name || apiLead.apolloData?.name || 'Unknown Company';
                

                company = apiLead.normalizedData?.company || apiLead.apolloData?.name || name;
            }
            

            
            return {
                id: apiLead.id,
                name,
                company,
                email: apiLead.normalizedData?.isEmailVisible ? apiLead.normalizedData.email || "unlock" : "unlock",
                phone: apiLead.normalizedData?.isPhoneVisible ? apiLead.normalizedData.phone || "unlock" : "unlock",
                links: "view",
                industry,
                location
            };
        });
    }


    const getFilteredLeads = (leads: any[], searchQuery?: string, filter?: any) => {
        return useMemo(() => {
            let filtered = leads
            

            if (searchQuery?.trim()) {
                const searchTerm = searchQuery.trim().toLowerCase()
                filtered = filtered.filter(lead => 
                    Object.values(lead).some(value => 
                        typeof value === 'string' && value.toLowerCase().includes(searchTerm)
                    )
                )
            }
            

            if (filter?.conditions?.length > 0) {
                filtered = filtered.filter(lead => {
                    return filter.conditions.every((condition: any) => {
                        const value = condition.value?.toString().toLowerCase() || ''
                        const leadValue = lead[condition.columnId as keyof any]?.toString().toLowerCase() || ''
                        return leadValue.includes(value)
                    })
                })
            }
            
            return filtered
        }, [leads, searchQuery, filter])
    }

    const handleSendEmail = async (leadId: string, leadData?: any) => {
        const lead = leadData || { id: leadId, name: 'Unknown Lead' }

        const email = lead.normalizedData?.email || lead.apolloData?.email || lead.email
        const isEmailVisible = lead.normalizedData?.isEmailVisible || lead.apolloData?.normalizedData?.isEmailVisible

        if (!email || !isEmailVisible) {
            toast.error("You have to unlock the email first before sending an email.")
            return
        }

        setSelectedLeadForEmail({ ...lead, email })
        setEmailSubject('')
        setEmailBody('')
        setSendEmailDialogOpen(true)
    }

    const handleConfirmSendEmail = async () => {
        if (!selectedLeadForEmail || !emailSubject.trim() || !emailBody.trim()) {
            toast.error("Please fill in both subject and body")
            return
        }

        setSendingEmail(true)
        try {
            const response = await sendEmailToLead(token?.token || '', workspace?.workspace?.id || '', selectedLeadForEmail.id, {
                subject: emailSubject.trim(),
                body: emailBody.trim()
            })
            

            if (response.isSuccess) {
                toast.success("Email sent successfully!")
                setSendEmailDialogOpen(false)
                setEmailSubject('')
                setEmailBody('')
                setSelectedLeadForEmail(null)
            } else {
                toast.error(response.error || "Failed to send email")
            }
        } catch (error) {

            toast.error("Failed to send email")
        } finally {
            setSendingEmail(false)
        }
    }

    const handleAddToSegments = async (leadId: string) => {
        setSelectedLeadIdForAction(leadId)
        setSegmentName('')
        setAddToSegmentDialogOpen(true)
    }

    const handleConfirmAddToSegment = async () => {
        if (!selectedLeadIdForAction || !segmentName.trim()) {
            toast.error("Please enter a segment name")
            return
        }

        setAddingToSegment(true)
        try {
            await addLeadToSegment(token?.token || '', workspace?.workspace?.id || '', selectedLeadIdForAction, { 
                name: segmentName.trim() 
            })
            toast.success("Lead added to segment successfully!")
            setAddToSegmentDialogOpen(false)
            setSegmentName('')
            setSelectedLeadIdForAction('')
        } catch (error) {

            toast.error("Failed to add to segment")
        } finally {
            setAddingToSegment(false)
        }
    }

    const handleAddToDatabase = async (leadId: string, leadData?: any) => {

        const lead = leadData || { id: leadId, name: 'Unknown Company' }
        setSelectedLeadForDatabase(lead)
        setAddToDatabaseDialogOpen(true)
    }



    const handleConfirmAddToDatabase = async (mappings: any[], databaseId: string) => {
        if (!selectedLeadForDatabase || !databaseId || mappings.length === 0) return
        
        setAddingToDatabase(true)
        try {

            const response = await addLeadToDatabase(
                token?.token || '', 
                workspace?.workspace?.id || '', 
                selectedLeadForDatabase.id, 
                { 
                    targetDatabaseId: databaseId
                }
            )
            
            if (response.isSuccess) {
                toast.success("Lead added to database successfully!")
                setAddToDatabaseDialogOpen(false)
                setSelectedLeadForDatabase(null)
            } else {
                toast.error(response.error || "Failed to add lead to database")
            }
        } catch (error) {

            toast.error("Failed to add lead to database")
        } finally {
            setAddingToDatabase(false)
        }
    }

    const handleAddToWorkflow = async (leadId: string) => {
        setSelectedLeadIdForAction(leadId)

        setLoadingWorkflows(true)
        try {
            const response = await getWorkflows(token?.token || '', workspace?.workspace?.id || '')

            if (response.isSuccess && response.data?.data?.workflows) {
                const onDemandWorkflows = response.data.data.workflows.filter(w => w.triggerType === WorkflowTriggerType.OnDemand_Callable)

                if (onDemandWorkflows.length > 0) {
                    setAvailableWorkflows(onDemandWorkflows)
                } else {
                    setAvailableWorkflows([
                        { id: 'default-workflow', name: 'Default Lead Workflow' }
                    ])
                }
            } else {
                setAvailableWorkflows([
                    { id: 'default-workflow', name: 'Default Lead Workflow' }
                ])
            }
        } catch (error) {
            setAvailableWorkflows([
                { id: 'default-workflow', name: 'Default Lead Workflow' }
            ])
        } finally {
            setLoadingWorkflows(false)
        }

        setAddToWorkflowDialogOpen(true)
    }

    const handleConfirmAddToWorkflow = async () => {
        if (!selectedLeadIdForAction || !selectedWorkflowId) {
            toast.error("Please select a workflow")
            return
        }

        setAddingToWorkflow(true)
        try {
            const result = await addLeadToWorkflow(token?.token || '', workspace?.workspace?.id || '', selectedLeadIdForAction, {
                workflowId: selectedWorkflowId
            })

            if (result.isSuccess) {
                toast.success("Lead added to workflow successfully!")
                setAddToWorkflowDialogOpen(false)
                setSelectedWorkflowId('')
                setSelectedLeadIdForAction('')
            } else {
                toast.error(result.error || "Failed to add lead to workflow")
            }
        } catch (error) {
            toast.error("Failed to add to workflow")
        } finally {
            setAddingToWorkflow(false)
        }
    }

    const AddToWorkflowDialog = ({
        open,
        onOpenChange,
        selectedWorkflowId,
        setSelectedWorkflowId,
        addingToWorkflow,
        loadingWorkflows,
        availableWorkflows,
        handleConfirmAddToWorkflow
    }: {
        open: boolean
        onOpenChange: (open: boolean) => void
        selectedWorkflowId: string
        setSelectedWorkflowId: (id: string) => void
        addingToWorkflow: boolean
        loadingWorkflows: boolean
        availableWorkflows: any[]
        handleConfirmAddToWorkflow: () => void
    }) => {
        return (
            <Dialog open={open} onOpenChange={onOpenChange}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Add Lead to Workflow</DialogTitle>
                        <DialogDescription>
                            Select a workflow to add this lead to.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        {loadingWorkflows ? (
                            <div className="flex items-center justify-center py-4">
                                <div className="text-sm text-muted-foreground">Loading workflows...</div>
                            </div>
                        ) : availableWorkflows.length === 0 ? (
                            <div className="text-center py-4">
                                <div className="text-sm text-muted-foreground mb-2">No workflows available</div>
                                <div className="text-xs text-muted-foreground">
                                    Please create a workflow first.
                                </div>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium">Select Workflow:</label>
                                    <select
                                        value={selectedWorkflowId}
                                        onChange={(e) => setSelectedWorkflowId(e.target.value)}
                                        disabled={loadingWorkflows}
                                        className="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="">Choose a workflow...</option>
                                        {availableWorkflows.map((workflow) => (
                                            <option key={workflow.id} value={workflow.id}>
                                                {workflow.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                            disabled={addingToWorkflow}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirmAddToWorkflow}
                            disabled={!selectedWorkflowId || addingToWorkflow || loadingWorkflows}
                            className="gap-2"
                        >
                            {addingToWorkflow ? (
                                <>
                                    <Loader theme="dark" className="size-4" />
                                    Adding...
                                </>
                            ) : (
                                <>
                                    <CodeMergeIcon className="size-4" />
                                    Add to Workflow
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        )
    }



    const SendEmailDialog = () => {
        return (
            <Dialog open={sendEmailDialogOpen} onOpenChange={setSendEmailDialogOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>Send Email to Lead</DialogTitle>
                    </DialogHeader>
                    <div className="py-4 space-y-4">
                        {selectedLeadForEmail && (
                            <div className="p-3 bg-gray-50 rounded-md">
                                <p className="text-sm text-gray-600">
                                    <span className="font-medium">To:</span> {selectedLeadForEmail.name}
                                </p>
                                {selectedLeadForEmail.email && (
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">Email:</span> {selectedLeadForEmail.email}
                                    </p>
                                )}
                            </div>
                        )}
                        
                        <div className="space-y-2">
                            <Label htmlFor="email-subject">Subject</Label>
                            <Input
                                id="email-subject"
                                value={emailSubject}
                                onChange={(e) => setEmailSubject(e.target.value)}
                                placeholder="Enter email subject"
                                disabled={sendingEmail}
                            />
                        </div>
                        
                        <div className="space-y-2">
                            <Label htmlFor="email-body">Message</Label>
                            <Textarea
                                id="email-body"
                                value={emailBody}
                                onChange={(e) => setEmailBody(e.target.value)}
                                placeholder="Enter your message here..."
                                rows={8}
                                disabled={sendingEmail}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setSendEmailDialogOpen(false)}
                            disabled={sendingEmail}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirmSendEmail}
                            disabled={!emailSubject.trim() || !emailBody.trim() || sendingEmail}
                        >
                            {sendingEmail ? "Sending..." : "Send Email"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        )
    }

    const AddToSegmentDialog = () => {
        return (
            <Dialog open={addToSegmentDialogOpen} onOpenChange={setAddToSegmentDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Add Lead to Segment</DialogTitle>
                        <DialogDescription>
                            Enter a name for the segment to add this lead to.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <div className="space-y-2">
                            <Label htmlFor="segment-name">Segment Name</Label>
                            <Input
                                id="segment-name"
                                value={segmentName}
                                onChange={(e) => setSegmentName(e.target.value)}
                                placeholder="e.g., High Priority Leads, Q1 Prospects"
                                disabled={addingToSegment}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setAddToSegmentDialogOpen(false)}
                            disabled={addingToSegment}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirmAddToSegment}
                            disabled={!segmentName.trim() || addingToSegment}
                            className="gap-2"
                        >
                            {addingToSegment ? (
                                <>
                                    <Loader theme="dark" className="size-4" />
                                    Adding...
                                </>
                            ) : (
                                <>
                                    <ChartLineIcon className="size-4" />
                                    Add to Segment
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        )
    }


    const SharedModals = () => (
        <>
            <UnlockEmailModal
                open={emailModalOpen}
                onOpenChange={setEmailModalOpen}
                leadId={selectedLeadId}
                onUnlockSuccess={(unlockedLead) => {
                    if (unlockEmailCallbackRef.current) {
                        unlockEmailCallbackRef.current(unlockedLead)
                    }
                }}
            />
            <UnlockPhoneModal
                open={phoneModalOpen}
                onOpenChange={setPhoneModalOpen}
                leadId={selectedLeadId}
                onUnlockSuccess={(unlockedLead) => {
                    if (unlockPhoneCallbackRef.current) {
                        unlockPhoneCallbackRef.current(unlockedLead)
                    }
                }}
            />
            {selectedLeadForDatabase && (
                <TwoStepDatabaseMapping
                    open={addToDatabaseDialogOpen}
                    onOpenChange={setAddToDatabaseDialogOpen}
                    lead={selectedLeadForDatabase}
                    onConfirm={handleConfirmAddToDatabase}
                    loading={addingToDatabase}
                />
            )}
            <AddToSegmentDialog />
            <AddToWorkflowDialog
                open={addToWorkflowDialogOpen}
                onOpenChange={setAddToWorkflowDialogOpen}
                selectedWorkflowId={selectedWorkflowId}
                setSelectedWorkflowId={setSelectedWorkflowId}
                addingToWorkflow={addingToWorkflow}
                loadingWorkflows={loadingWorkflows}
                availableWorkflows={availableWorkflows}
                handleConfirmAddToWorkflow={handleConfirmAddToWorkflow}
            />
            <SendEmailDialog />
        </>
    )

    return {

        selectedLeads,
        setSelectedLeads,
        emailModalOpen,
        phoneModalOpen,
        selectedLeadId,
        

        handleSelectAll,
        handleSelectLead,
        

        handleUnlockEmail,
        handleUnlockPhone,
        

        handleNameClick,
        handleViewLinks,
        

        getContactLinks,
        

        handleImportLeads,
        

        convertApiLeadsToUI,
        

        getFilteredLeads,
        

        handleSendEmail,
        handleConfirmSendEmail,
        handleAddToSegments,
        handleAddToDatabase,
        handleAddToWorkflow,
        handleConfirmAddToDatabase,
        

        addToDatabaseDialogOpen,
        setAddToDatabaseDialogOpen,
        selectedLeadForDatabase,
        setSelectedLeadForDatabase,
        addingToDatabase,
        setAddingToDatabase,
        selectedLeadIdForAction,
        setSelectedLeadIdForAction,
        

        sendEmailDialogOpen,
        setSendEmailDialogOpen,
        emailSubject,
        setEmailSubject,
        emailBody,
        setEmailBody,
        sendingEmail,
        setSendingEmail,
        selectedLeadForEmail,
        setSelectedLeadForEmail,
        

        addToSegmentDialogOpen,
        setAddToSegmentDialogOpen,
        segmentName,
        setSegmentName,
        addingToSegment,
        setAddingToSegment,
        handleConfirmAddToSegment,
        

        addToWorkflowDialogOpen,
        setAddToWorkflowDialogOpen,
        selectedWorkflowId,
        setSelectedWorkflowId,
        addingToWorkflow,
        setAddingToWorkflow,
        availableWorkflows,
        setAvailableWorkflows,
        loadingWorkflows,
        setLoadingWorkflows,
        handleConfirmAddToWorkflow,
        

        setUnlockEmailCallback: (callback: (data: any) => void) => {
            unlockEmailCallbackRef.current = callback
        },
        setUnlockPhoneCallback: (callback: (data: any) => void) => {
            unlockPhoneCallbackRef.current = callback
        },
        

        SharedModals,
        CompanyTableHeader
    }
}



const Companies = ({ activeSubTab, onLeadCreated }: CompaniesProps) => {
    const { workspace } = useWorkspace()
    const { token } = useAuth()
    

    const [sidebarOpen, setSidebarOpen] = useState(false)
    

    const leadManagement = useLeadManagement()
    

    const leadActions = {
        handleSendEmail: leadManagement.handleSendEmail,
        handleAddToSegments: leadManagement.handleAddToSegments,
        handleAddToDatabase: leadManagement.handleAddToDatabase,
        handleAddToWorkflow: leadManagement.handleAddToWorkflow,
        handleUnlockEmail: leadManagement.handleUnlockEmail,
        handleUnlockPhone: leadManagement.handleUnlockPhone,
    }
    
    const sidebarState = {
        isOpen: sidebarOpen,
        setIsOpen: setSidebarOpen
    }
    

    const sharedProps = {
        onLeadCreated,
        token: token?.token,
        workspaceId: workspace?.workspace?.id,

        ActionButton,
        ViewLinksModal,
        LeadActionsDropdown,
        leadActions,
        leadManagement
    }
    
    const renderContent = () => {
        switch (activeSubTab) {
            case 'my-leads':
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
            case 'find-leads':
                return (
                    <>
                        <FindLeads {...sharedProps} sidebarState={sidebarState} />
                        <leadManagement.SharedModals />
                    </>
                )
            case 'saved-search':
                return (
                    <>
                        <SavedSearch {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
            default:
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
        }
    }

    return renderContent()
}

export default Companies