"use client"

import React, { useState, useRef, useMemo } from "react"
import MyLeads from "./my-leads"
import FindLeads from "./find-leads"
import SavedSearch from "./saved-search"
import { useAuth } from "@ui/providers/user"
import { useWorkspace } from "@ui/providers/workspace"
import { But<PERSON> } from "@ui/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { EnvelopeIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon } from "@ui/components/icons/FontAwesomeRegular"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@ui/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Input } from "@ui/components/ui/input"
import { Textarea } from "@ui/components/ui/textarea"
import { Label } from "@ui/components/ui/label"
import { addLeadToDatabase, addLeadToSegment, addLeadToWorkflow, sendEmailToLead, unlockLead } from "@ui/api/leads"
import { getDatabases } from "@ui/api/database"
import { getWorkflows } from "@ui/api/workflow"
import { WorkflowTriggerType } from "@repo/app-db-utils/dist/typings/workflow"
import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { DatabaseSelect } from "@ui/components/workspace/main/common/databaseSelect"
import { OnDemandWorkflowSelect } from "@ui/components/workspace/main/common/onDemandWorkflowSelect"
import { TwoStepDatabaseMapping } from "../common/TwoStepDatabaseMapping"
import { Loader } from "@ui/components/custom-ui/loader"

interface CompaniesProps {
    activeSubTab: 'my-leads' | 'find-leads' | 'saved-search'
    onLeadCreated?: (lead: any) => void
}

// Shared Components (same as people)
export const ActionButton = ({ 
    icon: Icon, 
    children, 
    onClick 
}: { 
    icon: React.ComponentType<{ className?: string }>; 
    children: React.ReactNode; 
    onClick: () => void; 
}) => (
    <Button
        variant="outline"
        size="sm"
        onClick={onClick}
        className="text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center"
    >
        <Icon className="size-3" />
        <span className="truncate">{children}</span>
    </Button>
)

export const ViewLinksModal = ({ 
    trigger,
    links 
}: { 
    trigger: React.ReactNode;
    links: Array<{ id: string; title: string; url: string }> 
}) => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
                {trigger}
            </PopoverTrigger>
            <PopoverContent className="w-64 p-4" align="end">
                <h4 className="font-semibold text-sm mb-2">Social Links</h4>
                <div className="space-y-2">
                    {links.length === 0 ? (
                        <div className="text-xs text-muted-foreground">No links available</div>
                    ) : (
                        links.map((link) => (
                            <a
                                key={link.id}
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors p-2 rounded hover:bg-blue-50"
                            >
                                <UpRightFromSquareIcon className="size-3" />
                                <span className="truncate">{link.title}</span>
                            </a>
                        ))
                    )}
                </div>
            </PopoverContent>
        </Popover>
    )
}

interface LeadActionsDropdownProps {
    trigger: React.ReactNode
    onSendEmail?: () => void
    onAddToSegments?: () => void
    onAddToDatabase?: () => void
    onAddToWorkflow?: () => void
    leadData?: any // Add lead data to check email availability
}

export const LeadActionsDropdown = ({ 
    trigger, 
    onSendEmail, 
    onAddToSegments, 
    onAddToDatabase, 
    onAddToWorkflow,
    leadData 
}: LeadActionsDropdownProps) => {
    // Check if email is available for this lead
    const email = leadData?.normalizedData?.email || leadData?.apolloData?.email || leadData?.email
    const hasEmail = !!email
    
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                {trigger}
            </DropdownMenuTrigger>
            <DropdownMenuContent 
                className="w-56 rounded-none text-neutral-800 font-semibold"
                align="end"
                sideOffset={4}
            >
                <DropdownMenuGroup className="p-1 flex flex-col gap-2">
                    <DropdownMenuItem 
                        className={`text-xs rounded-none p-2 py-1.5 flex items-center gap-2 ${hasEmail ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
                        onClick={hasEmail ? onSendEmail : undefined}
                        disabled={!hasEmail}
                    >
                        <EnvelopeIcon className="size-3 text-neutral-600" />
                        <span>Send Email</span>
                        {!hasEmail && <span className="ml-auto text-xs text-gray-400">(Locked)</span>}
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToSegments}
                    >
                        <ChartLineIcon className="size-3 text-neutral-600" />
                        <span>Add to Segments</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToDatabase}
                    >
                        <DatabaseIcon className="size-3 text-neutral-600" />
                        <span>Add to Database</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToWorkflow}
                    >
                        <CodeMergeIcon className="size-3 text-neutral-600" />
                        <span>Add to Workflow</span>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

// Shared table header component (company version)
export const CompanyTableHeader = ({ 
    selectedLeads, 
    filteredLeads, 
    handleSelectAll 
}: { 
    selectedLeads: string[]
    filteredLeads: any[]
    handleSelectAll: (checked: boolean, leads: any[]) => void
}) => (
    <TableHeader>
        <TableRow className="border-b border-neutral-200 bg-white sticky top-0 z-10">
            <TableHead className="w-12 h-10 px-3">
                <Checkbox
                    checked={selectedLeads.length === filteredLeads.length && filteredLeads.length > 0}
                    onCheckedChange={(checked: boolean) => handleSelectAll(checked, filteredLeads)}
                />
            </TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Name</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Industry</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Location</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Email</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Phone number</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Social Links</TableHead>
            <TableHead className="w-12 h-10 px-1"></TableHead>
        </TableRow>
    </TableHeader>
)

// Centralized hook for all lead-related actions, state, and utilities
export const useLeadManagement = () => {
    const { toast } = useAlert()
    const { token } = useAuth()
    const { workspace } = useWorkspace()
    const router = useRouter()
    const params = useParams()
    
    // All shared state variables
    const [selectedLeads, setSelectedLeads] = useState<string[]>([])
    const [emailModalOpen, setEmailModalOpen] = useState(false)
    const [phoneModalOpen, setPhoneModalOpen] = useState(false)
    const [selectedLeadId, setSelectedLeadId] = useState<string | null>(null)
    
    // Callback refs for unlock success
    const unlockEmailCallbackRef = useRef<((data: any) => void) | null>(null)
    const unlockPhoneCallbackRef = useRef<((data: any) => void) | null>(null)
    
    // Add to Database state
    const [addToDatabaseDialogOpen, setAddToDatabaseDialogOpen] = useState(false)
    const [selectedLeadForDatabase, setSelectedLeadForDatabase] = useState<any>(null)
    const [addingToDatabase, setAddingToDatabase] = useState(false)
    const [selectedLeadIdForAction, setSelectedLeadIdForAction] = useState<string | null>(null)
    
    // Send Email state
    const [sendEmailDialogOpen, setSendEmailDialogOpen] = useState(false)
    const [emailSubject, setEmailSubject] = useState('')
    const [emailBody, setEmailBody] = useState('')
    const [sendingEmail, setSendingEmail] = useState(false)
    const [selectedLeadForEmail, setSelectedLeadForEmail] = useState<any>(null)

    // Add to Segment state
    const [addToSegmentDialogOpen, setAddToSegmentDialogOpen] = useState(false)
    const [segmentName, setSegmentName] = useState('')
    const [addingToSegment, setAddingToSegment] = useState(false)

    // Add to Workflow state
    const [addToWorkflowDialogOpen, setAddToWorkflowDialogOpen] = useState(false)
    const [selectedWorkflowId, setSelectedWorkflowId] = useState('')
    const [addingToWorkflow, setAddingToWorkflow] = useState(false)
    const [availableWorkflows, setAvailableWorkflows] = useState<any[]>([])
    const [loadingWorkflows, setLoadingWorkflows] = useState(false)
    
    // Shared selection handlers
    const handleSelectAll = (checked: boolean, leads: any[]) => {
        if (checked) {
            setSelectedLeads(leads.map(lead => lead.id))
        } else {
            setSelectedLeads([])
        }
    }

    const handleSelectLead = (leadId: string, checked: boolean) => {
        if (checked) {
            setSelectedLeads([...selectedLeads, leadId])
        } else {
            setSelectedLeads(selectedLeads.filter(id => id !== leadId))
        }
    }

    // Shared unlock handlers - now open modals instead of direct API calls
    const handleUnlockEmail = (leadId: string) => {
        setSelectedLeadId(leadId)
        setEmailModalOpen(true)
    }

    const handleUnlockPhone = (leadId: string) => {
        setSelectedLeadId(leadId)
        setPhoneModalOpen(true)
    }

    // Shared navigation handlers
    const handleNameClick = (lead: any) => {
        const domain = params.domain
        router.push(`/${domain}/lead-generation/company/details/${lead.id}`)
    }

    const handleViewLinks = (leadId: string) => {
        console.log("View links for lead:", leadId)
    }

    // Shared contact links generation (company version)
    const getContactLinks = (lead: any) => {
        const links = []
        
        // Add LinkedIn if available (could be from API data)
        if (lead.name) {
            links.push({ 
                id: "linkedin", 
                title: "LinkedIn Profile", 
                url: `https://linkedin.com/search/results/people/?keywords=${encodeURIComponent(lead.name + ' ' + lead.company)}` 
            })
        }
        
        // Add company website if available
        if (lead.company) {
            links.push({ 
                id: "company", 
                title: "Company Website", 
                url: `https://www.google.com/search?q=${encodeURIComponent(lead.company + ' official website')}` 
            })
        }
        
        // Add company LinkedIn
        if (lead.company) {
            links.push({ 
                id: "company-linkedin", 
                title: "Company LinkedIn", 
                url: `https://linkedin.com/search/results/companies/?keywords=${encodeURIComponent(lead.company)}` 
            })
        }
        
        // Add Google search for the company
        if (lead.company) {
            links.push({ 
                id: "google", 
                title: "Google Search", 
                url: `https://www.google.com/search?q=${encodeURIComponent(lead.company)}` 
            })
        }
        
        // Add company employees search if company is available
        if (lead.company) {
            links.push({ 
                id: "employees", 
                title: "Company Employees", 
                url: `https://linkedin.com/search/results/people/?keywords=${encodeURIComponent(lead.company)}` 
            })
        }
        
        return links
    }

    // Shared import leads handler
    const handleImportLeads = () => {
        console.log("Import company leads clicked")
    }


    // Shared API-to-UI conversion logic (company version)
    const convertApiLeadsToUI = (apiLeads: any[]): any[] => {
        
        
        return apiLeads.map((apiLead) => {
            // Debug logging for BBC News unlock issue
            if (apiLead.name && apiLead.name.toLowerCase().includes('bbc')) {
                console.log('🔍 [BBC DEBUG] Processing BBC lead:', {
                    id: apiLead.id,
                    name: apiLead.name,
                    hasNormalizedData: !!apiLead.normalizedData,
                    hasApolloData: !!apiLead.apolloData,
                    normalizedData: apiLead.normalizedData,
                    isEmailVisible: apiLead.normalizedData?.isEmailVisible,
                    isPhoneVisible: apiLead.normalizedData?.isPhoneVisible,
                    email: apiLead.normalizedData?.email,
                    phone: apiLead.normalizedData?.phone
                });
            }
            
            // Detect if this is actually person data in a company lead
            const isPersonData = apiLead.apolloData && (
                apiLead.apolloData.first_name || 
                apiLead.apolloData.last_name || 
                apiLead.apolloData.person ||
                apiLead.apolloData.title
            );
            
            console.log('🔍 [CONVERT] Data type detection:', {
                isPersonData,
                hasFirstName: !!apiLead.apolloData?.first_name,
                hasLastName: !!apiLead.apolloData?.last_name,
                hasPerson: !!apiLead.apolloData?.person,
                hasTitle: !!apiLead.apolloData?.title,
                hasIndustry: !!apiLead.apolloData?.industry
            });
            
            let industry = '-';
            let location = '-';
            let name = 'Unknown';
            let company = 'Unknown';
            
            if (isPersonData) {
                // This is person data, extract accordingly
                console.log('🔍 [CONVERT] Processing as PERSON data in company lead');
                
                // For person data, industry comes from organization
                industry = apiLead.apolloData?.organization?.industry || '-';
                
                // For person data, location comes from person fields
                if (apiLead.apolloData.city || apiLead.apolloData.state || apiLead.apolloData.country) {
                    location = [apiLead.apolloData.city, apiLead.apolloData.state, apiLead.apolloData.country]
                        .filter(Boolean)
                        .join(', ') || '-';
                }
                
                // For person data, name is first + last name
                const firstName = apiLead.apolloData?.first_name || '';
                const lastName = apiLead.apolloData?.last_name || '';
                name = `${firstName} ${lastName}`.trim() || apiLead.apolloData?.name || 'Unknown Person';
                
                // For person data, company comes from organization
                company = apiLead.apolloData?.organization?.name || apiLead.apolloData?.organization?.company_name || 'Unknown Company';
                
            } else {
                // This is actual company data
                console.log('🔍 [CONVERT] Processing as COMPANY data');
                
                // Extract industry from Apollo data
                industry = apiLead.apolloData?.industry || '-';
                
                // Extract location - companies might not have city/state/country in Apollo data
                if (apiLead.apolloData) {
                    const apolloData = apiLead.apolloData;
                    // Check if location data exists in apolloData
                    if (apolloData.city || apolloData.state || apolloData.country) {
                        location = [apolloData.city, apolloData.state, apolloData.country]
                            .filter(Boolean)
                            .join(', ') || '-';
                    } else if (apolloData.location) {
                        // Check if location is in a nested object
                        const loc = apolloData.location;
                        if (typeof loc === 'object' && loc !== null) {
                            location = [loc.city, loc.state, loc.country]
                                .filter(Boolean)
                                .join(', ') || '-';
                        }
                    }
                }
                
                // Extract name with fallback
                name = apiLead.normalizedData?.name || apiLead.apolloData?.name || 'Unknown Company';
                
                // Extract company name with fallback
                company = apiLead.normalizedData?.company || apiLead.apolloData?.name || name;
            }
            
            console.log('🔍 [CONVERT] Extracted data:', {
                name,
                company,
                industry,
                location,
                isPersonData
            });
            
            return {
                id: apiLead.id,
                name,
                company,
                email: apiLead.normalizedData?.isEmailVisible ? apiLead.normalizedData.email || "unlock" : "unlock",
                phone: apiLead.normalizedData?.isPhoneVisible ? apiLead.normalizedData.phone || "unlock" : "unlock",
                links: "view",
                industry,
                location
            };
        });
    }

    // Shared filtered leads logic (company version)
    const getFilteredLeads = (leads: any[], searchQuery?: string, filter?: any) => {
        return useMemo(() => {
            let filtered = leads
            
            // Apply search filter if user is searching locally
            if (searchQuery?.trim()) {
                const searchTerm = searchQuery.trim().toLowerCase()
                filtered = filtered.filter(lead => 
                    Object.values(lead).some(value => 
                        typeof value === 'string' && value.toLowerCase().includes(searchTerm)
                    )
                )
            }
            
            // Apply filter conditions (if any filters are set)
            if (filter?.conditions?.length > 0) {
                filtered = filtered.filter(lead => {
                    return filter.conditions.every((condition: any) => {
                        const value = condition.value?.toString().toLowerCase() || ''
                        const leadValue = lead[condition.columnId as keyof any]?.toString().toLowerCase() || ''
                        return leadValue.includes(value)
                    })
                })
            }
            
            return filtered
        }, [leads, searchQuery, filter])
    }

    const handleSendEmail = async (leadId: string, leadData?: any) => {
        console.log("🔍 [INDEX] handleSendEmail called")
        console.log("🔍 [INDEX] leadId:", leadId)
        console.log("🔍 [INDEX] leadData:", leadData)
        
        // Use provided lead data or create a basic object
        const lead = leadData || { id: leadId, name: 'Unknown Lead' }
        console.log("🔍 [INDEX] lead object:", lead)
        
        // Check if email is unlocked and visible
        const email = lead.normalizedData?.email || lead.apolloData?.email || lead.email
        const isEmailVisible = lead.normalizedData?.isEmailVisible || lead.apolloData?.normalizedData?.isEmailVisible
        console.log("🔍 [INDEX] email found:", email)
        console.log("🔍 [INDEX] isEmailVisible:", isEmailVisible)
        
        if (!email || !isEmailVisible) {
            console.log("🔍 [INDEX] Email not unlocked, showing error toast")
            toast.error("You have to unlock the email first before sending an email.")
            return
        }
        
        console.log("🔍 [INDEX] Email found, opening modal")
        setSelectedLeadForEmail({ ...lead, email })
        setEmailSubject('')
        setEmailBody('')
        setSendEmailDialogOpen(true)
        console.log("🔍 [INDEX] Modal state set to true")
    }

    const handleConfirmSendEmail = async () => {
        if (!selectedLeadForEmail || !emailSubject.trim() || !emailBody.trim()) {
            toast.error("Please fill in both subject and body")
            return
        }

        setSendingEmail(true)
        try {
            const response = await sendEmailToLead(token?.token || '', workspace?.workspace?.id || '', selectedLeadForEmail.id, {
                subject: emailSubject.trim(),
                body: emailBody.trim()
            })
            
            // Check if the API call was actually successful
            if (response.isSuccess) {
                toast.success("Email sent successfully!")
                setSendEmailDialogOpen(false)
                setEmailSubject('')
                setEmailBody('')
                setSelectedLeadForEmail(null)
            } else {
                toast.error(response.error || "Failed to send email")
            }
        } catch (error) {
            console.error("Failed to send email:", error)
            toast.error("Failed to send email")
        } finally {
            setSendingEmail(false)
        }
    }

    const handleAddToSegments = async (leadId: string) => {
        setSelectedLeadIdForAction(leadId)
        setSegmentName('')
        setAddToSegmentDialogOpen(true)
    }

    const handleConfirmAddToSegment = async () => {
        if (!selectedLeadIdForAction || !segmentName.trim()) {
            toast.error("Please enter a segment name")
            return
        }

        setAddingToSegment(true)
        try {
            await addLeadToSegment(token?.token || '', workspace?.workspace?.id || '', selectedLeadIdForAction, { 
                name: segmentName.trim() 
            })
            toast.success("Lead added to segment successfully!")
            setAddToSegmentDialogOpen(false)
            setSegmentName('')
            setSelectedLeadIdForAction('')
        } catch (error) {
            console.error("Failed to add to segment:", error)
            toast.error("Failed to add to segment")
        } finally {
            setAddingToSegment(false)
        }
    }

    const handleAddToDatabase = async (leadId: string, leadData?: any) => {
        console.log("🔍 handleAddToDatabase called with leadId:", leadId)
        
        // Use provided lead data or create a basic object
        const lead = leadData || { id: leadId, name: 'Unknown Company' }
        setSelectedLeadForDatabase(lead)
        setAddToDatabaseDialogOpen(true)
    }



    const handleConfirmAddToDatabase = async (mappings: any[], databaseId: string) => {
        if (!selectedLeadForDatabase || !databaseId || mappings.length === 0) return
        
        setAddingToDatabase(true)
        try {
            // For now, use the existing API with just databaseId
            // TODO: Update API to support field mappings
            const response = await addLeadToDatabase(
                token?.token || '', 
                workspace?.workspace?.id || '', 
                selectedLeadForDatabase.id, 
                { 
                    targetDatabaseId: databaseId
                }
            )
            
            if (response.isSuccess) {
                toast.success("Lead added to database successfully!")
                setAddToDatabaseDialogOpen(false)
                setSelectedLeadForDatabase(null)
            } else {
                toast.error(response.error || "Failed to add lead to database")
            }
        } catch (error) {
            console.error("Failed to add lead to database:", error)
            toast.error("Failed to add lead to database")
        } finally {
            setAddingToDatabase(false)
        }
    }

    const handleAddToWorkflow = async (leadId: string) => {
        console.log("🔍 [FRONTEND] handleAddToWorkflow called with leadId:", leadId)
        setSelectedLeadIdForAction(leadId)
        
        // Load workflows when dialog opens
        setLoadingWorkflows(true)
        try {
            console.log("🔍 [FRONTEND] Loading real workflows from API...")
            const response = await getWorkflows(token?.token || '', workspace?.workspace?.id || '')
            
            if (response.isSuccess && response.data?.data?.workflows) {
                // Filter for OnDemand_Callable workflows that can be manually triggered
                const onDemandWorkflows = response.data.data.workflows.filter(w => w.triggerType === WorkflowTriggerType.OnDemand_Callable)
                console.log("🔍 [FRONTEND] Found OnDemand workflows:", onDemandWorkflows.map(w => ({ id: w.id, name: w.name })))
                
                if (onDemandWorkflows.length > 0) {
                    setAvailableWorkflows(onDemandWorkflows)
                } else {
                    // If no OnDemand workflows, add default option
                    setAvailableWorkflows([
                        { id: 'default-workflow', name: 'Default Lead Workflow' }
                    ])
                }
            } else {
                console.error("🔍 [FRONTEND] Failed to load workflows:", response.error)
                setAvailableWorkflows([
                    { id: 'default-workflow', name: 'Default Lead Workflow' }
                ])
            }
            console.log("🔍 [FRONTEND] Loaded workflows successfully")
        } catch (error) {
            console.error("🔍 [FRONTEND] Failed to load workflows:", error)
            setAvailableWorkflows([
                { id: 'default-workflow', name: 'Default Lead Workflow' }
            ])
        } finally {
            setLoadingWorkflows(false)
        }
        
        setAddToWorkflowDialogOpen(true)
    }

    const handleConfirmAddToWorkflow = async () => {
        console.log("🔍 [FRONTEND] handleConfirmAddToWorkflow called")
        console.log("🔍 [FRONTEND] selectedLeadIdForAction:", selectedLeadIdForAction)
        console.log("🔍 [FRONTEND] selectedWorkflowId:", selectedWorkflowId)
        console.log("🔍 [FRONTEND] token:", token?.token ? "exists" : "missing")
        console.log("🔍 [FRONTEND] workspaceId:", workspace?.workspace?.id)
        
        if (!selectedLeadIdForAction || !selectedWorkflowId) {
            console.log("🔍 [FRONTEND] ERROR: Missing leadId or workflowId")
            toast.error("Please select a workflow")
            return
        }

        setAddingToWorkflow(true)
        try {
            console.log("🔍 [FRONTEND] Calling addLeadToWorkflow API...")
            const result = await addLeadToWorkflow(token?.token || '', workspace?.workspace?.id || '', selectedLeadIdForAction, { 
                workflowId: selectedWorkflowId 
            })
            console.log("🔍 [FRONTEND] addLeadToWorkflow API result:", result)
            
            // Check if the API call was actually successful
            if (result.isSuccess) {
                toast.success("Lead added to workflow successfully!")
                setAddToWorkflowDialogOpen(false)
                setSelectedWorkflowId('')
                setSelectedLeadIdForAction('')
            } else {
                toast.error(result.error || "Failed to add lead to workflow")
            }
        } catch (error) {
            console.error("🔍 [FRONTEND] ERROR in addLeadToWorkflow:", error)
            console.error("🔍 [FRONTEND] Error details:", {
                message: error.message,
                status: error.status,
                response: error.response
            })
            toast.error("Failed to add to workflow")
        } finally {
            setAddingToWorkflow(false)
        }
    }

    const AddToWorkflowDialog = ({
        open,
        onOpenChange,
        selectedWorkflowId,
        setSelectedWorkflowId,
        addingToWorkflow,
        loadingWorkflows,
        availableWorkflows,
        handleConfirmAddToWorkflow
    }: {
        open: boolean
        onOpenChange: (open: boolean) => void
        selectedWorkflowId: string
        setSelectedWorkflowId: (id: string) => void
        addingToWorkflow: boolean
        loadingWorkflows: boolean
        availableWorkflows: any[]
        handleConfirmAddToWorkflow: () => void
    }) => {
        return (
            <Dialog open={open} onOpenChange={onOpenChange}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Add Lead to Workflow</DialogTitle>
                        <DialogDescription>
                            Select a workflow to add this lead to.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        {loadingWorkflows ? (
                            <div className="flex items-center justify-center py-4">
                                <div className="text-sm text-muted-foreground">Loading workflows...</div>
                            </div>
                        ) : availableWorkflows.length === 0 ? (
                            <div className="text-center py-4">
                                <div className="text-sm text-muted-foreground mb-2">No workflows available</div>
                                <div className="text-xs text-muted-foreground">
                                    Please create a workflow first.
                                </div>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium">Select Workflow:</label>
                                    <select
                                        value={selectedWorkflowId}
                                        onChange={(e) => setSelectedWorkflowId(e.target.value)}
                                        disabled={loadingWorkflows}
                                        className="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="">Choose a workflow...</option>
                                        {availableWorkflows.map((workflow) => (
                                            <option key={workflow.id} value={workflow.id}>
                                                {workflow.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                            disabled={addingToWorkflow}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirmAddToWorkflow}
                            disabled={!selectedWorkflowId || addingToWorkflow || loadingWorkflows}
                            className="gap-2"
                        >
                            {addingToWorkflow ? (
                                <>
                                    <Loader theme="dark" className="size-4" />
                                    Adding...
                                </>
                            ) : (
                                <>
                                    <CodeMergeIcon className="size-4" />
                                    Add to Workflow
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        )
    }


    // Send Email Dialog Component
    const SendEmailDialog = () => {
        return (
            <Dialog open={sendEmailDialogOpen} onOpenChange={setSendEmailDialogOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>Send Email to Lead</DialogTitle>
                    </DialogHeader>
                    <div className="py-4 space-y-4">
                        {selectedLeadForEmail && (
                            <div className="p-3 bg-gray-50 rounded-md">
                                <p className="text-sm text-gray-600">
                                    <span className="font-medium">To:</span> {selectedLeadForEmail.name}
                                </p>
                                {selectedLeadForEmail.email && (
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">Email:</span> {selectedLeadForEmail.email}
                                    </p>
                                )}
                            </div>
                        )}
                        
                        <div className="space-y-2">
                            <Label htmlFor="email-subject">Subject</Label>
                            <Input
                                id="email-subject"
                                value={emailSubject}
                                onChange={(e) => setEmailSubject(e.target.value)}
                                placeholder="Enter email subject"
                                disabled={sendingEmail}
                            />
                        </div>
                        
                        <div className="space-y-2">
                            <Label htmlFor="email-body">Message</Label>
                            <Textarea
                                id="email-body"
                                value={emailBody}
                                onChange={(e) => setEmailBody(e.target.value)}
                                placeholder="Enter your message here..."
                                rows={8}
                                disabled={sendingEmail}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setSendEmailDialogOpen(false)}
                            disabled={sendingEmail}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirmSendEmail}
                            disabled={!emailSubject.trim() || !emailBody.trim() || sendingEmail}
                        >
                            {sendingEmail ? "Sending..." : "Send Email"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        )
    }

    const AddToSegmentDialog = () => {
        return (
            <Dialog open={addToSegmentDialogOpen} onOpenChange={setAddToSegmentDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Add Lead to Segment</DialogTitle>
                        <DialogDescription>
                            Enter a name for the segment to add this lead to.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <div className="space-y-2">
                            <Label htmlFor="segment-name">Segment Name</Label>
                            <Input
                                id="segment-name"
                                value={segmentName}
                                onChange={(e) => setSegmentName(e.target.value)}
                                placeholder="e.g., High Priority Leads, Q1 Prospects"
                                disabled={addingToSegment}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setAddToSegmentDialogOpen(false)}
                            disabled={addingToSegment}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirmAddToSegment}
                            disabled={!segmentName.trim() || addingToSegment}
                            className="gap-2"
                        >
                            {addingToSegment ? (
                                <>
                                    <Loader theme="dark" className="size-4" />
                                    Adding...
                                </>
                            ) : (
                                <>
                                    <ChartLineIcon className="size-4" />
                                    Add to Segment
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        )
    }

    // Shared modals
    const SharedModals = () => (
        <>
            <UnlockEmailModal
                open={emailModalOpen}
                onOpenChange={setEmailModalOpen}
                leadId={selectedLeadId}
                onUnlockSuccess={(unlockedLead) => {
                    if (unlockEmailCallbackRef.current) {
                        unlockEmailCallbackRef.current(unlockedLead)
                    }
                }}
            />
            <UnlockPhoneModal
                open={phoneModalOpen}
                onOpenChange={setPhoneModalOpen}
                leadId={selectedLeadId}
                onUnlockSuccess={(unlockedLead) => {
                    if (unlockPhoneCallbackRef.current) {
                        unlockPhoneCallbackRef.current(unlockedLead)
                    }
                }}
            />
            {selectedLeadForDatabase && (
                <TwoStepDatabaseMapping
                    open={addToDatabaseDialogOpen}
                    onOpenChange={setAddToDatabaseDialogOpen}
                    lead={selectedLeadForDatabase}
                    onConfirm={handleConfirmAddToDatabase}
                    loading={addingToDatabase}
                />
            )}
            <AddToSegmentDialog />
            <AddToWorkflowDialog
                open={addToWorkflowDialogOpen}
                onOpenChange={setAddToWorkflowDialogOpen}
                selectedWorkflowId={selectedWorkflowId}
                setSelectedWorkflowId={setSelectedWorkflowId}
                addingToWorkflow={addingToWorkflow}
                loadingWorkflows={loadingWorkflows}
                availableWorkflows={availableWorkflows}
                handleConfirmAddToWorkflow={handleConfirmAddToWorkflow}
            />
            <SendEmailDialog />
        </>
    )

    return {
        // State
        selectedLeads,
        setSelectedLeads,
        emailModalOpen,
        phoneModalOpen,
        selectedLeadId,
        
        // Selection handlers
        handleSelectAll,
        handleSelectLead,
        
        // Unlock handlers
        handleUnlockEmail,
        handleUnlockPhone,
        
        // Navigation handlers
        handleNameClick,
        handleViewLinks,
        
        // Contact links
        getContactLinks,
        
        // Import handler
        handleImportLeads,
        
        // API conversion
        convertApiLeadsToUI,
        
        // Filter logic
        getFilteredLeads,
        
        // Lead actions
        handleSendEmail,
        handleConfirmSendEmail,
        handleAddToSegments,
        handleAddToDatabase,
        handleAddToWorkflow,
        handleConfirmAddToDatabase,
        
        // Database dialog state
        addToDatabaseDialogOpen,
        setAddToDatabaseDialogOpen,
        selectedLeadForDatabase,
        setSelectedLeadForDatabase,
        addingToDatabase,
        setAddingToDatabase,
        selectedLeadIdForAction,
        setSelectedLeadIdForAction,
        
        // Email dialog state
        sendEmailDialogOpen,
        setSendEmailDialogOpen,
        emailSubject,
        setEmailSubject,
        emailBody,
        setEmailBody,
        sendingEmail,
        setSendingEmail,
        selectedLeadForEmail,
        setSelectedLeadForEmail,
        
        // Segment dialog state
        addToSegmentDialogOpen,
        setAddToSegmentDialogOpen,
        segmentName,
        setSegmentName,
        addingToSegment,
        setAddingToSegment,
        handleConfirmAddToSegment,
        
        // Workflow dialog state
        addToWorkflowDialogOpen,
        setAddToWorkflowDialogOpen,
        selectedWorkflowId,
        setSelectedWorkflowId,
        addingToWorkflow,
        setAddingToWorkflow,
        availableWorkflows,
        setAvailableWorkflows,
        loadingWorkflows,
        setLoadingWorkflows,
        handleConfirmAddToWorkflow,
        
        // Callback setters
        setUnlockEmailCallback: (callback: (data: any) => void) => {
            unlockEmailCallbackRef.current = callback
        },
        setUnlockPhoneCallback: (callback: (data: any) => void) => {
            unlockPhoneCallbackRef.current = callback
        },
        
        // Components
        SharedModals,
        CompanyTableHeader
    }
}



const Companies = ({ activeSubTab, onLeadCreated }: CompaniesProps) => {
    const { workspace } = useWorkspace()
    const { token } = useAuth()
    
    // Shared sidebar state that persists across tab switches
    const [sidebarOpen, setSidebarOpen] = useState(false)
    
    // Centralized lead management and actions
    const leadManagement = useLeadManagement()
    
    // Create leadActions directly from leadManagement to avoid duplicate state
    const leadActions = {
        handleSendEmail: leadManagement.handleSendEmail,
        handleAddToSegments: leadManagement.handleAddToSegments,
        handleAddToDatabase: leadManagement.handleAddToDatabase,
        handleAddToWorkflow: leadManagement.handleAddToWorkflow,
        handleUnlockEmail: leadManagement.handleUnlockEmail,
        handleUnlockPhone: leadManagement.handleUnlockPhone,
    }
    
    const sidebarState = {
        isOpen: sidebarOpen,
        setIsOpen: setSidebarOpen
    }
    
    // Shared props for all sub-components
    const sharedProps = {
        onLeadCreated,
        token: token?.token,
        workspaceId: workspace?.workspace?.id,
        // Pass shared components and actions
        ActionButton,
        ViewLinksModal,
        LeadActionsDropdown,
        leadActions,
        leadManagement
    }
    
    const renderContent = () => {
        switch (activeSubTab) {
            case 'my-leads':
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
            case 'find-leads':
                return (
                    <>
                        <FindLeads {...sharedProps} sidebarState={sidebarState} />
                        <leadManagement.SharedModals />
                    </>
                )
            case 'saved-search':
                return (
                    <>
                        <SavedSearch {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
            default:
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
        }
    }

    return renderContent()
}

export default Companies