"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { ScrollArea } from "@ui/components/ui/scroll-area"
import { BookmarkIcon, MagnifyingGlassIcon, TrashIcon, ChevronRightIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, EnvelopeIcon, PhoneIcon, EllipsisVerticalIcon } from "@ui/components/icons/FontAwesomeRegular"
import { MagnifyingGlassCircleIcon } from "@heroicons/react/24/outline"
import { Loader } from "@ui/components/custom-ui/loader"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { UpRightFromSquareIcon } from "@ui/components/icons/FontAwesomeRegular"

import { getSavedSearches, executeSavedSearch, deleteSavedSearch } from "@ui/api/leads"
import { SearchFilters, SavedSearch as SavedSearchType, Lead as APILead } from "@ui/typings/lead"
import { useAlert } from "@ui/providers/alert"
import { formatDistanceToNow } from "date-fns"
import { useRouter } from "next/navigation"
import { useParams } from "next/navigation"
import { PeopleTableHeader } from "./index"

interface SavedSearchProps {
    onLeadCreated?: (lead: any) => void
    token?: string
    workspaceId?: string
    // Shared components and actions from index
    ActionButton?: any
    ViewLinksModal?: any
    LeadActionsDropdown?: any
    leadActions?: any
    leadManagement?: any
}

// UI Lead interface for display
interface Lead {
    id: string
    name: string
    jobTitle: string
    company: string
    email: string
    phone: string
    links: string
    location: string
}

// All components are now imported from index

const SavedSearch = ({ onLeadCreated, token, workspaceId, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement }: SavedSearchProps) => {
    const router = useRouter()
    const params = useParams()
    const [savedSearches, setSavedSearches] = useState<SavedSearchType[]>([])
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    
    // State for showing leads from a saved search
    const [selectedSearch, setSelectedSearch] = useState<SavedSearchType | null>(null)
    const [leads, setLeads] = useState<Lead[]>([])
    const [executingSearch, setExecutingSearch] = useState(false)
    const [deletingSearch, setDeletingSearch] = useState<string | null>(null)
    
    const { toast } = useAlert()
    
    // Register unlock success callbacks
    React.useEffect(() => {
        if (leadManagement?.setUnlockEmailCallback) {
            leadManagement.setUnlockEmailCallback((unlockedLead: any) => {
                // Update the lead in the list with unlocked data
                setLeads(prev => 
                    prev.map(lead => 
                        lead.id === leadManagement?.selectedLeadId 
                            ? { 
                                ...lead, 
                                email: unlockedLead?.normalizedData?.email || "unlock",
                                normalizedData: {
                                    ...(lead as any).normalizedData,
                                    email: unlockedLead?.normalizedData?.email || (lead as any).normalizedData?.email,
                                    isEmailVisible: unlockedLead?.normalizedData?.isEmailVisible || (lead as any).normalizedData?.isEmailVisible
                                }
                            }
                            : lead
                    )
                )
            })
        }
        
        if (leadManagement?.setUnlockPhoneCallback) {
            leadManagement.setUnlockPhoneCallback((unlockedLead: any) => {
                // Update the lead in the list with unlocked data
                setLeads(prev => 
                    prev.map(lead => 
                        lead.id === leadManagement?.selectedLeadId 
                            ? { 
                                ...lead, 
                                phone: unlockedLead?.normalizedData?.phone || "unlock",
                                email: unlockedLead?.normalizedData?.email || lead.email
                            }
                            : lead
                    )
                )
            })
        }
    }, [leadManagement, leads.length])
    
    // Fetch saved searches when token and workspaceId are available
    useEffect(() => {
        const fetchSavedSearches = async () => {
            if (!token || !workspaceId) return;
            
            setLoading(true);
            setError(null);
            
            try {
                const response = await getSavedSearches(token, workspaceId, { searchType: 'people' });
                
                console.log(`🔍 [SAVED SEARCHES] 🔍 Full response:`, response)
                console.log(`🔍 [SAVED SEARCHES] 🔍 Response keys:`, Object.keys(response))
                console.log(`🔍 [SAVED SEARCHES] 🔍 isSuccess:`, response.isSuccess)
                console.log(`🔍 [SAVED SEARCHES] 🔍 error:`, response.error)
                console.log(`🔍 [SAVED SEARCHES] 🔍 data:`, response.data)
                console.log(`🔍 [SAVED SEARCHES] 🔍 data.data:`, response.data?.data)
                console.log(`🔍 [SAVED SEARCHES] 🔍 searches:`, response.data?.data?.searches)
                
                if (response.isSuccess && response.data?.data?.searches) {
                    setSavedSearches(response.data.data.searches);
                    console.log(`🔍 [SAVED SEARCHES] ✅ Loaded ${response.data.data.searches.length} saved searches`)
                } else {
                    console.error(`🔍 [SAVED SEARCHES] ❌ Failed to load - isSuccess: ${response.isSuccess}, error: ${response.error}`)
                    setError(response.error || "Failed to load saved searches");
                    toast.error("Failed to load saved searches");
                }
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
                setError(errorMessage);
                toast.error("Failed to load saved searches");
            } finally {
                setLoading(false);
            }
        };
        
        fetchSavedSearches();
    }, [token, workspaceId, toast]);
    
    // Delete saved search
    const handleDeleteSearch = async (searchId: string, event: React.MouseEvent) => {
        event.stopPropagation() // Prevent card click
        
        if (!token || !workspaceId) {
            toast.error("Authentication required")
            return
        }
        
        setDeletingSearch(searchId)
        
        try {
            console.log('🗑️ [DELETE SEARCH] 🚀 Deleting saved search:', searchId)
            
            const response = await deleteSavedSearch(token, workspaceId, searchId)
            
            console.log('🗑️ [DELETE SEARCH] 📊 Response:', response)
            
            if (response.isSuccess) {
                // Remove from local state
                setSavedSearches(prev => prev.filter(search => search.id !== searchId))
                toast.success("Saved search deleted successfully")
                console.log('🗑️ [DELETE SEARCH] ✅ Successfully deleted saved search')
            } else {
                toast.error("Failed to delete saved search")
                console.error('🗑️ [DELETE SEARCH] ❌ Failed to delete:', response.error)
            }
        } catch (error) {
            console.error('🗑️ [DELETE SEARCH] ❌ Error deleting saved search:', error)
            toast.error("Failed to delete saved search")
        } finally {
            setDeletingSearch(null)
        }
    }
    
    // Execute saved search and show results in table
    const handleExecuteSearch = async (savedSearch: SavedSearchType) => {
        if (!token || !workspaceId) {
            toast.error("Authentication required")
            return
        }
        
        setExecutingSearch(true)
        setSelectedSearch(savedSearch)
        
        try {
            console.log('🔍 [SAVED SEARCH] 🚀 Executing saved search:', savedSearch.id)
            console.log('🔍 [SAVED SEARCH] Filters:', savedSearch.filters)
            
            const response = await executeSavedSearch(token, workspaceId, savedSearch.id, {
                pagination: {
                    page: 1,
                    limit: 50
                },
                searchType: 'people'
            })
            
            console.log('🔍 [SAVED SEARCH] 📊 Response:', response)
            
            if (response.isSuccess && response.data?.data?.leads) {
                const apiLeads = response.data.data.leads
                
                // Convert API leads to UI format using shared logic
                const convertedLeads: Lead[] = leadManagement?.convertApiLeadsToUI(apiLeads) || []
                
                setLeads(convertedLeads)
                leadManagement?.setSelectedLeads([])
                toast.success(`Loaded ${convertedLeads.length} leads from "${savedSearch.name}"`)
                
                console.log('🔍 [SAVED SEARCH] ✅ Successfully loaded leads:', convertedLeads.length)
            } else {
                toast.error("Failed to execute saved search")
                console.error('🔍 [SAVED SEARCH] ❌ Failed to execute:', response.error)
            }
        } catch (error) {
            console.error('🔍 [SAVED SEARCH] ❌ Error executing saved search:', error)
            toast.error("Failed to execute saved search")
        } finally {
            setExecutingSearch(false)
        }
    }
    
    // Generate contact links based on lead data
    // getContactLinks is now provided by shared hook
    
    // Event handlers
    // All selection handlers are now provided by shared hook
    
   
    if (selectedSearch && leads.length > 0) {
    return (
        <>
              
            <div className="flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white">
                    <div className="flex items-center gap-3">
                            <Button 
                                variant="ghost" 
                                size="sm" 
                            onClick={() => {
                                setSelectedSearch(null)
                                setLeads([])
                                leadManagement?.setSelectedLeads([])
                            }}
                            className="text-xs rounded-full h-auto px-3 py-1.5 gap-1.5"
                        >
                            ← Back to Saved Searches
                            </Button>
                            <h1 className="text-xs font-semibold text-black">{selectedSearch.name}</h1>
                </div>
                
                <div className="flex items-center space-x-2">
                        {/* Delete button when items are selected */}
                        {leadManagement?.selectedLeads.length > 0 && (
                    <Button 
                                variant="ghost"
                                className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer"
                                onClick={() => {
                                    setLeads(leads.filter(lead => !leadManagement?.selectedLeads.includes(lead.id)))
                                    leadManagement?.setSelectedLeads([])
                                }}
                            >
                                <TrashIcon className="size-3"/>
                                Delete {leadManagement?.selectedLeads.length}
                    </Button>
                        )}
                    </div>
            </div>
            
                {/* Table with Results */}
                    <div className="flex-1 overflow-hidden">
                            <ScrollArea className="size-full scrollBlockChild">
                                <Table>
                                    <PeopleTableHeader 
                                        selectedLeads={leadManagement?.selectedLeads || []}
                                        filteredLeads={leads}
                                        handleSelectAll={leadManagement?.handleSelectAll || (() => {})}
                                    />
                                    <TableBody>
                                {leads.map((lead) => (
                                            <TableRow key={lead.id} className="border-b border-neutral-200 hover:bg-neutral-50 transition-colors group">
                                        <TableCell className="w-12 px-3 relative">
                                            <Checkbox
                                                checked={leadManagement?.selectedLeads.includes(lead.id)}
                                                onCheckedChange={(checked: boolean) => leadManagement?.handleSelectLead(lead.id, checked)}
                                                className={`${leadManagement?.selectedLeads.includes(lead.id) ? 'opacity-100 visible' : 'invisible opacity-0 group-hover:opacity-100 group-hover:visible'}`}
                                            />
                                        </TableCell>
                                                <TableCell className="px-1">
                                            <button 
                                                className="text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0" 
                                                onClick={() => {
                                                    const domain = params.domain
                                                    router.push(`/${domain}/lead-generation/people/details/${lead.id}`)
                                                }}
                                            >
                                                        {lead.name}
                                            </button>
                                                </TableCell>
                                        <TableCell className="px-1 text-xs text-muted-foreground">{lead.jobTitle}</TableCell>
                                        <TableCell className="px-1 text-xs text-muted-foreground">{lead.company}</TableCell>
                                                <TableCell className="px-1">
                                            {lead.email && lead.email !== "unlock" ? (
                                                // Show unlocked email
                                                <div className="text-xs text-black font-medium truncate max-w-[120px]" title={lead.email}>
                                                    {lead.email}
                                                        </div>
                                                    ) : (
                                                // Show unlock button
                                                <ActionButton 
                                                    icon={EnvelopeIcon} 
                                                    onClick={() => leadManagement?.handleUnlockEmail(lead.id)}
                                                >
                                                    Unlock Email
                                                </ActionButton>
                                                    )}
                                                </TableCell>
                                                <TableCell className="px-1">
                                            {lead.phone && lead.phone !== "unlock" ? (
                                                // Show unlocked phone
                                                <div className="text-xs text-black font-medium truncate max-w-[120px]" title={lead.phone}>
                                                    {lead.phone}
                                                        </div>
                                                    ) : (
                                                // Show unlock button
                                                <ActionButton 
                                                    icon={PhoneIcon} 
                                                    onClick={() => leadManagement?.handleUnlockPhone(lead.id)}
                                                >
                                                    Unlock Mobile
                                                </ActionButton>
                                                    )}
                                                </TableCell>
                                                <TableCell className="px-1">
                                            <ViewLinksModal
                                                trigger={
                                                    <button className="text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0">
                                                        View links
                                                        <ChevronRightIcon className="size-3" />
                                                    </button>
                                                }
                                                links={leadManagement?.getContactLinks(lead) || []}
                                            />
                                        </TableCell>
                                        <TableCell className="w-12 px-2 relative">
                                            <LeadActionsDropdown
                                                trigger={
                                                    <Button 
                                                        variant="ghost" 
                                                        size="sm" 
                                                        className="h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible"
                                                    >
                                                        <EllipsisVerticalIcon className="size-4" />
                                                    </Button>
                                                }
                                                leadData={lead}
                                                onSendEmail={() => leadActions?.handleSendEmail(lead.id, lead)}
                                                onAddToSegments={() => leadActions?.handleAddToSegments(lead.id)}
                                                onAddToDatabase={() => leadActions?.handleAddToDatabase(lead.id, lead)}
                                                onAddToWorkflow={() => leadActions?.handleAddToWorkflow(lead.id)}
                                            />
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                        {/* Horizontal line under table */}
                        <div className="border-b border-neutral-200"></div>
                            </ScrollArea>
                </div>
            </>
        )
    }
    
    // Show saved searches as cards
    return (
        <>
            {/* Content Header */}
            <div className="flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white">
                <div className="flex items-center">
                    <h1 className="text-xs font-semibold text-black">Saved Searches</h1>
                </div>
                    </div>
            
            {/* Main Content Area */}
                <div className="flex-1 overflow-hidden">
                    {loading ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                            <Loader theme="dark" className="size-8 mx-auto mb-4" />
                            <p className="text-neutral-500 text-sm">Loading saved searches...</p>
                            </div>
                        </div>
                    ) : error ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <MagnifyingGlassIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-neutral-900 mb-2">Error loading saved searches</h3>
                            <p className="text-neutral-500 mb-4 text-sm">{error}</p>
                        </div>
                    </div>
                ) : savedSearches.length === 0 ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                            <BookmarkIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-neutral-900 mb-2">No saved searches</h3>
                                <p className="text-neutral-500 mb-4 text-sm">
                                Save searches from the Find People page to see them here
                            </p>
                            </div>
                        </div>
                    ) : (
                    <ScrollArea className="size-full scrollBlockChild">
                        <div className="p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {savedSearches.map((search) => (
                                        <div 
                                            key={search.id} 
                                    className="border border-neutral-200 rounded-lg p-4 hover:border-neutral-300 hover:shadow-sm transition-all cursor-pointer bg-white relative group"
                                            onClick={() => handleExecuteSearch(search)}
                                        >
                                            <div className="flex items-start justify-between mb-3">
                                                <div className="flex items-center gap-2">
                                                    <BookmarkIcon className="size-4 text-primary" />
                                            <h3 className="text-sm font-semibold text-black truncate">{search.name}</h3>
                                                </div>
                                        <div className="flex items-center gap-2">
                                            {executingSearch && selectedSearch?.id === search.id && (
                                                <Loader theme="dark" className="size-4" />
                                            )}
                                            <button
                                                onClick={(e) => handleDeleteSearch(search.id, e)}
                                                disabled={deletingSearch === search.id}
                                                className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-red-50 rounded-full text-red-500 hover:text-red-600 disabled:opacity-50"
                                                title="Delete saved search"
                                            >
                                                {deletingSearch === search.id ? (
                                                    <Loader theme="dark" className="size-3" />
                                                ) : (
                                                    <TrashIcon className="size-3" />
                                                )}
                                            </button>
                                                </div>
                                            </div>
                                    
                                    {search.description && (
                                        <p className="text-xs text-neutral-600 mb-3 line-clamp-2">{search.description}</p>
                                    )}
                                    
                                            <div className="flex items-center justify-between text-xs text-neutral-500">
                                        <span>Saved {formatDistanceToNow(new Date(search.createdAt), { addSuffix: true })}</span>
                                        <ChevronRightIcon className="size-3" />
                                        </div>
                                    </div>
                                ))}
                        </div>
                    </ScrollArea>
                )}
            </div>
        </>
    )
}

export default SavedSearch