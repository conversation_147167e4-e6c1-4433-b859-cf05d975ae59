"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/lead-generation/people/my-leads/page",{

/***/ "(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/index.tsx":
/*!*********************************************************************************!*\
  !*** ../../packages/ui/src/components/workspace/main/lead-generation/index.tsx ***!
  \*********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ui/providers/workspace */ \"(app-pages-browser)/../../packages/ui/src/providers/workspace.tsx\");\n/* harmony import */ var _ui_providers_screenSize__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ui/providers/screenSize */ \"(app-pages-browser)/../../packages/ui/src/providers/screenSize.tsx\");\n/* harmony import */ var _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ui/context/routerContext */ \"(app-pages-browser)/../../packages/ui/src/context/routerContext.tsx\");\n/* harmony import */ var _ui_components_custom_ui_tabView__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ui/components/custom-ui/tabView */ \"(app-pages-browser)/../../packages/ui/src/components/custom-ui/tabView.tsx\");\n/* harmony import */ var _people__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./people */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/people/index.tsx\");\n/* harmony import */ var _company__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./company */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/index.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst LeadGeneration = (props)=>{\n    _s();\n    const { workspace } = (0,_ui_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { isMobile } = (0,_ui_providers_screenSize__WEBPACK_IMPORTED_MODULE_3__.useScreenSize)();\n    const router = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const params = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_4__.useParams)();\n    const pathname = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const getActiveTabsFromUrl = ()=>{\n        const pathSegments = pathname.split(\"/\").filter(Boolean);\n        const leadGenIndex = pathSegments.findIndex((segment)=>segment === \"lead-generation\");\n        if (leadGenIndex !== -1) {\n            const primaryTab = pathSegments[leadGenIndex + 1];\n            const secondaryTab = pathSegments[leadGenIndex + 2] // 'my-leads', 'find-leads', 'saved-search'\n            ;\n            return {\n                primary: primaryTab === \"companies\" ? \"companies\" : \"people\",\n                secondary: [\n                    \"my-leads\",\n                    \"find-leads\",\n                    \"saved-search\"\n                ].includes(secondaryTab) ? secondaryTab : \"my-leads\"\n            };\n        }\n        return {\n            primary: \"people\",\n            secondary: \"my-leads\"\n        };\n    };\n    const { primary: activeTab, secondary: activeSubTab } = getActiveTabsFromUrl();\n    const handlePrimaryTabClick = (tab)=>{\n        const domain = params.domain;\n        router.push(\"/\".concat(domain, \"/lead-generation/\").concat(tab, \"/my-leads\"));\n    };\n    const handleSecondaryTabClick = (subTab)=>{\n        const domain = params.domain;\n        router.push(\"/\".concat(domain, \"/lead-generation/\").concat(activeTab, \"/\").concat(subTab));\n    };\n    const primaryTabs = [\n        {\n            id: \"people\",\n            title: \"People\",\n            content: null\n        },\n        {\n            id: \"companies\",\n            title: \"Companies\",\n            content: null\n        }\n    ];\n    const secondaryTabs = [\n        {\n            id: \"my-leads\",\n            title: \"My leads\",\n            content: null\n        },\n        {\n            id: \"find-leads\",\n            title: \"Find leads\",\n            content: null\n        },\n        {\n            id: \"saved-search\",\n            title: \"Saved search\",\n            content: null\n        }\n    ];\n    const renderContent = ()=>{\n        switch(activeTab){\n            case \"people\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_people__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    activeSubTab: activeSubTab,\n                    onLeadCreated: props.onLeadCreated\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\index.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 24\n                }, undefined);\n            case \"companies\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_company__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    activeSubTab: activeSubTab,\n                    onLeadCreated: props.onLeadCreated\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\index.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 24\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_people__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    activeSubTab: activeSubTab,\n                    onLeadCreated: props.onLeadCreated\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\index.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 24\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_tabView__WEBPACK_IMPORTED_MODULE_5__.NavigationTabView, {\n                tabs: primaryTabs,\n                tab: activeTab,\n                onTabChange: handlePrimaryTabClick,\n                tabHeaderClassName: \"\".concat(isMobile ? \"pl-8\" : \"pl-0\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\index.tsx\",\n                lineNumber: 85,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_tabView__WEBPACK_IMPORTED_MODULE_5__.NavigationTabView, {\n                tabs: secondaryTabs,\n                tab: activeSubTab,\n                onTabChange: handleSecondaryTabClick,\n                tabHeaderClassName: \"py-2 border-b border-neutral-200 bg-white \".concat(isMobile ? \"pl-4\" : \"pl-0\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\index.tsx\",\n                lineNumber: 93,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\index.tsx\",\n                lineNumber: 101,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\index.tsx\",\n        lineNumber: 83,\n        columnNumber: 9\n    }, undefined);\n};\n_s(LeadGeneration, \"NYUu94SitNHxDXo4iA0gWMGWwIM=\", false, function() {\n    return [\n        _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _ui_providers_screenSize__WEBPACK_IMPORTED_MODULE_3__.useScreenSize,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_4__.useParams,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = LeadGeneration;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeadGeneration);\nvar _c;\n$RefreshReg$(_c, \"LeadGeneration\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/index.tsx\n"));

/***/ })

});