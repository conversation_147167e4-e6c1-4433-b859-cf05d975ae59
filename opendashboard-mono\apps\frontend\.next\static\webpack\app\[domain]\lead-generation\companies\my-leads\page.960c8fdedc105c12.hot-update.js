"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/lead-generation/companies/my-leads/page",{

/***/ "(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/find-leads.tsx":
/*!**********************************************************************************************!*\
  !*** ../../packages/ui/src/components/workspace/main/lead-generation/company/find-leads.tsx ***!
  \**********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ui/components/ui/button */ \"(app-pages-browser)/../../packages/ui/src/components/ui/button.tsx\");\n/* harmony import */ var _ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ui/components/ui/input */ \"(app-pages-browser)/../../packages/ui/src/components/ui/input.tsx\");\n/* harmony import */ var _ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ui/components/ui/checkbox */ \"(app-pages-browser)/../../packages/ui/src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ui/components/ui/table */ \"(app-pages-browser)/../../packages/ui/src/components/ui/table.tsx\");\n/* harmony import */ var _ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ui/components/ui/scroll-area */ \"(app-pages-browser)/../../packages/ui/src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ui/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/../../packages/ui/src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassCircleIcon.js\");\n/* harmony import */ var _ui_components_workspace_main_lead_generation_filters_company__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ui/components/workspace/main/lead-generation/filters/company */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/filters/company.tsx\");\n/* harmony import */ var _ui_api_leads__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ui/api/leads */ \"(app-pages-browser)/../../packages/ui/src/api/leads.ts\");\n/* harmony import */ var _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @ui/providers/workspace */ \"(app-pages-browser)/../../packages/ui/src/providers/workspace.tsx\");\n/* harmony import */ var _ui_providers_user__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ui/providers/user */ \"(app-pages-browser)/../../packages/ui/src/providers/user.tsx\");\n/* harmony import */ var _ui_providers_alert__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ui/providers/alert */ \"(app-pages-browser)/../../packages/ui/src/providers/alert.tsx\");\n/* harmony import */ var _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @ui/context/routerContext */ \"(app-pages-browser)/../../packages/ui/src/context/routerContext.tsx\");\n/* harmony import */ var _ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ui/components/ui/dialog */ \"(app-pages-browser)/../../packages/ui/src/components/ui/dialog.tsx\");\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./index */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/index.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// All components are now imported from index\nconst FindLeads = (param)=>{\n    let { onLeadCreated, sidebarState, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement } = param;\n    _s();\n    const router = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const params = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__.useParams)();\n    const { workspace } = (0,_ui_providers_workspace__WEBPACK_IMPORTED_MODULE_10__.useWorkspace)();\n    const { token } = (0,_ui_providers_user__WEBPACK_IMPORTED_MODULE_11__.useAuth)();\n    const { toast } = (0,_ui_providers_alert__WEBPACK_IMPORTED_MODULE_12__.useAlert)();\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Search filters state - ADDED\n    const [searchFilters, setSearchFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        person: {},\n        company: {},\n        signals: {},\n        customFilters: {}\n    });\n    // Exclude my leads state - ADDED\n    const [excludeMyLeads, setExcludeMyLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Modal states are now managed by shared hook\n    const [isSavingSearch, setIsSavingSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSearchId, setCurrentSearchId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Dialog state for save search\n    const [saveDialogOpen, setSaveDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchName, setSearchName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Register unlock success callbacks\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockEmailCallback) {\n            leadManagement.setUnlockEmailCallback((unlockedLead)=>{\n                var _unlockedLead_apolloData;\n                const normalizedData = (unlockedLead === null || unlockedLead === void 0 ? void 0 : unlockedLead.normalizedData) || (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_apolloData = unlockedLead.apolloData) === null || _unlockedLead_apolloData === void 0 ? void 0 : _unlockedLead_apolloData.normalizedData);\n                // Update the lead in the list with unlocked data\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _lead_normalizedData;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || \"unlock\",\n                            normalizedData: {\n                                ...lead.normalizedData,\n                                email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email),\n                                isEmailVisible: true // Set to true after successful unlock\n                            }\n                        } : lead;\n                    }));\n            });\n        }\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockPhoneCallback) {\n            leadManagement.setUnlockPhoneCallback((unlockedLead)=>{\n                var _unlockedLead_apolloData;\n                const normalizedData = (unlockedLead === null || unlockedLead === void 0 ? void 0 : unlockedLead.normalizedData) || (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_apolloData = unlockedLead.apolloData) === null || _unlockedLead_apolloData === void 0 ? void 0 : _unlockedLead_apolloData.normalizedData);\n                // Update the lead in the list with unlocked data\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _lead_normalizedData, _lead_normalizedData1, _lead_normalizedData2;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            phone: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.phone) || \"unlock\",\n                            email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || lead.email,\n                            normalizedData: {\n                                ...lead.normalizedData,\n                                email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email),\n                                phone: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.phone) || ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.phone),\n                                isEmailVisible: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.isEmailVisible) || ((_lead_normalizedData2 = lead.normalizedData) === null || _lead_normalizedData2 === void 0 ? void 0 : _lead_normalizedData2.isEmailVisible),\n                                isPhoneVisible: true // Set to true after successful unlock\n                            }\n                        } : lead;\n                    }));\n            });\n        }\n    }, [\n        leadManagement,\n        leads.length\n    ]);\n    // Initialize searchFilters with default values when component mounts\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const defaultFilters = {\n            person: {},\n            company: {},\n            signals: {},\n            customFilters: {}\n        };\n        setSearchFilters(defaultFilters);\n    }, []);\n    // Search functionality\n    const searchLeads = async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : searchFilters, page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        var _workspace_workspace;\n        if (!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || !(token === null || token === void 0 ? void 0 : token.token)) {\n            toast.error(\"Authentication required\");\n            return;\n        }\n        // Check if API is properly configured\n        if (false) {}\n        setIsSearching(true);\n        try {\n            var _response_data_data, _response_data, _response_data_data1, _response_data1, _response_data_data2, _response_data2, _response_data_data3, _response_data3, _response_data_data4, _response_data4, _response_data_data5, _response_data5, _response_data_data_metadata, _response_data_data6, _response_data6, _response_data_data7, _response_data7;\n            const cleanFilters = {\n                ...filters\n            };\n            const searchRequest = {\n                filters: cleanFilters,\n                excludeMyLeads: excludeMyLeads,\n                pagination: {\n                    page: page,\n                    limit: 50\n                }\n            };\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_9__.searchCompanyLeads)(token.token, workspace.workspace.id, searchRequest);\n            if (response.error) {\n                // Provide user-friendly error messages\n                const errorMessage = response.error.toLowerCase();\n                if (errorMessage.includes(\"unauthorized\") || errorMessage.includes(\"authentication\")) {\n                    toast.error(\"Session expired. Please log in again.\");\n                } else if (errorMessage.includes(\"network\") || errorMessage.includes(\"connection\")) {\n                    toast.error(\"Connection error. Please check your internet and try again.\");\n                } else if (errorMessage.includes(\"env\") || errorMessage.includes(\"undefined\")) {\n                    toast.error(\"Search service is currently unavailable. Please try again later.\");\n                } else {\n                    toast.error(\"Failed to search company leads. Please try again.\");\n                }\n                return;\n            }\n            const apiLeads = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.leads) || [];\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ✅ API leads extracted:\", {\n                leadsCount: apiLeads.length,\n                firstLead: apiLeads[0] || null,\n                allLeads: apiLeads\n            });\n            // Convert API leads to UI format using shared logic\n            const convertedLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.convertApiLeadsToUI(apiLeads)) || [];\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Converted leads for UI:\", {\n                convertedCount: convertedLeads.length,\n                firstConverted: convertedLeads[0] || null\n            });\n            setLeads(convertedLeads);\n            // Store search results with proper structure for pagination display\n            setSearchResults({\n                totalCount: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data1 = _response_data1.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.totalCount,\n                metadata: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : (_response_data_data2 = _response_data2.data) === null || _response_data_data2 === void 0 ? void 0 : _response_data_data2.metadata) || {},\n                hasNextPage: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data3 = _response_data3.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.hasNextPage\n            });\n            // Update pagination state\n            const responseTotalCount = ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data4 = _response_data4.data) === null || _response_data_data4 === void 0 ? void 0 : _response_data_data4.totalCount) || 0;\n            const responseHasNextPage = ((_response_data5 = response.data) === null || _response_data5 === void 0 ? void 0 : (_response_data_data5 = _response_data5.data) === null || _response_data_data5 === void 0 ? void 0 : _response_data_data5.hasNextPage) || false;\n            // Calculate total pages based on cached results\n            // Show ALL cached pages + ONE page to trigger Apollo expansion\n            let availablePages = 1;\n            // Use the backend's totalPagesAvailable to show all cached pages\n            // This ensures users can navigate to ALL available pages, plus one more to trigger Apollo\n            // Example: If backend has 14 pages, show 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n            // Where page 15 triggers Apollo to get more leads\n            const totalPagesAvailable = ((_response_data6 = response.data) === null || _response_data6 === void 0 ? void 0 : (_response_data_data6 = _response_data6.data) === null || _response_data_data6 === void 0 ? void 0 : (_response_data_data_metadata = _response_data_data6.metadata) === null || _response_data_data_metadata === void 0 ? void 0 : _response_data_data_metadata.totalPagesAvailable) || 1;\n            availablePages = totalPagesAvailable + 1;\n            setTotalCount(responseTotalCount);\n            setTotalPages(availablePages);\n            setCurrentPage(page) // Set to the page we just searched for\n            ;\n            // Track the current search ID for saving functionality\n            const searchId = ((_response_data7 = response.data) === null || _response_data7 === void 0 ? void 0 : (_response_data_data7 = _response_data7.data) === null || _response_data_data7 === void 0 ? void 0 : _response_data_data7.searchId) || \"\";\n            setCurrentSearchId(searchId);\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDCCA Pagination updated:\", {\n                currentPage: page,\n                totalPages: availablePages,\n                totalCount: responseTotalCount,\n                leadsPerPage: 50\n            });\n            setHasSearched(true);\n            toast.success(\"Found \".concat(convertedLeads.length, \" company leads\"));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ✅ Search completed successfully. State updated.\");\n        } catch (error) {\n            console.error(\"Search error:\", error);\n            // Provide user-friendly error message without exposing technical details\n            toast.error(\"Search service is temporarily unavailable. Please try again later.\");\n        } finally{\n            setIsSearching(false);\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Search state reset to not searching\");\n        }\n    };\n    // Handle filter changes from CompanyFilter\n    const handleFilterChange = (filters)=>{\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] handleFilterChange called with:\", JSON.stringify(filters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Previous searchFilters:\", JSON.stringify(searchFilters, null, 2));\n        // Reset pagination when filters change - this ensures fresh results from page 1\n        // when user changes search criteria (industry, company size, etc.)\n        setCurrentPage(1);\n        setTotalPages(1);\n        setTotalCount(0);\n        setCurrentSearchId(\"\");\n        setHasSearched(false);\n        setLeads([]);\n        setSearchResults(null);\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDD04 Pagination and search state reset to page 1 (filters changed)\");\n        // Simply replace the filters completely - CompanyFilter sends the complete state\n        // No need to merge as it can cause conflicts and state inconsistencies\n        setSearchFilters(filters) // FIXED: Now properly updates the state\n        ;\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] searchFilters state updated to:\", JSON.stringify(filters, null, 2));\n    // Don't auto-search! Let user control search via Search button\n    // This provides a much cleaner UX flow\n    };\n    // Pagination functions\n    const handlePageChange = (page)=>{\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Page change requested: \".concat(page));\n        if (page < 1) {\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ❌ Invalid page: \".concat(page, \" - cannot be less than 1\"));\n            return;\n        }\n        // Allow clicking beyond current totalPages - this will trigger Apollo calls\n        // when the backend detects the requested page doesn't exist in cache\n        if (page > totalPages) {\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDD04 Page \".concat(page, \" requested beyond current cache (\").concat(totalPages, \" pages)\"));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] This will trigger Apollo call to get more leads\");\n        }\n        // Automatically search for the page with current filters - this is the fix!\n        // This ensures pagination works correctly without requiring extra button clicks\n        setCurrentPage(page);\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ✅ Page set to \".concat(page, \". Automatically searching for page \").concat(page, \" results\"));\n        // Use current filters and search for the specific page\n        searchLeads(searchFilters, page);\n    };\n    const calculateTotalPages = function(totalLeads) {\n        let leadsPerPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        return Math.ceil(totalLeads / leadsPerPage);\n    };\n    const generatePageNumbers = ()=>{\n        const pages = [];\n        const maxVisiblePages = 5 // Show max 5 page numbers at once\n        ;\n        if (totalPages <= maxVisiblePages) {\n            // Show all pages if total is small\n            for(let i = 1; i <= totalPages; i++){\n                pages.push(i);\n            }\n        } else {\n            // Show smart pagination with ellipsis\n            if (currentPage <= 3) {\n                // Near start: show 1, 2, 3, 4, 5, ..., last\n                for(let i = 1; i <= 5; i++){\n                    pages.push(i);\n                }\n                pages.push(\"...\");\n                pages.push(totalPages);\n            } else if (currentPage >= totalPages - 2) {\n                // Near end: show 1, ..., last-4, last-3, last-2, last-1, last\n                pages.push(1);\n                pages.push(\"...\");\n                for(let i = totalPages - 4; i <= totalPages; i++){\n                    pages.push(i);\n                }\n            } else {\n                // Middle: show 1, ..., current-1, current, current+1, ..., last\n                pages.push(1);\n                pages.push(\"...\");\n                for(let i = currentPage - 1; i <= currentPage + 1; i++){\n                    pages.push(i);\n                }\n                pages.push(\"...\");\n                pages.push(totalPages);\n            }\n        }\n        return pages;\n    };\n    // Manual search trigger\n    const handleSearch = ()=>{\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] handleSearch called\");\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Current searchFilters:\", JSON.stringify(searchFilters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Current page: \".concat(currentPage));\n        console.log('\\uD83D\\uDD0D [FRONTEND DEBUG] Current searchQuery: \"\"'); // searchQuery removed\n        // Use the current page that user selected (don't reset to 1)\n        // This allows users to click page 2, then click Search to get page 2 results\n        // ONLY send sidebar filters - ignore search input field\n        // Search input is for a different purpose, not for Apollo searches\n        const filtersToSend = {\n            ...searchFilters // FIXED: Now uses the actual searchFilters state\n        };\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Final filters being sent to searchLeads (sidebar filters only):\", JSON.stringify(filtersToSend, null, 2));\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Searching for page: \".concat(currentPage));\n        searchLeads(filtersToSend, currentPage) // Use current page, not always page 1\n        ;\n    };\n    // Event handlers\n    const handleCreateLead = ()=>{\n        console.log(\"Create company lead clicked\");\n    };\n    // handleImportLeads is now provided by shared hook\n    // Selection handlers are now provided by shared hook\n    // Save current search results as a saved search\n    const handleSaveLeads = ()=>{\n        var _workspace_workspace, _workspace_workspace1;\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] \\uD83D\\uDE80 User clicked Save Leads button\");\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] \\uD83D\\uDCCA Current state:\", {\n            hasWorkspace: !!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id),\n            hasToken: !!(token === null || token === void 0 ? void 0 : token.token),\n            hasSearched,\n            leadsCount: leads.length,\n            currentSearchId,\n            currentPage,\n            searchFilters: searchFilters\n        });\n        if (!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace1 = workspace.workspace) === null || _workspace_workspace1 === void 0 ? void 0 : _workspace_workspace1.id) || !(token === null || token === void 0 ? void 0 : token.token)) {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] ❌ Missing authentication\");\n            toast.error(\"Authentication required\");\n            return;\n        }\n        if (!hasSearched || leads.length === 0) {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] ❌ No search results to save\");\n            toast.error(\"No search results to save\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] ✅ Validation passed, opening save dialog\");\n        // Set default name and open dialog\n        const defaultName = \"Company Search - \".concat(new Date().toLocaleDateString());\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] \\uD83D\\uDCDD Setting default name:\", defaultName);\n        setSearchName(defaultName);\n        setSaveDialogOpen(true);\n    };\n    // Handle the actual save operation\n    const handleConfirmSave = async ()=>{\n        var _workspace_workspace;\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDE80 User confirmed save operation\");\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDCCA Save details:\", {\n            searchName: searchName === null || searchName === void 0 ? void 0 : searchName.trim(),\n            leadsCount: leads.length,\n            currentPage,\n            currentSearchId,\n            hasToken: !!(token === null || token === void 0 ? void 0 : token.token),\n            hasWorkspace: !!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id)\n        });\n        if (!searchName || searchName.trim() === \"\") {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Empty search name\");\n            toast.error(\"Search name cannot be empty\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ✅ Starting save process...\");\n        setIsSavingSearch(true);\n        setSaveDialogOpen(false);\n        try {\n            var _response_data;\n            const saveRequest = {\n                name: searchName.trim(),\n                description: \"Saved company search with \".concat(leads.length, \" leads from page \").concat(currentPage),\n                filters: searchFilters,\n                searchId: currentSearchId || undefined,\n                searchType: \"company\"\n            };\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDCE4 Sending save request:\", saveRequest);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD17 API call: saveSearch()\");\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_9__.saveSearch)(token.token, workspace.workspace.id, saveRequest);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDCE5 Received response from backend\");\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D Full response:\", response);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D Response keys:\", Object.keys(response));\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D isSuccess:\", response.isSuccess);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D error:\", response.error);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D data:\", response.data);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D data.data:\", (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data);\n            if (response.isSuccess) {\n                var _response_data_data_savedSearch, _response_data_data, _response_data1;\n                console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ✅ Save operation successful!\");\n                console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDCCB Saved search details:\", {\n                    name: searchName,\n                    leadsCount: leads.length,\n                    searchId: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : (_response_data_data_savedSearch = _response_data_data.savedSearch) === null || _response_data_data_savedSearch === void 0 ? void 0 : _response_data_data_savedSearch.id,\n                    searchType: \"company\"\n                });\n                toast.success('Saved \"'.concat(searchName, '\" with ').concat(leads.length, \" company leads\"));\n            } else {\n                console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Save operation failed\");\n                console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error details:\", {\n                    isSuccess: response.isSuccess,\n                    error: response.error,\n                    status: response.status\n                });\n                toast.error(response.error || \"Failed to save search\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Exception during save operation:\", error);\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error type:\", typeof error);\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error message:\", error instanceof Error ? error.message : \"Unknown error\");\n            toast.error(\"Failed to save search. Please try again.\");\n        } finally{\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83C\\uDFC1 Save operation completed, resetting UI state\");\n            setIsSavingSearch(false);\n        }\n    };\n    // Unlock handlers are now provided by shared hook\n    // Generate contact links based on lead data\n    // getContactLinks is now provided by shared hook\n    const handleNameClick = (lead)=>{\n        // Navigate to company details page using router\n        const domain = params.domain;\n        router.push(\"/\".concat(domain, \"/lead-generation/companies/details/\").concat(lead.id));\n    };\n    // filteredLeads is now provided by shared hook\n    const filteredLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getFilteredLeads(leads, undefined, undefined)) || leads;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xs font-semibold text-black\",\n                            children: \"Find Companies\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer\",\n                                onClick: ()=>{\n                                    setLeads(leads.filter((lead)=>!(leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id))));\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setSelectedLeads([]);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.TrashIcon, {\n                                        className: \"size-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    \"Delete \",\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: totalPages > 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>handlePageChange(Math.max(1, currentPage - 1)),\n                                                    disabled: currentPage === 1,\n                                                    className: \"h-6 w-6 p-0 text-xs\",\n                                                    children: \"←\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: generatePageNumbers().map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                            children: page === \"...\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-1 text-neutral-400 text-xs\",\n                                                                children: \"...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 57\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: currentPage === page ? \"default\" : \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handlePageChange(page),\n                                                                className: \"h-6 w-6 p-0 text-xs \".concat(currentPage === page ? \"bg-primary text-white\" : \"hover:bg-neutral-50\"),\n                                                                children: page\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 49\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>handlePageChange(Math.min(totalPages, currentPage + 1)),\n                                                    disabled: currentPage === totalPages,\n                                                    className: \"h-6 w-6 p-0 text-xs\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 37\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-neutral-500\",\n                                            children: \"Single page\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"size-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                placeholder: \"Search and find companies\",\n                                                // value={searchQuery} // searchQuery removed\n                                                // onChange={(e) => setSearchQuery(e.target.value)} // searchQuery removed\n                                                onKeyDown: (e)=>e.key === \"Enter\" && handleSearch(),\n                                                className: \"text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleSearch,\n                                        disabled: isSearching,\n                                        className: \"text-xs rounded-full h-auto px-3 py-1.5\",\n                                        children: isSearching ? \"Searching...\" : \"Search\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    hasSearched && leads.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSaveLeads,\n                                        disabled: isSavingSearch,\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        className: \"text-xs rounded-full h-auto px-3 py-1.5 gap-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.DatabaseIcon, {\n                                                className: \"size-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            isSavingSearch ? \"Saving...\" : \"Save \".concat(leads.length, \" Companies\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                lineNumber: 515,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_workspace_main_lead_generation_filters_company__WEBPACK_IMPORTED_MODULE_8__.CompanyFilter, {\n                        forceSidebar: true,\n                        onFilterChange: handleFilterChange,\n                        excludeMyLeads: excludeMyLeads,\n                        onExcludeMyLeadsChange: (value)=>{\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] Parent received excludeMyLeads change:\");\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] - Previous value: \".concat(excludeMyLeads));\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] - New value: \".concat(value));\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] - This will affect display filtering, not search criteria\");\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] ==========================================\");\n                            setExcludeMyLeads(value) // FIXED: Now properly updates the state\n                            ;\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] excludeMyLeads state updated to: \".concat(value));\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: filteredLeads.length === 0 ? /* Empty State - INSIDE the container */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.MagnifyingGlassIcon, {\n                                        className: \"size-12 text-neutral-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-neutral-900 mb-2\",\n                                        children: hasSearched ? \"No companies found\" : \"Ready to find companies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-neutral-500 mb-4 text-sm\",\n                                        children: hasSearched ? \"Try adjusting your search query or filters to find more results\" : \"Use the search box above or apply filters on the left to discover companies. Click the Search button to start.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                className: \"size-full scrollBlockChild\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_15__.CompanyTableHeader, {\n                                                selectedLeads: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads) || [],\n                                                filteredLeads: filteredLeads,\n                                                handleSelectAll: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectAll) || (()=>{})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                                children: filteredLeads.map((lead)=>{\n                                                    var _lead_normalizedData, _lead_normalizedData1, _lead_normalizedData2, _lead_normalizedData3;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                        className: \"border-b border-neutral-200 hover:bg-neutral-50 transition-colors group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"w-12 px-3 relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__.Checkbox, {\n                                                                    checked: leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id),\n                                                                    onCheckedChange: (checked)=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectLead(lead.id, checked),\n                                                                    className: \"\".concat((leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id)) ? \"opacity-100 visible\" : \"invisible opacity-0 group-hover:opacity-100 group-hover:visible\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0\",\n                                                                    onClick: ()=>handleNameClick(lead),\n                                                                    children: lead.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1 text-xs text-muted-foreground\",\n                                                                children: lead.industry\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1 text-xs text-muted-foreground\",\n                                                                children: lead.location\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1\",\n                                                                children: ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email) && ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.isEmailVisible) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                                    title: lead.normalizedData.email,\n                                                                    children: lead.normalizedData.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 45\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                                    icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.EnvelopeIcon,\n                                                                    onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockEmail(lead.id),\n                                                                    children: \"Unlock Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1\",\n                                                                children: ((_lead_normalizedData2 = lead.normalizedData) === null || _lead_normalizedData2 === void 0 ? void 0 : _lead_normalizedData2.phone) && ((_lead_normalizedData3 = lead.normalizedData) === null || _lead_normalizedData3 === void 0 ? void 0 : _lead_normalizedData3.isPhoneVisible) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                                    title: lead.normalizedData.phone,\n                                                                    children: lead.normalizedData.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 45\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                                    icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.PhoneIcon,\n                                                                    onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockPhone(lead.id),\n                                                                    children: \"Unlock Mobile\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewLinksModal, {\n                                                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0\",\n                                                                        children: [\n                                                                            \"View links\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.ChevronRightIcon, {\n                                                                                className: \"size-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 63\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 49\n                                                                    }, void 0),\n                                                                    links: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getContactLinks(lead)) || []\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"w-12 px-2 relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeadActionsDropdown, {\n                                                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.EllipsisVerticalIcon, {\n                                                                            className: \"size-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                            lineNumber: 725,\n                                                                            columnNumber: 53\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 49\n                                                                    }, void 0),\n                                                                    leadData: lead,\n                                                                    onSendEmail: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleSendEmail(lead.id, lead),\n                                                                    onAddToSegments: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToSegments(lead.id),\n                                                                    onAddToDatabase: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToDatabase(lead.id, lead),\n                                                                    onAddToWorkflow: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToWorkflow(lead.id)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 37\n                                                            }, undefined)\n                                                        ]\n                                                    }, lead.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 33\n                                                    }, undefined);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 53\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b border-neutral-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                lineNumber: 627,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                open: saveDialogOpen,\n                onOpenChange: setSaveDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                    className: \"max-w-[95vw] sm:max-w-[600px] !rounded-none p-4\",\n                    \"aria-describedby\": \"save-search-description\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                children: \"Save Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 755,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 754,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    id: \"save-search-description\",\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: \"Enter a name for this saved search:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: searchName,\n                                    onChange: (e)=>setSearchName(e.target.value),\n                                    placeholder: \"Enter search name\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === \"Enter\") {\n                                            handleConfirmSave();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 757,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: ()=>setSaveDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: handleConfirmSave,\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                    lineNumber: 753,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                lineNumber: 752,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(FindLeads, \"FoJDO8RUZutziNYcumqiKlwK1SA=\", false, function() {\n    return [\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__.useRouter,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__.useParams,\n        _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_10__.useWorkspace,\n        _ui_providers_user__WEBPACK_IMPORTED_MODULE_11__.useAuth,\n        _ui_providers_alert__WEBPACK_IMPORTED_MODULE_12__.useAlert\n    ];\n});\n_c = FindLeads;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FindLeads);\nvar _c;\n$RefreshReg$(_c, \"FindLeads\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/find-leads.tsx\n"));

/***/ })

});