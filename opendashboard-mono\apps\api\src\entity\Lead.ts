import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"
import { LeadMeta } from "../businessLogic/lead";

export enum LeadType {
    Person = 'person',
    Company = 'company'
}

export enum LeadSource {
    Apollo = 'apollo',
    Manual = 'manual',
    Import = 'import'
}

export interface ApolloPersonData {
    id: string;
    first_name?: string;
    last_name?: string;
    name?: string;
    linkedin_url?: string;
    title?: string;
    email?: string;
    phone?: string;
    organization?: {
        id?: string;
        name?: string;
        website_url?: string;
        linkedin_url?: string;
        primary_domain?: string;
        industry?: string;
        keywords?: string[];
        estimated_num_employees?: number;
        organization_revenue?: number;
        organization_revenue_printed?: string;
        market_cap?: string;
        organization_headcount_six_month_growth?: number;
        organization_headcount_twelve_month_growth?: number;
        organization_headcount_twenty_four_month_growth?: number;
        show_intent?: boolean;
        intent_strength?: number;
        founded_year?: number;
        publicly_traded_symbol?: string;
        publicly_traded_exchange?: string;
        logo_url?: string;
        primary_phone?: {
            number?: string;
            source?: string;
            sanitized_number?: string;
        };
        languages?: string[];
        alexa_ranking?: number;
        retail_location_count?: number;
        stage?: string;
        short_description?: string;
        crunchbase_url?: string;
        angellist_url?: string;
        facebook_url?: string;
        twitter_url?: string;
        blog_url?: string;
        linkedin_uid?: string;
        sanitized_phone?: string;
        industry_tag_id?: string;
        industry_tag_hash?: Record<string, string>;
        snippets_loaded?: boolean;
        generic_org_insights?: any;
        secondary_industries?: string[];
        owned_by_organization?: {
            id?: string;
            name?: string;
            website_url?: string;
        };
        owned_by_organization_id?: string;
        intent_signal_account?: any;
        has_intent_signal_account?: boolean;
        current_technologies?: Array<{
            name?: string;
            category?: string;
            description?: string;
        }>;
    };
    employment_history?: Array<{
        company_name?: string;
        title?: string;
        start_date?: string;
        end_date?: string;
        is_current?: boolean;
    }>;
    photo_url?: string;
    twitter_url?: string;
    github_url?: string;
    facebook_url?: string;
    extrapolated_email_confidence?: number;
    headline?: string;
    country?: string;
    state?: string;
    city?: string;
    [key: string]: string | number | boolean | string[] | number[] | Record<string, string | number | boolean> | Array<{
        company_name?: string;
        title?: string;
        start_date?: string;
        end_date?: string;
        is_current?: boolean;
    }> | {
        id?: string;
        name?: string;
        website_url?: string;
        linkedin_url?: string;
        primary_domain?: string;
        industry?: string;
        keywords?: string[];
        estimated_num_employees?: number;
        organization_revenue?: number;
        organization_revenue_printed?: string;
        market_cap?: string;
        organization_headcount_six_month_growth?: number;
        organization_headcount_twelve_month_growth?: number;
        organization_headcount_twenty_four_month_growth?: number;
        show_intent?: boolean;
        intent_strength?: number;
        founded_year?: number;
        publicly_traded_symbol?: string;
        publicly_traded_exchange?: string;
        logo_url?: string;
        primary_phone?: {
            number?: string;
            source?: string;
            sanitized_number?: string;
        };
        languages?: string[];
        alexa_ranking?: number;
        retail_location_count?: number;
        stage?: string;
        short_description?: string;
        crunchbase_url?: string;
        angellist_url?: string;
        facebook_url?: string;
        twitter_url?: string;
        blog_url?: string;
        linkedin_uid?: string;
        sanitized_phone?: string;
        industry_tag_id?: string;
        industry_tag_hash?: Record<string, string>;
        snippets_loaded?: boolean;
        generic_org_insights?: any;
        secondary_industries?: string[];
        owned_by_organization?: {
            id?: string;
            name?: string;
            website_url?: string;
            sanitized_number?: string;
        };
        owned_by_organization_id?: string;
        intent_signal_account?: any;
        has_intent_signal_account?: boolean;
        current_technologies?: Array<{
            name?: string;
            category?: string;
            description?: string;
        }>;
    };
}

export interface ApolloCompanyData {
    id: string;
    name: string;
    website_url?: string;
    linkedin_url?: string;
    primary_domain?: string;
    industry?: string;
    keywords?: string[];
    estimated_num_employees?: number;
    retail_location_count?: number;
    stage?: string;
    short_description?: string;
    founded_year?: number;
    publicly_traded_symbol?: string;
    publicly_traded_exchange?: string;
    logo_url?: string;
    crunchbase_url?: string;
    primary_phone?: {
        number?: string;
        source?: string;
        sanitized_number?: string;
    };
    languages?: string[];
    alexa_ranking?: number;
    phone?: string;
    technologies?: string[];
    annual_revenue?: string;
    total_funding?: number;
    latest_funding_round_date?: string;
    latest_funding_stage?: string;
    seo_description?: string;
    technology_names?: string[];
    current_technologies?: Array<{
        name?: string;
        category?: string;
        description?: string;
    }>;
    account_id?: string;
    organization_revenue?: number;
    organization_revenue_printed?: string;
    market_cap?: string;
    organization_headcount_six_month_growth?: number;
    organization_headcount_twelve_month_growth?: number;
    organization_headcount_twenty_four_month_growth?: number;
    show_intent?: boolean;
    intent_strength?: number;
    intent_signal_account?: any;
    has_intent_signal_account?: boolean;
    blog_url?: string;
    angellist_url?: string;
    facebook_url?: string;
    twitter_url?: string;
    owned_by_organization?: {
        id?: string;
        name?: string;
        website_url?: string;
    };
    owned_by_organization_id?: string;
    sanitized_phone?: string;
    linkedin_uid?: string;
    industry_tag_id?: string;
    industry_tag_hash?: Record<string, string>;
    snippets_loaded?: boolean;
    generic_org_insights?: any;
    secondary_industries?: string[];
    
    [key: string]: string | number | boolean | string[] | number[] | Record<string, string | number | boolean> | Array<{
        name?: string;
        category?: string;
        description?: string;
    }> | {
        number?: string;
        source?: string;
        sanitized_number?: string;
    } | {
        id?: string;
        name?: string;
        website_url?: string;
    } | Record<string, string>;
}

export interface NormalizedLeadData {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    jobTitle?: string;
    company?: string;
    companyDomain?: string;
    linkedinUrl?: string;
    photoUrl?: string;
    location?: {
        country?: string;
        state?: string;
        city?: string;
    };
    isEmailVisible: boolean;
    isPhoneVisible: boolean;
    confidence?: number;
    // Store original email and phone for company leads (hidden until unlocked)
    originalEmail?: string;
    originalPhone?: string;
}

@Entity()
@Index(["workspaceId", "apolloId"], { unique: true })
@Index(["workspaceId", "type"])
export class Lead {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string;

    @Index()
    @Column({type: 'varchar', nullable: true, length: 255})
    apolloId: string;

    @Index()
    @Column({type: 'varchar', nullable: false, length: 50})
    type: LeadType;

    @Index()
    @Column({type: 'varchar', nullable: false, default: LeadSource.Apollo, length: 50})
    source: LeadSource;

    @Column({type: "json", nullable: true})
    apolloData: ApolloPersonData | ApolloCompanyData;

    @Column({type: "json", nullable: false})
    normalizedData: NormalizedLeadData;

    @Column({type: "json", nullable: true})
    searchHashes: string[];

    @Index()
    @Column({type: 'varchar', nullable: true, length: 255})
    email: string;
 
    @Index()
    @Column({type: 'varchar', nullable: true, length: 255})
    companyDomain: string;

    @Index()
    @Column({type: 'varchar', nullable: true, length: 255})
    name: string;

    @Index()
    @Column({default: false, type: 'boolean'})
    isUnlocked: boolean;

    @Index()
    @Column({type: 'timestamp', nullable: true})
    lastEnrichedAt: Date;

    @Index()
    @Column({type: 'varchar', nullable: true})
    createdById: string;

    @Index()
    @Column({type: 'varchar', nullable: true})
    updatedById: string;

    @CreateDateColumn({ type: 'timestamp', default: 0 })
    createdAt: Date;

    @UpdateDateColumn({ type: 'timestamp', default: 0 })
    updatedAt: Date;

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date;

    @Column({type: "json", nullable: true})
    meta: LeadMeta;
}
