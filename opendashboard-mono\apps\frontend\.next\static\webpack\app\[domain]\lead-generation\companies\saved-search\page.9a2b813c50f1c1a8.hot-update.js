"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/lead-generation/companies/saved-search/page",{

/***/ "(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/find-leads.tsx":
/*!**********************************************************************************************!*\
  !*** ../../packages/ui/src/components/workspace/main/lead-generation/company/find-leads.tsx ***!
  \**********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ui/components/ui/button */ \"(app-pages-browser)/../../packages/ui/src/components/ui/button.tsx\");\n/* harmony import */ var _ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ui/components/ui/input */ \"(app-pages-browser)/../../packages/ui/src/components/ui/input.tsx\");\n/* harmony import */ var _ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ui/components/ui/checkbox */ \"(app-pages-browser)/../../packages/ui/src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ui/components/ui/table */ \"(app-pages-browser)/../../packages/ui/src/components/ui/table.tsx\");\n/* harmony import */ var _ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ui/components/ui/scroll-area */ \"(app-pages-browser)/../../packages/ui/src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ui/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/../../packages/ui/src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/.pnpm/@heroicons+react@2.2.0_react@18.3.1/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassCircleIcon.js\");\n/* harmony import */ var _ui_components_workspace_main_lead_generation_filters_company__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ui/components/workspace/main/lead-generation/filters/company */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/filters/company.tsx\");\n/* harmony import */ var _ui_api_leads__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ui/api/leads */ \"(app-pages-browser)/../../packages/ui/src/api/leads.ts\");\n/* harmony import */ var _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @ui/providers/workspace */ \"(app-pages-browser)/../../packages/ui/src/providers/workspace.tsx\");\n/* harmony import */ var _ui_providers_user__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ui/providers/user */ \"(app-pages-browser)/../../packages/ui/src/providers/user.tsx\");\n/* harmony import */ var _ui_providers_alert__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ui/providers/alert */ \"(app-pages-browser)/../../packages/ui/src/providers/alert.tsx\");\n/* harmony import */ var _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @ui/context/routerContext */ \"(app-pages-browser)/../../packages/ui/src/context/routerContext.tsx\");\n/* harmony import */ var _ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ui/components/ui/dialog */ \"(app-pages-browser)/../../packages/ui/src/components/ui/dialog.tsx\");\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./index */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/index.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// All components are now imported from index\nconst FindLeads = (param)=>{\n    let { onLeadCreated, sidebarState, ActionButton, ViewLinksModal, LeadActionsDropdown, leadActions, leadManagement } = param;\n    _s();\n    const router = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const params = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__.useParams)();\n    const { workspace } = (0,_ui_providers_workspace__WEBPACK_IMPORTED_MODULE_10__.useWorkspace)();\n    const { token } = (0,_ui_providers_user__WEBPACK_IMPORTED_MODULE_11__.useAuth)();\n    const { toast } = (0,_ui_providers_alert__WEBPACK_IMPORTED_MODULE_12__.useAlert)();\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Search filters state - ADDED\n    const [searchFilters, setSearchFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        person: {},\n        company: {},\n        signals: {},\n        customFilters: {}\n    });\n    // Exclude my leads state - ADDED\n    const [excludeMyLeads, setExcludeMyLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Modal states are now managed by shared hook\n    const [isSavingSearch, setIsSavingSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSearchId, setCurrentSearchId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Dialog state for save search\n    const [saveDialogOpen, setSaveDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchName, setSearchName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Register unlock success callbacks\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockEmailCallback) {\n            leadManagement.setUnlockEmailCallback((unlockedLead)=>{\n                var _unlockedLead_apolloData;\n                const normalizedData = (unlockedLead === null || unlockedLead === void 0 ? void 0 : unlockedLead.normalizedData) || (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_apolloData = unlockedLead.apolloData) === null || _unlockedLead_apolloData === void 0 ? void 0 : _unlockedLead_apolloData.normalizedData);\n                // Update the lead in the list with unlocked data\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _lead_normalizedData;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || \"unlock\",\n                            normalizedData: {\n                                ...lead.normalizedData,\n                                email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email),\n                                isEmailVisible: true // Set to true after successful unlock\n                            }\n                        } : lead;\n                    }));\n            });\n        }\n        if (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setUnlockPhoneCallback) {\n            leadManagement.setUnlockPhoneCallback((unlockedLead)=>{\n                var _unlockedLead_apolloData;\n                const normalizedData = (unlockedLead === null || unlockedLead === void 0 ? void 0 : unlockedLead.normalizedData) || (unlockedLead === null || unlockedLead === void 0 ? void 0 : (_unlockedLead_apolloData = unlockedLead.apolloData) === null || _unlockedLead_apolloData === void 0 ? void 0 : _unlockedLead_apolloData.normalizedData);\n                // Update the lead in the list with unlocked data\n                setLeads((prev)=>prev.map((lead)=>{\n                        var _lead_normalizedData, _lead_normalizedData1, _lead_normalizedData2;\n                        return lead.id === (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeadId) ? {\n                            ...lead,\n                            phone: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.phone) || \"unlock\",\n                            email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || lead.email,\n                            normalizedData: {\n                                ...lead.normalizedData,\n                                email: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.email) || ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email),\n                                phone: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.phone) || ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.phone),\n                                isEmailVisible: (normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.isEmailVisible) || ((_lead_normalizedData2 = lead.normalizedData) === null || _lead_normalizedData2 === void 0 ? void 0 : _lead_normalizedData2.isEmailVisible),\n                                isPhoneVisible: true // Set to true after successful unlock\n                            }\n                        } : lead;\n                    }));\n            });\n        }\n    }, [\n        leadManagement,\n        leads.length\n    ]);\n    // Initialize searchFilters with default values when component mounts\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const defaultFilters = {\n            person: {},\n            company: {},\n            signals: {},\n            customFilters: {}\n        };\n        setSearchFilters(defaultFilters);\n    }, []);\n    // Search functionality\n    const searchLeads = async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : searchFilters, page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        var _workspace_workspace;\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] searchLeads called\");\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Input filters:\", JSON.stringify(filters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Current searchFilters state:\", JSON.stringify(searchFilters, null, 2));\n        if (!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || !(token === null || token === void 0 ? void 0 : token.token)) {\n            toast.error(\"Authentication required\");\n            return;\n        }\n        // Check if API is properly configured\n        if (false) {}\n        setIsSearching(true);\n        try {\n            var _response_data_data, _response_data, _response_data_data1, _response_data1, _response_data_data2, _response_data2, _response_data_data3, _response_data3, _response_data_data4, _response_data4, _response_data_data5, _response_data5, _response_data_data_metadata, _response_data_data6, _response_data6, _response_data_data7, _response_data7;\n            // Clean filters before sending - excludeMyLeads is NOT a search filter\n            const cleanFilters = {\n                ...filters\n            };\n            // excludeMyLeads is already a separate state variable, no need to delete from filters\n            // Proper frontend pagination through accumulated results\n            // Calculate how many pages we have available from previous searches\n            // const totalPagesAvailable = searchResults ? Math.ceil(searchResults.totalCount / 50) : 1; // Removed\n            // const currentPage = searchResults && searchResults.hasNextPage ? \n            //     ((pageCounter % totalPagesAvailable) + 1) : 1; // Removed\n            // setPageCounter(prev => prev + 1) // Removed\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDD27 FILTERS PREPARATION:\");\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] - Search filters: \".concat(JSON.stringify(filters, null, 2)));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] - excludeMyLeads (separate): \".concat(excludeMyLeads));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] - excludeMyLeads is NOT part of search criteria\");\n            // console.log(`🔍 [FRONTEND DEBUG] - Total pages available: ${totalPagesAvailable}`); // Removed\n            // console.log(`🔍 [FRONTEND DEBUG] - Page counter: ${pageCounter + 1}, Requesting page: ${currentPage}`); // Removed\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ==========================================\");\n            const searchRequest = {\n                filters: cleanFilters,\n                excludeMyLeads: excludeMyLeads,\n                pagination: {\n                    page: page,\n                    limit: 50\n                }\n            };\n            console.log('\\uD83D\\uDD0D [FRONTEND DEBUG] Search query: \"\"'); // searchQuery removed\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Workspace ID: \".concat(workspace.workspace.id));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Token exists: \".concat(!!token.token));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] API URL: \".concat(\"http://localhost:3033/api/v1\"));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDE80 Sending search request to API:\", JSON.stringify(searchRequest, null, 2));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Calling searchCompanyLeads API function...\");\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_9__.searchCompanyLeads)(token.token, workspace.workspace.id, searchRequest);\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] API response received:\", response);\n            if (response.error) {\n                // Provide user-friendly error messages\n                const errorMessage = response.error.toLowerCase();\n                if (errorMessage.includes(\"unauthorized\") || errorMessage.includes(\"authentication\")) {\n                    toast.error(\"Session expired. Please log in again.\");\n                } else if (errorMessage.includes(\"network\") || errorMessage.includes(\"connection\")) {\n                    toast.error(\"Connection error. Please check your internet and try again.\");\n                } else if (errorMessage.includes(\"env\") || errorMessage.includes(\"undefined\")) {\n                    toast.error(\"Search service is currently unavailable. Please try again later.\");\n                } else {\n                    toast.error(\"Failed to search company leads. Please try again.\");\n                }\n                return;\n            }\n            const apiLeads = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.leads) || [];\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ✅ API leads extracted:\", {\n                leadsCount: apiLeads.length,\n                firstLead: apiLeads[0] || null,\n                allLeads: apiLeads\n            });\n            // Convert API leads to UI format using shared logic\n            const convertedLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.convertApiLeadsToUI(apiLeads)) || [];\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Converted leads for UI:\", {\n                convertedCount: convertedLeads.length,\n                firstConverted: convertedLeads[0] || null\n            });\n            setLeads(convertedLeads);\n            // Store search results with proper structure for pagination display\n            setSearchResults({\n                totalCount: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data1 = _response_data1.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1.totalCount,\n                metadata: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : (_response_data_data2 = _response_data2.data) === null || _response_data_data2 === void 0 ? void 0 : _response_data_data2.metadata) || {},\n                hasNextPage: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_data3 = _response_data3.data) === null || _response_data_data3 === void 0 ? void 0 : _response_data_data3.hasNextPage\n            });\n            // Update pagination state\n            const responseTotalCount = ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_data4 = _response_data4.data) === null || _response_data_data4 === void 0 ? void 0 : _response_data_data4.totalCount) || 0;\n            const responseHasNextPage = ((_response_data5 = response.data) === null || _response_data5 === void 0 ? void 0 : (_response_data_data5 = _response_data5.data) === null || _response_data_data5 === void 0 ? void 0 : _response_data_data5.hasNextPage) || false;\n            // Calculate total pages based on cached results\n            // Show ALL cached pages + ONE page to trigger Apollo expansion\n            let availablePages = 1;\n            // Use the backend's totalPagesAvailable to show all cached pages\n            // This ensures users can navigate to ALL available pages, plus one more to trigger Apollo\n            // Example: If backend has 14 pages, show 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15\n            // Where page 15 triggers Apollo to get more leads\n            const totalPagesAvailable = ((_response_data6 = response.data) === null || _response_data6 === void 0 ? void 0 : (_response_data_data6 = _response_data6.data) === null || _response_data_data6 === void 0 ? void 0 : (_response_data_data_metadata = _response_data_data6.metadata) === null || _response_data_data_metadata === void 0 ? void 0 : _response_data_data_metadata.totalPagesAvailable) || 1;\n            availablePages = totalPagesAvailable + 1;\n            setTotalCount(responseTotalCount);\n            setTotalPages(availablePages);\n            setCurrentPage(page) // Set to the page we just searched for\n            ;\n            // Track the current search ID for saving functionality\n            const searchId = ((_response_data7 = response.data) === null || _response_data7 === void 0 ? void 0 : (_response_data_data7 = _response_data7.data) === null || _response_data_data7 === void 0 ? void 0 : _response_data_data7.searchId) || \"\";\n            setCurrentSearchId(searchId);\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDCCA Pagination updated:\", {\n                currentPage: page,\n                totalPages: availablePages,\n                totalCount: responseTotalCount,\n                leadsPerPage: 50\n            });\n            setHasSearched(true);\n            toast.success(\"Found \".concat(convertedLeads.length, \" company leads\"));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ✅ Search completed successfully. State updated.\");\n        } catch (error) {\n            console.error(\"Search error:\", error);\n            // Provide user-friendly error message without exposing technical details\n            toast.error(\"Search service is temporarily unavailable. Please try again later.\");\n        } finally{\n            setIsSearching(false);\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Search state reset to not searching\");\n        }\n    };\n    // Handle filter changes from CompanyFilter\n    const handleFilterChange = (filters)=>{\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] handleFilterChange called with:\", JSON.stringify(filters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Previous searchFilters:\", JSON.stringify(searchFilters, null, 2));\n        // Reset pagination when filters change - this ensures fresh results from page 1\n        // when user changes search criteria (industry, company size, etc.)\n        setCurrentPage(1);\n        setTotalPages(1);\n        setTotalCount(0);\n        setCurrentSearchId(\"\");\n        setHasSearched(false);\n        setLeads([]);\n        setSearchResults(null);\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDD04 Pagination and search state reset to page 1 (filters changed)\");\n        // Simply replace the filters completely - CompanyFilter sends the complete state\n        // No need to merge as it can cause conflicts and state inconsistencies\n        setSearchFilters(filters) // FIXED: Now properly updates the state\n        ;\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] searchFilters state updated to:\", JSON.stringify(filters, null, 2));\n    // Don't auto-search! Let user control search via Search button\n    // This provides a much cleaner UX flow\n    };\n    // Pagination functions\n    const handlePageChange = (page)=>{\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Page change requested: \".concat(page));\n        if (page < 1) {\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ❌ Invalid page: \".concat(page, \" - cannot be less than 1\"));\n            return;\n        }\n        // Allow clicking beyond current totalPages - this will trigger Apollo calls\n        // when the backend detects the requested page doesn't exist in cache\n        if (page > totalPages) {\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] \\uD83D\\uDD04 Page \".concat(page, \" requested beyond current cache (\").concat(totalPages, \" pages)\"));\n            console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] This will trigger Apollo call to get more leads\");\n        }\n        // Automatically search for the page with current filters - this is the fix!\n        // This ensures pagination works correctly without requiring extra button clicks\n        setCurrentPage(page);\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] ✅ Page set to \".concat(page, \". Automatically searching for page \").concat(page, \" results\"));\n        // Use current filters and search for the specific page\n        searchLeads(searchFilters, page);\n    };\n    const calculateTotalPages = function(totalLeads) {\n        let leadsPerPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        return Math.ceil(totalLeads / leadsPerPage);\n    };\n    const generatePageNumbers = ()=>{\n        const pages = [];\n        const maxVisiblePages = 5 // Show max 5 page numbers at once\n        ;\n        if (totalPages <= maxVisiblePages) {\n            // Show all pages if total is small\n            for(let i = 1; i <= totalPages; i++){\n                pages.push(i);\n            }\n        } else {\n            // Show smart pagination with ellipsis\n            if (currentPage <= 3) {\n                // Near start: show 1, 2, 3, 4, 5, ..., last\n                for(let i = 1; i <= 5; i++){\n                    pages.push(i);\n                }\n                pages.push(\"...\");\n                pages.push(totalPages);\n            } else if (currentPage >= totalPages - 2) {\n                // Near end: show 1, ..., last-4, last-3, last-2, last-1, last\n                pages.push(1);\n                pages.push(\"...\");\n                for(let i = totalPages - 4; i <= totalPages; i++){\n                    pages.push(i);\n                }\n            } else {\n                // Middle: show 1, ..., current-1, current, current+1, ..., last\n                pages.push(1);\n                pages.push(\"...\");\n                for(let i = currentPage - 1; i <= currentPage + 1; i++){\n                    pages.push(i);\n                }\n                pages.push(\"...\");\n                pages.push(totalPages);\n            }\n        }\n        return pages;\n    };\n    // Manual search trigger\n    const handleSearch = ()=>{\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] handleSearch called\");\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Current searchFilters:\", JSON.stringify(searchFilters, null, 2));\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Current page: \".concat(currentPage));\n        console.log('\\uD83D\\uDD0D [FRONTEND DEBUG] Current searchQuery: \"\"'); // searchQuery removed\n        // Use the current page that user selected (don't reset to 1)\n        // This allows users to click page 2, then click Search to get page 2 results\n        // ONLY send sidebar filters - ignore search input field\n        // Search input is for a different purpose, not for Apollo searches\n        const filtersToSend = {\n            ...searchFilters // FIXED: Now uses the actual searchFilters state\n        };\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Final filters being sent to searchLeads (sidebar filters only):\", JSON.stringify(filtersToSend, null, 2));\n        console.log(\"\\uD83D\\uDD0D [FRONTEND DEBUG] Searching for page: \".concat(currentPage));\n        searchLeads(filtersToSend, currentPage) // Use current page, not always page 1\n        ;\n    };\n    // Event handlers\n    const handleCreateLead = ()=>{\n        console.log(\"Create company lead clicked\");\n    };\n    // handleImportLeads is now provided by shared hook\n    // Selection handlers are now provided by shared hook\n    // Save current search results as a saved search\n    const handleSaveLeads = ()=>{\n        var _workspace_workspace, _workspace_workspace1;\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] \\uD83D\\uDE80 User clicked Save Leads button\");\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] \\uD83D\\uDCCA Current state:\", {\n            hasWorkspace: !!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id),\n            hasToken: !!(token === null || token === void 0 ? void 0 : token.token),\n            hasSearched,\n            leadsCount: leads.length,\n            currentSearchId,\n            currentPage,\n            searchFilters: searchFilters\n        });\n        if (!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace1 = workspace.workspace) === null || _workspace_workspace1 === void 0 ? void 0 : _workspace_workspace1.id) || !(token === null || token === void 0 ? void 0 : token.token)) {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] ❌ Missing authentication\");\n            toast.error(\"Authentication required\");\n            return;\n        }\n        if (!hasSearched || leads.length === 0) {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] ❌ No search results to save\");\n            toast.error(\"No search results to save\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] ✅ Validation passed, opening save dialog\");\n        // Set default name and open dialog\n        const defaultName = \"Company Search - \".concat(new Date().toLocaleDateString());\n        console.log(\"\\uD83D\\uDCBE [FRONTEND SAVE LEADS - COMPANY] \\uD83D\\uDCDD Setting default name:\", defaultName);\n        setSearchName(defaultName);\n        setSaveDialogOpen(true);\n    };\n    // Handle the actual save operation\n    const handleConfirmSave = async ()=>{\n        var _workspace_workspace;\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDE80 User confirmed save operation\");\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDCCA Save details:\", {\n            searchName: searchName === null || searchName === void 0 ? void 0 : searchName.trim(),\n            leadsCount: leads.length,\n            currentPage,\n            currentSearchId,\n            hasToken: !!(token === null || token === void 0 ? void 0 : token.token),\n            hasWorkspace: !!(workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id)\n        });\n        if (!searchName || searchName.trim() === \"\") {\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Empty search name\");\n            toast.error(\"Search name cannot be empty\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ✅ Starting save process...\");\n        setIsSavingSearch(true);\n        setSaveDialogOpen(false);\n        try {\n            var _response_data;\n            const saveRequest = {\n                name: searchName.trim(),\n                description: \"Saved company search with \".concat(leads.length, \" leads from page \").concat(currentPage),\n                filters: searchFilters,\n                searchId: currentSearchId || undefined,\n                searchType: \"company\"\n            };\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDCE4 Sending save request:\", saveRequest);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD17 API call: saveSearch()\");\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_9__.saveSearch)(token.token, workspace.workspace.id, saveRequest);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDCE5 Received response from backend\");\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D Full response:\", response);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D Response keys:\", Object.keys(response));\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D isSuccess:\", response.isSuccess);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D error:\", response.error);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D data:\", response.data);\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDD0D data.data:\", (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data);\n            if (response.isSuccess) {\n                var _response_data_data_savedSearch, _response_data_data, _response_data1;\n                console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ✅ Save operation successful!\");\n                console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83D\\uDCCB Saved search details:\", {\n                    name: searchName,\n                    leadsCount: leads.length,\n                    searchId: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : (_response_data_data_savedSearch = _response_data_data.savedSearch) === null || _response_data_data_savedSearch === void 0 ? void 0 : _response_data_data_savedSearch.id,\n                    searchType: \"company\"\n                });\n                toast.success('Saved \"'.concat(searchName, '\" with ').concat(leads.length, \" company leads\"));\n            } else {\n                console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Save operation failed\");\n                console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error details:\", {\n                    isSuccess: response.isSuccess,\n                    error: response.error,\n                    status: response.status\n                });\n                toast.error(response.error || \"Failed to save search\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Exception during save operation:\", error);\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error type:\", typeof error);\n            console.error(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] ❌ Error message:\", error instanceof Error ? error.message : \"Unknown error\");\n            toast.error(\"Failed to save search. Please try again.\");\n        } finally{\n            console.log(\"\\uD83D\\uDCBE [FRONTEND CONFIRM SAVE - COMPANY] \\uD83C\\uDFC1 Save operation completed, resetting UI state\");\n            setIsSavingSearch(false);\n        }\n    };\n    // Unlock handlers are now provided by shared hook\n    // Generate contact links based on lead data\n    // getContactLinks is now provided by shared hook\n    const handleNameClick = (lead)=>{\n        // Navigate to company details page using router\n        const domain = params.domain;\n        router.push(\"/\".concat(domain, \"/lead-generation/companies/details/\").concat(lead.id));\n    };\n    // filteredLeads is now provided by shared hook\n    const filteredLeads = (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getFilteredLeads(leads, undefined, undefined)) || leads;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xs font-semibold text-black\",\n                            children: \"Find Companies\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer\",\n                                onClick: ()=>{\n                                    setLeads(leads.filter((lead)=>!(leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id))));\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.setSelectedLeads([]);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.TrashIcon, {\n                                        className: \"size-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    \"Delete \",\n                                    leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: totalPages > 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>handlePageChange(Math.max(1, currentPage - 1)),\n                                                    disabled: currentPage === 1,\n                                                    className: \"h-6 w-6 p-0 text-xs\",\n                                                    children: \"←\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: generatePageNumbers().map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                            children: page === \"...\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-1 text-neutral-400 text-xs\",\n                                                                children: \"...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 57\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: currentPage === page ? \"default\" : \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handlePageChange(page),\n                                                                className: \"h-6 w-6 p-0 text-xs \".concat(currentPage === page ? \"bg-primary text-white\" : \"hover:bg-neutral-50\"),\n                                                                children: page\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 49\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>handlePageChange(Math.min(totalPages, currentPage + 1)),\n                                                    disabled: currentPage === totalPages,\n                                                    className: \"h-6 w-6 p-0 text-xs\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 37\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-neutral-500\",\n                                            children: \"Single page\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"size-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                placeholder: \"Search and find companies\",\n                                                // value={searchQuery} // searchQuery removed\n                                                // onChange={(e) => setSearchQuery(e.target.value)} // searchQuery removed\n                                                onKeyDown: (e)=>e.key === \"Enter\" && handleSearch(),\n                                                className: \"text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        onClick: handleSearch,\n                                        disabled: isSearching,\n                                        className: \"text-xs rounded-full h-auto px-3 py-1.5\",\n                                        children: isSearching ? \"Searching...\" : \"Search\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    hasSearched && leads.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSaveLeads,\n                                        disabled: isSavingSearch,\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        className: \"text-xs rounded-full h-auto px-3 py-1.5 gap-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.DatabaseIcon, {\n                                                className: \"size-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            isSavingSearch ? \"Saving...\" : \"Save \".concat(leads.length, \" Companies\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                lineNumber: 544,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_workspace_main_lead_generation_filters_company__WEBPACK_IMPORTED_MODULE_8__.CompanyFilter, {\n                        forceSidebar: true,\n                        onFilterChange: handleFilterChange,\n                        excludeMyLeads: excludeMyLeads,\n                        onExcludeMyLeadsChange: (value)=>{\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] Parent received excludeMyLeads change:\");\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] - Previous value: \".concat(excludeMyLeads));\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] - New value: \".concat(value));\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] - This will affect display filtering, not search criteria\");\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] ==========================================\");\n                            setExcludeMyLeads(value) // FIXED: Now properly updates the state\n                            ;\n                            console.log(\"\\uD83D\\uDD12 [FRONTEND DEBUG] excludeMyLeads state updated to: \".concat(value));\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: filteredLeads.length === 0 ? /* Empty State - INSIDE the container */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.MagnifyingGlassIcon, {\n                                        className: \"size-12 text-neutral-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-neutral-900 mb-2\",\n                                        children: hasSearched ? \"No companies found\" : \"Ready to find companies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-neutral-500 mb-4 text-sm\",\n                                        children: hasSearched ? \"Try adjusting your search query or filters to find more results\" : \"Use the search box above or apply filters on the left to discover companies. Click the Search button to start.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 679,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 25\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                                className: \"size-full scrollBlockChild\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_15__.CompanyTableHeader, {\n                                                selectedLeads: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads) || [],\n                                                filteredLeads: filteredLeads,\n                                                handleSelectAll: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectAll) || (()=>{})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                                children: filteredLeads.map((lead)=>{\n                                                    var _lead_normalizedData, _lead_normalizedData1, _lead_normalizedData2, _lead_normalizedData3;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                        className: \"border-b border-neutral-200 hover:bg-neutral-50 transition-colors group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"w-12 px-3 relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_4__.Checkbox, {\n                                                                    checked: leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id),\n                                                                    onCheckedChange: (checked)=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleSelectLead(lead.id, checked),\n                                                                    className: \"\".concat((leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.selectedLeads.includes(lead.id)) ? \"opacity-100 visible\" : \"invisible opacity-0 group-hover:opacity-100 group-hover:visible\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0\",\n                                                                    onClick: ()=>handleNameClick(lead),\n                                                                    children: lead.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1 text-xs text-muted-foreground\",\n                                                                children: lead.industry\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 716,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1 text-xs text-muted-foreground\",\n                                                                children: lead.location\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1\",\n                                                                children: ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email) && ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.isEmailVisible) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                                    title: lead.normalizedData.email,\n                                                                    children: lead.normalizedData.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 45\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                                    icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.EnvelopeIcon,\n                                                                    onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockEmail(lead.id),\n                                                                    children: \"Unlock Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1\",\n                                                                children: ((_lead_normalizedData2 = lead.normalizedData) === null || _lead_normalizedData2 === void 0 ? void 0 : _lead_normalizedData2.phone) && ((_lead_normalizedData3 = lead.normalizedData) === null || _lead_normalizedData3 === void 0 ? void 0 : _lead_normalizedData3.isPhoneVisible) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-black font-medium truncate max-w-[120px]\",\n                                                                    title: lead.normalizedData.phone,\n                                                                    children: lead.normalizedData.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 45\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActionButton, {\n                                                                    icon: _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.PhoneIcon,\n                                                                    onClick: ()=>leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.handleUnlockPhone(lead.id),\n                                                                    children: \"Unlock Mobile\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 45\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"px-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewLinksModal, {\n                                                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0\",\n                                                                        children: [\n                                                                            \"View links\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.ChevronRightIcon, {\n                                                                                className: \"size-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                                lineNumber: 740,\n                                                                                columnNumber: 63\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                        lineNumber: 739,\n                                                                        columnNumber: 49\n                                                                    }, void 0),\n                                                                    links: (leadManagement === null || leadManagement === void 0 ? void 0 : leadManagement.getContactLinks(lead)) || []\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 37\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"w-12 px-2 relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LeadActionsDropdown, {\n                                                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_7__.EllipsisVerticalIcon, {\n                                                                            className: \"size-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 53\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 49\n                                                                    }, void 0),\n                                                                    leadData: lead,\n                                                                    onSendEmail: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleSendEmail(lead.id, lead),\n                                                                    onAddToSegments: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToSegments(lead.id),\n                                                                    onAddToDatabase: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToDatabase(lead.id, lead),\n                                                                    onAddToWorkflow: ()=>leadActions === null || leadActions === void 0 ? void 0 : leadActions.handleAddToWorkflow(lead.id)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                    lineNumber: 747,\n                                                                    columnNumber: 41\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 37\n                                                            }, undefined)\n                                                        ]\n                                                    }, lead.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 33\n                                                    }, undefined);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 53\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b border-neutral-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                        lineNumber: 675,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                lineNumber: 656,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                open: saveDialogOpen,\n                onOpenChange: setSaveDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                    className: \"max-w-[95vw] sm:max-w-[600px] !rounded-none p-4\",\n                    \"aria-describedby\": \"save-search-description\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                children: \"Save Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                lineNumber: 784,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 783,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    id: \"save-search-description\",\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: \"Enter a name for this saved search:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: searchName,\n                                    onChange: (e)=>setSearchName(e.target.value),\n                                    placeholder: \"Enter search name\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === \"Enter\") {\n                                            handleConfirmSave();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 786,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: ()=>setSaveDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: handleConfirmSave,\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                    lineNumber: 782,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\find-leads.tsx\",\n                lineNumber: 781,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(FindLeads, \"FoJDO8RUZutziNYcumqiKlwK1SA=\", false, function() {\n    return [\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__.useRouter,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_13__.useParams,\n        _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_10__.useWorkspace,\n        _ui_providers_user__WEBPACK_IMPORTED_MODULE_11__.useAuth,\n        _ui_providers_alert__WEBPACK_IMPORTED_MODULE_12__.useAlert\n    ];\n});\n_c = FindLeads;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FindLeads);\nvar _c;\n$RefreshReg$(_c, \"FindLeads\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS9zcmMvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi9sZWFkLWdlbmVyYXRpb24vY29tcGFueS9maW5kLWxlYWRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0Q7QUFDQztBQUNGO0FBQ007QUFDa0Q7QUFDN0M7QUFDMkw7QUFDOUs7QUFFc0I7QUFLL0I7QUFFUjtBQUNWO0FBQ0U7QUFDa0I7QUFDeUM7QUFDN0Q7QUE0QjVDLDZDQUE2QztBQUU3QyxNQUFNZ0MsWUFBWTtRQUFDLEVBQUVDLGFBQWEsRUFBRUMsWUFBWSxFQUFFQyxZQUFZLEVBQUVDLGNBQWMsRUFBRUMsbUJBQW1CLEVBQUVDLFdBQVcsRUFBRUMsY0FBYyxFQUFrQjs7SUFDOUksTUFBTUMsU0FBU2hCLHFFQUFTQTtJQUN4QixNQUFNaUIsU0FBU2hCLHFFQUFTQTtJQUN4QixNQUFNLEVBQUVpQixTQUFTLEVBQUUsR0FBR3JCLHNFQUFZQTtJQUNsQyxNQUFNLEVBQUVzQixLQUFLLEVBQUUsR0FBR3JCLDREQUFPQTtJQUN6QixNQUFNLEVBQUVzQixLQUFLLEVBQUUsR0FBR3JCLDhEQUFRQTtJQUcxQixNQUFNLENBQUNzQixPQUFPQyxTQUFTLEdBQUc3QywrQ0FBUUEsQ0FBUyxFQUFFO0lBQzdDLE1BQU0sQ0FBQzhDLGFBQWFDLGVBQWUsR0FBRy9DLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2dELGFBQWFDLGVBQWUsR0FBR2pELCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2tELGVBQWVDLGlCQUFpQixHQUFHbkQsK0NBQVFBLENBQU07SUFFeEQsK0JBQStCO0lBQy9CLE1BQU0sQ0FBQ29ELGVBQWVDLGlCQUFpQixHQUFHckQsK0NBQVFBLENBQWdCO1FBQzlEc0QsUUFBUSxDQUFDO1FBQ1RDLFNBQVMsQ0FBQztRQUNWQyxTQUFTLENBQUM7UUFDVkMsZUFBZSxDQUFDO0lBQ3BCO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBRzNELCtDQUFRQSxDQUFDO0lBR3JELE1BQU0sQ0FBQzRELGFBQWFDLGVBQWUsR0FBRzdELCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzhELFlBQVlDLGNBQWMsR0FBRy9ELCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2dFLFlBQVlDLGNBQWMsR0FBR2pFLCtDQUFRQSxDQUFDO0lBRTdDLDhDQUE4QztJQUM5QyxNQUFNLENBQUNrRSxnQkFBZ0JDLGtCQUFrQixHQUFHbkUsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDb0UsaUJBQWlCQyxtQkFBbUIsR0FBR3JFLCtDQUFRQSxDQUFTO0lBRS9ELCtCQUErQjtJQUMvQixNQUFNLENBQUNzRSxnQkFBZ0JDLGtCQUFrQixHQUFHdkUsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDd0UsWUFBWUMsY0FBYyxHQUFHekUsK0NBQVFBLENBQUM7SUFHN0Msb0NBQW9DO0lBQ3BDRCxzREFBZSxDQUFDO1FBQ1osSUFBSXVDLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxQyxzQkFBc0IsRUFBRTtZQUN4Q3JDLGVBQWVxQyxzQkFBc0IsQ0FBQyxDQUFDQztvQkFDb0JBO2dCQUF2RCxNQUFNQyxpQkFBaUJELENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY0MsY0FBYyxNQUFJRCx5QkFBQUEsb0NBQUFBLDJCQUFBQSxhQUFjRSxVQUFVLGNBQXhCRiwrQ0FBQUEseUJBQTBCQyxjQUFjO2dCQUUvRixpREFBaUQ7Z0JBQ2pEaEMsU0FBU2tDLENBQUFBLE9BQ0xBLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUE7NEJBT3VDOytCQU41Q0EsS0FBS0MsRUFBRSxNQUFLNUMsMkJBQUFBLHFDQUFBQSxlQUFnQjZDLGNBQWMsSUFDcEM7NEJBQ0UsR0FBR0YsSUFBSTs0QkFDUEcsT0FBT1AsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQk8sS0FBSyxLQUFJOzRCQUNoQ1AsZ0JBQWdCO2dDQUNaLEdBQUcsS0FBY0EsY0FBYztnQ0FDL0JPLE9BQU9QLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JPLEtBQUssT0FBSSw0QkFBY1AsY0FBYyxjQUE1QixnRUFBOEJPLEtBQUs7Z0NBQ25FQyxnQkFBZ0IsS0FBSyxzQ0FBc0M7NEJBQy9EO3dCQUNKLElBQ0VKOztZQUdsQjtRQUNKO1FBRUEsSUFBSTNDLDJCQUFBQSxxQ0FBQUEsZUFBZ0JnRCxzQkFBc0IsRUFBRTtZQUN4Q2hELGVBQWVnRCxzQkFBc0IsQ0FBQyxDQUFDVjtvQkFDb0JBO2dCQUF2RCxNQUFNQyxpQkFBaUJELENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY0MsY0FBYyxNQUFJRCx5QkFBQUEsb0NBQUFBLDJCQUFBQSxhQUFjRSxVQUFVLGNBQXhCRiwrQ0FBQUEseUJBQTBCQyxjQUFjO2dCQUUvRixpREFBaUQ7Z0JBQ2pEaEMsU0FBU2tDLENBQUFBLE9BQ0xBLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUE7NEJBUXVDLHNCQUNBLHVCQUNrQjsrQkFUOURBLEtBQUtDLEVBQUUsTUFBSzVDLDJCQUFBQSxxQ0FBQUEsZUFBZ0I2QyxjQUFjLElBQ3BDOzRCQUNFLEdBQUdGLElBQUk7NEJBQ1BNLE9BQU9WLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JVLEtBQUssS0FBSTs0QkFDaENILE9BQU9QLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JPLEtBQUssS0FBSUgsS0FBS0csS0FBSzs0QkFDMUNQLGdCQUFnQjtnQ0FDWixHQUFHLEtBQWNBLGNBQWM7Z0NBQy9CTyxPQUFPUCxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCTyxLQUFLLE9BQUksNEJBQWNQLGNBQWMsY0FBNUIsZ0VBQThCTyxLQUFLO2dDQUNuRUcsT0FBT1YsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQlUsS0FBSyxPQUFJLDZCQUFjVixjQUFjLGNBQTVCLGtFQUE4QlUsS0FBSztnQ0FDbkVGLGdCQUFnQlIsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQlEsY0FBYyxPQUFJLDZCQUFjUixjQUFjLGNBQTVCLGtFQUE4QlEsY0FBYztnQ0FDOUZHLGdCQUFnQixLQUFLLHNDQUFzQzs0QkFDL0Q7d0JBQ0osSUFDRVA7O1lBR2xCO1FBQ0o7SUFDSixHQUFHO1FBQUMzQztRQUFnQk0sTUFBTTZDLE1BQU07S0FBQztJQUVqQyxxRUFBcUU7SUFDckUxRixzREFBZSxDQUFDO1FBQ1osTUFBTTJGLGlCQUFnQztZQUNsQ3BDLFFBQVEsQ0FBQztZQUNUQyxTQUFTLENBQUM7WUFDVkMsU0FBUyxDQUFDO1lBQ1ZDLGVBQWUsQ0FBQztRQUNwQjtRQUNBSixpQkFBaUJxQztJQUNyQixHQUFHLEVBQUU7SUFFTCx1QkFBdUI7SUFDdkIsTUFBTUMsY0FBYztZQUFPQywyRUFBeUJ4QyxlQUFleUMsd0VBQWU7WUFLekVwRDtRQUpMcUQsUUFBUUMsR0FBRyxDQUFFO1FBQ2JELFFBQVFDLEdBQUcsQ0FBRSxnREFBcUNDLEtBQUtDLFNBQVMsQ0FBQ0wsU0FBUyxNQUFNO1FBQ2hGRSxRQUFRQyxHQUFHLENBQUUsOERBQW1EQyxLQUFLQyxTQUFTLENBQUM3QyxlQUFlLE1BQU07UUFFcEcsSUFBSSxFQUFDWCxzQkFBQUEsaUNBQUFBLHVCQUFBQSxVQUFXQSxTQUFTLGNBQXBCQSwyQ0FBQUEscUJBQXNCeUMsRUFBRSxLQUFJLEVBQUN4QyxrQkFBQUEsNEJBQUFBLE1BQU9BLEtBQUssR0FBRTtZQUM1Q0MsTUFBTXVELEtBQUssQ0FBQztZQUNaO1FBQ0o7UUFFQSxzQ0FBc0M7UUFDdEMsSUFBSSxLQUFnQyxFQUFFLEVBR3JDO1FBRURuRCxlQUFlO1FBQ2YsSUFBSTtnQkF3RGlCdUQscUJBQUFBLGdCQW1CREEsc0JBQUFBLGlCQUNGQSxzQkFBQUEsaUJBQ0dBLHNCQUFBQSxpQkFJVUEsc0JBQUFBLGlCQUNDQSxzQkFBQUEsaUJBVUFBLDhCQUFBQSxzQkFBQUEsaUJBUVhBLHNCQUFBQTtZQW5HakIsdUVBQXVFO1lBQ3ZFLE1BQU1DLGVBQWU7Z0JBQUUsR0FBR1gsT0FBTztZQUFDO1lBQ2xDLHNGQUFzRjtZQUV0Rix5REFBeUQ7WUFDekQsb0VBQW9FO1lBQ3BFLHVHQUF1RztZQUN2RyxvRUFBb0U7WUFDcEUsZ0VBQWdFO1lBRWhFLDhDQUE4QztZQUU5Q0UsUUFBUUMsR0FBRyxDQUFFO1lBQ2JELFFBQVFDLEdBQUcsQ0FBQyxtREFBMEUsT0FBakNDLEtBQUtDLFNBQVMsQ0FBQ0wsU0FBUyxNQUFNO1lBQ25GRSxRQUFRQyxHQUFHLENBQUMsOERBQW1FLE9BQWZyQztZQUNoRW9DLFFBQVFDLEdBQUcsQ0FBRTtZQUNiLGlHQUFpRztZQUNqRyxxSEFBcUg7WUFDckhELFFBQVFDLEdBQUcsQ0FBRTtZQUViLE1BQU1TLGdCQUFvQztnQkFDdENaLFNBQVNXO2dCQUNUN0MsZ0JBQWdCQTtnQkFDaEIrQyxZQUFZO29CQUNSWixNQUFNQTtvQkFDTmEsT0FBTztnQkFDWDtZQUNKO1lBRUFaLFFBQVFDLEdBQUcsQ0FBRSxtREFBd0Msc0JBQXNCO1lBQzNFRCxRQUFRQyxHQUFHLENBQUMsK0NBQTRELE9BQXZCdEQsVUFBVUEsU0FBUyxDQUFDeUMsRUFBRTtZQUN2RVksUUFBUUMsR0FBRyxDQUFDLCtDQUFtRCxPQUFkLENBQUMsQ0FBQ3JELE1BQU1BLEtBQUs7WUFDOURvRCxRQUFRQyxHQUFHLENBQUMsMENBQWdFLE9BQWhDSSw4QkFBK0I7WUFDM0VMLFFBQVFDLEdBQUcsQ0FBRSw2RUFBd0RDLEtBQUtDLFNBQVMsQ0FBQ08sZUFBZSxNQUFNO1lBQ3pHVixRQUFRQyxHQUFHLENBQUU7WUFFYixNQUFNTyxXQUFXLE1BQU1wRixpRUFBa0JBLENBQUN3QixNQUFNQSxLQUFLLEVBQUVELFVBQVVBLFNBQVMsQ0FBQ3lDLEVBQUUsRUFBRXNCO1lBRS9FVixRQUFRQyxHQUFHLENBQUUsd0RBQTZDTztZQUUxRCxJQUFJQSxTQUFTSixLQUFLLEVBQUU7Z0JBQ2hCLHVDQUF1QztnQkFDdkMsTUFBTVMsZUFBZUwsU0FBU0osS0FBSyxDQUFDVSxXQUFXO2dCQUMvQyxJQUFJRCxhQUFhRSxRQUFRLENBQUMsbUJBQW1CRixhQUFhRSxRQUFRLENBQUMsbUJBQW1CO29CQUNsRmxFLE1BQU11RCxLQUFLLENBQUM7Z0JBQ2hCLE9BQU8sSUFBSVMsYUFBYUUsUUFBUSxDQUFDLGNBQWNGLGFBQWFFLFFBQVEsQ0FBQyxlQUFlO29CQUNoRmxFLE1BQU11RCxLQUFLLENBQUM7Z0JBQ2hCLE9BQU8sSUFBSVMsYUFBYUUsUUFBUSxDQUFDLFVBQVVGLGFBQWFFLFFBQVEsQ0FBQyxjQUFjO29CQUMzRWxFLE1BQU11RCxLQUFLLENBQUM7Z0JBQ2hCLE9BQU87b0JBQ0h2RCxNQUFNdUQsS0FBSyxDQUFDO2dCQUNoQjtnQkFDQTtZQUNKO1lBRUEsTUFBTVksV0FBV1IsRUFBQUEsaUJBQUFBLFNBQVNTLElBQUksY0FBYlQsc0NBQUFBLHNCQUFBQSxlQUFlUyxJQUFJLGNBQW5CVCwwQ0FBQUEsb0JBQXFCMUQsS0FBSyxLQUFJLEVBQUU7WUFFakRrRCxRQUFRQyxHQUFHLENBQUUsd0RBQTZDO2dCQUN0RGlCLFlBQVlGLFNBQVNyQixNQUFNO2dCQUMzQndCLFdBQVdILFFBQVEsQ0FBQyxFQUFFLElBQUk7Z0JBQzFCSSxVQUFVSjtZQUNkO1lBRUEsb0RBQW9EO1lBQ3BELE1BQU1LLGlCQUF5QjdFLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0I4RSxtQkFBbUIsQ0FBQ04sY0FBYSxFQUFFO1lBRWxGaEIsUUFBUUMsR0FBRyxDQUFFLHlEQUE4QztnQkFDdkRzQixnQkFBZ0JGLGVBQWUxQixNQUFNO2dCQUNyQzZCLGdCQUFnQkgsY0FBYyxDQUFDLEVBQUUsSUFBSTtZQUN6QztZQUVBdEUsU0FBU3NFO1lBQ1Qsb0VBQW9FO1lBQ3BFaEUsaUJBQWlCO2dCQUNiYSxVQUFVLEdBQUVzQyxrQkFBQUEsU0FBU1MsSUFBSSxjQUFiVCx1Q0FBQUEsdUJBQUFBLGdCQUFlUyxJQUFJLGNBQW5CVCwyQ0FBQUEscUJBQXFCdEMsVUFBVTtnQkFDM0N1RCxVQUFVakIsRUFBQUEsa0JBQUFBLFNBQVNTLElBQUksY0FBYlQsdUNBQUFBLHVCQUFBQSxnQkFBZVMsSUFBSSxjQUFuQlQsMkNBQUFBLHFCQUFxQmlCLFFBQVEsS0FBSSxDQUFDO2dCQUM1Q0MsV0FBVyxHQUFFbEIsa0JBQUFBLFNBQVNTLElBQUksY0FBYlQsdUNBQUFBLHVCQUFBQSxnQkFBZVMsSUFBSSxjQUFuQlQsMkNBQUFBLHFCQUFxQmtCLFdBQVc7WUFDakQ7WUFFQSwwQkFBMEI7WUFDMUIsTUFBTUMscUJBQXFCbkIsRUFBQUEsa0JBQUFBLFNBQVNTLElBQUksY0FBYlQsdUNBQUFBLHVCQUFBQSxnQkFBZVMsSUFBSSxjQUFuQlQsMkNBQUFBLHFCQUFxQnRDLFVBQVUsS0FBSTtZQUM5RCxNQUFNMEQsc0JBQXNCcEIsRUFBQUEsa0JBQUFBLFNBQVNTLElBQUksY0FBYlQsdUNBQUFBLHVCQUFBQSxnQkFBZVMsSUFBSSxjQUFuQlQsMkNBQUFBLHFCQUFxQmtCLFdBQVcsS0FBSTtZQUVoRSxnREFBZ0Q7WUFDaEQsK0RBQStEO1lBQy9ELElBQUlHLGlCQUFpQjtZQUVyQixpRUFBaUU7WUFDakUsMEZBQTBGO1lBQzFGLDZFQUE2RTtZQUM3RSxrREFBa0Q7WUFDbEQsTUFBTUMsc0JBQXNCdEIsRUFBQUEsa0JBQUFBLFNBQVNTLElBQUksY0FBYlQsdUNBQUFBLHVCQUFBQSxnQkFBZVMsSUFBSSxjQUFuQlQsNENBQUFBLCtCQUFBQSxxQkFBcUJpQixRQUFRLGNBQTdCakIsbURBQUFBLDZCQUErQnNCLG1CQUFtQixLQUFJO1lBQ2xGRCxpQkFBaUJDLHNCQUFzQjtZQUV2QzNELGNBQWN3RDtZQUNkMUQsY0FBYzREO1lBQ2Q5RCxlQUFlZ0MsTUFBTSx1Q0FBdUM7O1lBRTVELHVEQUF1RDtZQUN2RCxNQUFNZ0MsV0FBV3ZCLEVBQUFBLGtCQUFBQSxTQUFTUyxJQUFJLGNBQWJULHVDQUFBQSx1QkFBQUEsZ0JBQWVTLElBQUksY0FBbkJULDJDQUFBQSxxQkFBcUJ1QixRQUFRLEtBQUk7WUFDbER4RCxtQkFBbUJ3RDtZQUVuQi9CLFFBQVFDLEdBQUcsQ0FBRSxrRUFBNkM7Z0JBQ3REbkMsYUFBYWlDO2dCQUNiL0IsWUFBWTZEO2dCQUNaM0QsWUFBWXlEO2dCQUNaSyxjQUFjO1lBQ2xCO1lBRUE3RSxlQUFlO1lBQ2ZOLE1BQU1vRixPQUFPLENBQUMsU0FBK0IsT0FBdEJaLGVBQWUxQixNQUFNLEVBQUM7WUFFN0NLLFFBQVFDLEdBQUcsQ0FBRTtRQUVqQixFQUFFLE9BQU9HLE9BQU87WUFDWkosUUFBUUksS0FBSyxDQUFDLGlCQUFpQkE7WUFDL0IseUVBQXlFO1lBQ3pFdkQsTUFBTXVELEtBQUssQ0FBQztRQUNoQixTQUFVO1lBQ05uRCxlQUFlO1lBQ2YrQyxRQUFRQyxHQUFHLENBQUU7UUFDakI7SUFDSjtJQUVBLDJDQUEyQztJQUMzQyxNQUFNaUMscUJBQXFCLENBQUNwQztRQUN4QkUsUUFBUUMsR0FBRyxDQUFFLGlFQUFzREMsS0FBS0MsU0FBUyxDQUFDTCxTQUFTLE1BQU07UUFDakdFLFFBQVFDLEdBQUcsQ0FBRSx5REFBOENDLEtBQUtDLFNBQVMsQ0FBQzdDLGVBQWUsTUFBTTtRQUUvRixnRkFBZ0Y7UUFDaEYsbUVBQW1FO1FBQ25FUyxlQUFlO1FBQ2ZFLGNBQWM7UUFDZEUsY0FBYztRQUNkSSxtQkFBbUI7UUFDbkJwQixlQUFlO1FBQ2ZKLFNBQVMsRUFBRTtRQUNYTSxpQkFBaUI7UUFDakIyQyxRQUFRQyxHQUFHLENBQUU7UUFFYixpRkFBaUY7UUFDakYsdUVBQXVFO1FBQ3ZFMUMsaUJBQWlCdUMsU0FBUyx3Q0FBd0M7O1FBRWxFRSxRQUFRQyxHQUFHLENBQUUsaUVBQXNEQyxLQUFLQyxTQUFTLENBQUNMLFNBQVMsTUFBTTtJQUNqRywrREFBK0Q7SUFDL0QsdUNBQXVDO0lBQzNDO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1xQyxtQkFBbUIsQ0FBQ3BDO1FBQ3RCQyxRQUFRQyxHQUFHLENBQUMsd0RBQW1ELE9BQUxGO1FBQzFELElBQUlBLE9BQU8sR0FBRztZQUNWQyxRQUFRQyxHQUFHLENBQUMsaURBQTRDLE9BQUxGLE1BQUs7WUFDeEQ7UUFDSjtRQUVBLDRFQUE0RTtRQUM1RSxxRUFBcUU7UUFDckUsSUFBSUEsT0FBTy9CLFlBQVk7WUFDbkJnQyxRQUFRQyxHQUFHLENBQUMsbURBQXVFakMsT0FBeEMrQixNQUFLLHFDQUE4QyxPQUFYL0IsWUFBVztZQUM5RmdDLFFBQVFDLEdBQUcsQ0FBRTtRQUNqQjtRQUVBLDRFQUE0RTtRQUM1RSxnRkFBZ0Y7UUFDaEZsQyxlQUFlZ0M7UUFDZkMsUUFBUUMsR0FBRyxDQUFDLCtDQUErRUYsT0FBMUNBLE1BQUssdUNBQTBDLE9BQUxBLE1BQUs7UUFFaEcsdURBQXVEO1FBQ3ZERixZQUFZdkMsZUFBZXlDO0lBQy9CO0lBRUEsTUFBTXFDLHNCQUFzQixTQUFDQztZQUFvQkwsZ0ZBQXVCO1FBQ3BFLE9BQU9NLEtBQUtDLElBQUksQ0FBQ0YsYUFBYUw7SUFDbEM7SUFFQSxNQUFNUSxzQkFBc0I7UUFDeEIsTUFBTUMsUUFBUSxFQUFFO1FBQ2hCLE1BQU1DLGtCQUFrQixFQUFFLGtDQUFrQzs7UUFFNUQsSUFBSTFFLGNBQWMwRSxpQkFBaUI7WUFDL0IsbUNBQW1DO1lBQ25DLElBQUssSUFBSUMsSUFBSSxHQUFHQSxLQUFLM0UsWUFBWTJFLElBQUs7Z0JBQ2xDRixNQUFNRyxJQUFJLENBQUNEO1lBQ2Y7UUFDSixPQUFPO1lBQ0gsc0NBQXNDO1lBQ3RDLElBQUk3RSxlQUFlLEdBQUc7Z0JBQ2xCLDRDQUE0QztnQkFDNUMsSUFBSyxJQUFJNkUsSUFBSSxHQUFHQSxLQUFLLEdBQUdBLElBQUs7b0JBQ3pCRixNQUFNRyxJQUFJLENBQUNEO2dCQUNmO2dCQUNBRixNQUFNRyxJQUFJLENBQUM7Z0JBQ1hILE1BQU1HLElBQUksQ0FBQzVFO1lBQ2YsT0FBTyxJQUFJRixlQUFlRSxhQUFhLEdBQUc7Z0JBQ3RDLDhEQUE4RDtnQkFDOUR5RSxNQUFNRyxJQUFJLENBQUM7Z0JBQ1hILE1BQU1HLElBQUksQ0FBQztnQkFDWCxJQUFLLElBQUlELElBQUkzRSxhQUFhLEdBQUcyRSxLQUFLM0UsWUFBWTJFLElBQUs7b0JBQy9DRixNQUFNRyxJQUFJLENBQUNEO2dCQUNmO1lBQ0osT0FBTztnQkFDSCxnRUFBZ0U7Z0JBQ2hFRixNQUFNRyxJQUFJLENBQUM7Z0JBQ1hILE1BQU1HLElBQUksQ0FBQztnQkFDWCxJQUFLLElBQUlELElBQUk3RSxjQUFjLEdBQUc2RSxLQUFLN0UsY0FBYyxHQUFHNkUsSUFBSztvQkFDckRGLE1BQU1HLElBQUksQ0FBQ0Q7Z0JBQ2Y7Z0JBQ0FGLE1BQU1HLElBQUksQ0FBQztnQkFDWEgsTUFBTUcsSUFBSSxDQUFDNUU7WUFDZjtRQUNKO1FBRUEsT0FBT3lFO0lBQ1g7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTUksZUFBZTtRQUNqQjdDLFFBQVFDLEdBQUcsQ0FBRTtRQUNiRCxRQUFRQyxHQUFHLENBQUUsd0RBQTZDQyxLQUFLQyxTQUFTLENBQUM3QyxlQUFlLE1BQU07UUFDOUYwQyxRQUFRQyxHQUFHLENBQUMsK0NBQWlELE9BQVpuQztRQUNqRGtDLFFBQVFDLEdBQUcsQ0FBRSwwREFBK0Msc0JBQXNCO1FBRWxGLDZEQUE2RDtRQUM3RCw2RUFBNkU7UUFFN0Usd0RBQXdEO1FBQ3hELG1FQUFtRTtRQUNuRSxNQUFNNkMsZ0JBQWdCO1lBQ2xCLEdBQUd4RixjQUFjLGlEQUFpRDtRQUN0RTtRQUVBMEMsUUFBUUMsR0FBRyxDQUFFLGlHQUFzRkMsS0FBS0MsU0FBUyxDQUFDMkMsZUFBZSxNQUFNO1FBQ3ZJOUMsUUFBUUMsR0FBRyxDQUFDLHFEQUF1RCxPQUFabkM7UUFDdkQrQixZQUFZaUQsZUFBZWhGLGFBQWEsc0NBQXNDOztJQUNsRjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNaUYsbUJBQW1CO1FBQ3JCL0MsUUFBUUMsR0FBRyxDQUFDO0lBQ2hCO0lBRUEsbURBQW1EO0lBRS9DLHFEQUFxRDtJQUV6RCxnREFBZ0Q7SUFDaEQsTUFBTStDLGtCQUFrQjtZQUdBckcsc0JBU2ZBO1FBWExxRCxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLDRFQUF3RDtZQUNoRWdELGNBQWMsQ0FBQyxFQUFDdEcsc0JBQUFBLGlDQUFBQSx1QkFBQUEsVUFBV0EsU0FBUyxjQUFwQkEsMkNBQUFBLHFCQUFzQnlDLEVBQUU7WUFDeEM4RCxVQUFVLENBQUMsRUFBQ3RHLGtCQUFBQSw0QkFBQUEsTUFBT0EsS0FBSztZQUN4Qk07WUFDQWdFLFlBQVlwRSxNQUFNNkMsTUFBTTtZQUN4QnJCO1lBQ0FSO1lBQ0FSLGVBQWVBO1FBQ25CO1FBRUEsSUFBSSxFQUFDWCxzQkFBQUEsaUNBQUFBLHdCQUFBQSxVQUFXQSxTQUFTLGNBQXBCQSw0Q0FBQUEsc0JBQXNCeUMsRUFBRSxLQUFJLEVBQUN4QyxrQkFBQUEsNEJBQUFBLE1BQU9BLEtBQUssR0FBRTtZQUM1Q29ELFFBQVFDLEdBQUcsQ0FBQztZQUNacEQsTUFBTXVELEtBQUssQ0FBQztZQUNaO1FBQ0o7UUFFQSxJQUFJLENBQUNsRCxlQUFlSixNQUFNNkMsTUFBTSxLQUFLLEdBQUc7WUFDcENLLFFBQVFDLEdBQUcsQ0FBQztZQUNacEQsTUFBTXVELEtBQUssQ0FBQztZQUNaO1FBQ0o7UUFFQUosUUFBUUMsR0FBRyxDQUFDO1FBRVosbUNBQW1DO1FBQ25DLE1BQU1rRCxjQUFjLG9CQUFvRCxPQUFoQyxJQUFJQyxPQUFPQyxrQkFBa0I7UUFDckVyRCxRQUFRQyxHQUFHLENBQUMsbUZBQStEa0Q7UUFDM0V4RSxjQUFjd0U7UUFDZDFFLGtCQUFrQjtJQUN0QjtJQUVBLG1DQUFtQztJQUNuQyxNQUFNNkUsb0JBQW9CO1lBUUYzRztRQVBwQnFELFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsNkVBQXlEO1lBQ2pFdkIsVUFBVSxFQUFFQSx1QkFBQUEsaUNBQUFBLFdBQVk2RSxJQUFJO1lBQzVCckMsWUFBWXBFLE1BQU02QyxNQUFNO1lBQ3hCN0I7WUFDQVE7WUFDQTRFLFVBQVUsQ0FBQyxFQUFDdEcsa0JBQUFBLDRCQUFBQSxNQUFPQSxLQUFLO1lBQ3hCcUcsY0FBYyxDQUFDLEVBQUN0RyxzQkFBQUEsaUNBQUFBLHVCQUFBQSxVQUFXQSxTQUFTLGNBQXBCQSwyQ0FBQUEscUJBQXNCeUMsRUFBRTtRQUM1QztRQUVBLElBQUksQ0FBQ1YsY0FBY0EsV0FBVzZFLElBQUksT0FBTyxJQUFJO1lBQ3pDdkQsUUFBUUMsR0FBRyxDQUFDO1lBQ1pwRCxNQUFNdUQsS0FBSyxDQUFDO1lBQ1o7UUFDSjtRQUVBSixRQUFRQyxHQUFHLENBQUM7UUFDWjVCLGtCQUFrQjtRQUNsQkksa0JBQWtCO1FBRWxCLElBQUk7Z0JBb0JrRStCO1lBbkJsRSxNQUFNZ0QsY0FBYztnQkFDaEJDLE1BQU0vRSxXQUFXNkUsSUFBSTtnQkFDckJHLGFBQWEsNkJBQTZENUYsT0FBaENoQixNQUFNNkMsTUFBTSxFQUFDLHFCQUErQixPQUFaN0I7Z0JBQzFFZ0MsU0FBU3hDO2dCQUNUeUUsVUFBVXpELG1CQUFtQnFGO2dCQUM3QkMsWUFBWTtZQUNoQjtZQUVBNUQsUUFBUUMsR0FBRyxDQUFDLHFGQUFpRXVEO1lBQzdFeEQsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTU8sV0FBVyxNQUFNbkYseURBQVVBLENBQUN1QixNQUFNQSxLQUFLLEVBQUVELFVBQVVBLFNBQVMsQ0FBQ3lDLEVBQUUsRUFBRW9FO1lBRXZFeEQsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyw4RUFBMERPO1lBQ3RFUixRQUFRQyxHQUFHLENBQUMsOEVBQTBENEQsT0FBT0MsSUFBSSxDQUFDdEQ7WUFDbEZSLFFBQVFDLEdBQUcsQ0FBQywwRUFBc0RPLFNBQVN1RCxTQUFTO1lBQ3BGL0QsUUFBUUMsR0FBRyxDQUFDLHNFQUFrRE8sU0FBU0osS0FBSztZQUM1RUosUUFBUUMsR0FBRyxDQUFDLHFFQUFpRE8sU0FBU1MsSUFBSTtZQUMxRWpCLFFBQVFDLEdBQUcsQ0FBQywyRUFBc0RPLGlCQUFBQSxTQUFTUyxJQUFJLGNBQWJULHFDQUFBQSxlQUFlUyxJQUFJO1lBRXJGLElBQUlULFNBQVN1RCxTQUFTLEVBQUU7b0JBS052RCxpQ0FBQUEscUJBQUFBO2dCQUpkUixRQUFRQyxHQUFHLENBQUM7Z0JBQ1pELFFBQVFDLEdBQUcsQ0FBQyxxRkFBaUU7b0JBQ3pFd0QsTUFBTS9FO29CQUNOd0MsWUFBWXBFLE1BQU02QyxNQUFNO29CQUN4Qm9DLFFBQVEsR0FBRXZCLGtCQUFBQSxTQUFTUyxJQUFJLGNBQWJULHVDQUFBQSxzQkFBQUEsZ0JBQWVTLElBQUksY0FBbkJULDJDQUFBQSxrQ0FBQUEsb0JBQXFCd0QsV0FBVyxjQUFoQ3hELHNEQUFBQSxnQ0FBa0NwQixFQUFFO29CQUM5Q3dFLFlBQVk7Z0JBQ2hCO2dCQUNBL0csTUFBTW9GLE9BQU8sQ0FBQyxVQUE4Qm5GLE9BQXBCNEIsWUFBVyxXQUFzQixPQUFiNUIsTUFBTTZDLE1BQU0sRUFBQztZQUM3RCxPQUFPO2dCQUNISyxRQUFRSSxLQUFLLENBQUM7Z0JBQ2RKLFFBQVFJLEtBQUssQ0FBQyxtRUFBeUQ7b0JBQ25FMkQsV0FBV3ZELFNBQVN1RCxTQUFTO29CQUM3QjNELE9BQU9JLFNBQVNKLEtBQUs7b0JBQ3JCNkQsUUFBUXpELFNBQVN5RCxNQUFNO2dCQUMzQjtnQkFDQXBILE1BQU11RCxLQUFLLENBQUNJLFNBQVNKLEtBQUssSUFBSTtZQUNsQztRQUNKLEVBQUUsT0FBT0EsT0FBTztZQUNaSixRQUFRSSxLQUFLLENBQUMscUZBQTJFQTtZQUN6RkosUUFBUUksS0FBSyxDQUFDLGdFQUFzRCxPQUFPQTtZQUMzRUosUUFBUUksS0FBSyxDQUFDLG1FQUF5REEsaUJBQWlCOEQsUUFBUTlELE1BQU0rRCxPQUFPLEdBQUc7WUFDaEh0SCxNQUFNdUQsS0FBSyxDQUFDO1FBQ2hCLFNBQVU7WUFDTkosUUFBUUMsR0FBRyxDQUFDO1lBQ1o1QixrQkFBa0I7UUFDdEI7SUFDSjtJQUVBLGtEQUFrRDtJQUVsRCw0Q0FBNEM7SUFDNUMsaURBQWlEO0lBRWpELE1BQU0rRixrQkFBa0IsQ0FBQ2pGO1FBQ3JCLGdEQUFnRDtRQUNoRCxNQUFNa0YsU0FBUzNILE9BQU8ySCxNQUFNO1FBQzVCNUgsT0FBT21HLElBQUksQ0FBQyxJQUFnRHpELE9BQTVDa0YsUUFBTyx1Q0FBNkMsT0FBUmxGLEtBQUtDLEVBQUU7SUFDdkU7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTWtGLGdCQUFnQjlILENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0IrSCxnQkFBZ0IsQ0FBQ3pILE9BQU82RyxXQUFXQSxlQUFjN0c7SUFFdkYscUJBQ0k7OzBCQUNJLDhEQUFDMEg7Z0JBQUlDLFdBQVU7O2tDQUNYLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWCw0RUFBQ0M7NEJBQUdELFdBQVU7c0NBQW1DOzs7Ozs7Ozs7OztrQ0FFckQsOERBQUNEO3dCQUFJQyxXQUFVOzs0QkFDVmpJLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JtSSxhQUFhLENBQUNoRixNQUFNLElBQUcsbUJBQ3BDLDhEQUFDeEYsNERBQU1BO2dDQUFDeUssU0FBUTtnQ0FBUUgsV0FBVTtnQ0FBeUlJLFNBQVM7b0NBQVE5SCxTQUFTRCxNQUFNZ0ksTUFBTSxDQUFDM0YsQ0FBQUEsT0FBUSxFQUFDM0MsMkJBQUFBLHFDQUFBQSxlQUFnQm1JLGFBQWEsQ0FBQzVELFFBQVEsQ0FBQzVCLEtBQUtDLEVBQUU7b0NBQUs1QywyQkFBQUEscUNBQUFBLGVBQWdCdUksZ0JBQWdCLENBQUMsRUFBRTtnQ0FBRTs7a0RBQy9TLDhEQUFDakssOEVBQVNBO3dDQUFDMkosV0FBVTs7Ozs7O29DQUFVO29DQUFRakksMkJBQUFBLHFDQUFBQSxlQUFnQm1JLGFBQWEsQ0FBQ2hGLE1BQU07Ozs7Ozs7MENBSW5GLDhEQUFDNkU7Z0NBQUlDLFdBQVU7O29DQUVWdkgsNkJBQ0csOERBQUNzSDt3Q0FBSUMsV0FBVTtrREFJVnpHLGFBQWEsa0JBQ1YsOERBQUN3Rzs0Q0FBSUMsV0FBVTs7OERBRVgsOERBQUN0Syw0REFBTUE7b0RBQ0h5SyxTQUFRO29EQUNSSSxNQUFLO29EQUNMSCxTQUFTLElBQU0xQyxpQkFBaUJHLEtBQUsyQyxHQUFHLENBQUMsR0FBR25ILGNBQWM7b0RBQzFEb0gsVUFBVXBILGdCQUFnQjtvREFDMUIyRyxXQUFVOzhEQUNiOzs7Ozs7OERBS0QsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNWakMsc0JBQXNCdEQsR0FBRyxDQUFDLENBQUNhLE1BQU1vRixzQkFDOUIsOERBQUNsTCx1REFBYztzRUFDVjhGLFNBQVMsc0JBQ04sOERBQUNzRjtnRUFBS1osV0FBVTswRUFBZ0M7Ozs7OzBGQUVoRCw4REFBQ3RLLDREQUFNQTtnRUFDSHlLLFNBQVM5RyxnQkFBZ0JpQyxPQUFPLFlBQVk7Z0VBQzVDaUYsTUFBSztnRUFDTEgsU0FBUyxJQUFNMUMsaUJBQWlCcEM7Z0VBQ2hDMEUsV0FBVyx1QkFJVixPQUhHM0csZ0JBQWdCaUMsT0FDViwwQkFDQTswRUFHVEE7Ozs7OzsyREFkUW9GOzs7Ozs7Ozs7OzhEQXNCN0IsOERBQUNoTCw0REFBTUE7b0RBQ0h5SyxTQUFRO29EQUNSSSxNQUFLO29EQUNMSCxTQUFTLElBQU0xQyxpQkFBaUJHLEtBQUtnRCxHQUFHLENBQUN0SCxZQUFZRixjQUFjO29EQUNuRW9ILFVBQVVwSCxnQkFBZ0JFO29EQUMxQnlHLFdBQVU7OERBQ2I7Ozs7Ozs7Ozs7O3NFQUtMLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFBMkI7Ozs7Ozs7Ozs7O2tEQU90RCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNYLDhEQUFDdkosb0hBQXlCQTtnREFBQ3VKLFdBQVU7Ozs7OzswREFDckMsOERBQUNySywwREFBS0E7Z0RBQ0ZtTCxhQUFZO2dEQUNaLDZDQUE2QztnREFDN0MsMEVBQTBFO2dEQUMxRUMsV0FBVyxDQUFDQyxJQUFNQSxFQUFFQyxHQUFHLEtBQUssV0FBVzdDO2dEQUN2QzRCLFdBQVU7Ozs7Ozs7Ozs7OztrREFHbEIsOERBQUN0Syw0REFBTUE7d0NBQ0g2SyxNQUFLO3dDQUNMSCxTQUFTaEM7d0NBQ1RxQyxVQUFVbEk7d0NBQ1Z5SCxXQUFVO2tEQUVUekgsY0FBYyxpQkFBaUI7Ozs7OztvQ0FLbkNFLGVBQWVKLE1BQU02QyxNQUFNLEdBQUcsbUJBQzNCLDhEQUFDeEYsNERBQU1BO3dDQUNIMEssU0FBUzdCO3dDQUNUa0MsVUFBVTlHO3dDQUNWNEcsTUFBSzt3Q0FDTEosU0FBUTt3Q0FDUkgsV0FBVTs7MERBRVYsOERBQUN6SixpRkFBWUE7Z0RBQUN5SixXQUFVOzs7Ozs7NENBQ3ZCckcsaUJBQWlCLGNBQWMsUUFBcUIsT0FBYnRCLE1BQU02QyxNQUFNLEVBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXpFLDhEQUFDNkU7Z0JBQUlDLFdBQVU7O2tDQUVYLDhEQUFDdEosd0dBQWFBO3dCQUNWd0ssY0FBYzt3QkFDZEMsZ0JBQWdCMUQ7d0JBQ2hCdEUsZ0JBQWdCQTt3QkFDaEJpSSx3QkFBd0IsQ0FBQ0M7NEJBQ3JCOUYsUUFBUUMsR0FBRyxDQUFFOzRCQUNiRCxRQUFRQyxHQUFHLENBQUMsbURBQXdELE9BQWZyQzs0QkFDckRvQyxRQUFRQyxHQUFHLENBQUMsOENBQTBDLE9BQU42Rjs0QkFDaEQ5RixRQUFRQyxHQUFHLENBQUU7NEJBQ2JELFFBQVFDLEdBQUcsQ0FBRTs0QkFFYnBDLGtCQUFrQmlJLE9BQU8sd0NBQXdDOzs0QkFDakU5RixRQUFRQyxHQUFHLENBQUMsa0VBQThELE9BQU42Rjt3QkFDeEU7Ozs7OztrQ0FJSiw4REFBQ3RCO3dCQUFJQyxXQUFVO2tDQUNWSCxjQUFjM0UsTUFBTSxLQUFLLElBQ3RCLHNDQUFzQyxpQkFDdEMsOERBQUM2RTs0QkFBSUMsV0FBVTtzQ0FDWCw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNYLDhEQUFDeEosd0ZBQW1CQTt3Q0FBQ3dKLFdBQVU7Ozs7OztrREFDL0IsOERBQUNzQjt3Q0FBR3RCLFdBQVU7a0RBQ1R2SCxjQUFjLHVCQUF1Qjs7Ozs7O2tEQUUxQyw4REFBQzhJO3dDQUFFdkIsV0FBVTtrREFDUnZILGNBQ0ssb0VBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTWxCO3NDQUVJLDRFQUFDeEMscUVBQVVBO2dDQUFDK0osV0FBVTs7a0RBQ0UsOERBQUNuSywwREFBS0E7OzBEQUNsQyw4REFBQzBCLHVEQUFrQkE7Z0RBQ2YySSxlQUFlbkksQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQm1JLGFBQWEsS0FBSSxFQUFFO2dEQUNsREwsZUFBZUE7Z0RBQ2YyQixpQkFBaUJ6SixDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCeUosZUFBZSxLQUFLLE1BQU87Ozs7OzswREFFaEUsOERBQUMxTCw4REFBU0E7MERBQ0wrSixjQUFjcEYsR0FBRyxDQUFDLENBQUNDO3dEQWdCUEEsc0JBQThCQSx1QkFTOUJBLHVCQUE4QkE7eUVBeEJ2Qyw4REFBQzFFLDZEQUFRQTt3REFBZWdLLFdBQVU7OzBFQUM5Qiw4REFBQ2pLLDhEQUFTQTtnRUFBQ2lLLFdBQVU7MEVBQ2pCLDRFQUFDcEssZ0VBQVFBO29FQUFDNkwsT0FBTyxFQUFFMUosMkJBQUFBLHFDQUFBQSxlQUFnQm1JLGFBQWEsQ0FBQzVELFFBQVEsQ0FBQzVCLEtBQUtDLEVBQUU7b0VBQUcrRyxpQkFBaUIsQ0FBQ0QsVUFBcUIxSiwyQkFBQUEscUNBQUFBLGVBQWdCNEosZ0JBQWdCLENBQUNqSCxLQUFLQyxFQUFFLEVBQUU4RztvRUFBVXpCLFdBQVcsR0FBK0ksT0FBNUlqSSxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCbUksYUFBYSxDQUFDNUQsUUFBUSxDQUFDNUIsS0FBS0MsRUFBRSxLQUFJLHdCQUF3Qjs7Ozs7Ozs7Ozs7MEVBRTNQLDhEQUFDNUUsOERBQVNBO2dFQUFDaUssV0FBVTswRUFDakIsNEVBQUM0QjtvRUFDRzVCLFdBQVU7b0VBQ1ZJLFNBQVMsSUFBTVQsZ0JBQWdCakY7OEVBRTlCQSxLQUFLc0UsSUFBSTs7Ozs7Ozs7Ozs7MEVBR2xCLDhEQUFDakosOERBQVNBO2dFQUFDaUssV0FBVTswRUFBc0N0RixLQUFLbUgsUUFBUTs7Ozs7OzBFQUN4RSw4REFBQzlMLDhEQUFTQTtnRUFBQ2lLLFdBQVU7MEVBQXNDdEYsS0FBS29ILFFBQVE7Ozs7OzswRUFDeEUsOERBQUMvTCw4REFBU0E7Z0VBQUNpSyxXQUFVOzBFQUNoQnRGLEVBQUFBLHVCQUFBQSxLQUFLSixjQUFjLGNBQW5CSSwyQ0FBQUEscUJBQXFCRyxLQUFLLE9BQUlILHdCQUFBQSxLQUFLSixjQUFjLGNBQW5CSSw0Q0FBQUEsc0JBQXFCSSxjQUFjLGtCQUM5RCw4REFBQ2lGO29FQUFJQyxXQUFVO29FQUF3RCtCLE9BQU9ySCxLQUFLSixjQUFjLENBQUNPLEtBQUs7OEVBQ2xHSCxLQUFLSixjQUFjLENBQUNPLEtBQUs7Ozs7OzhGQUc5Qiw4REFBQ2xEO29FQUFhcUssTUFBTTlMLGlGQUFZQTtvRUFBRWtLLFNBQVMsSUFBTXJJLDJCQUFBQSxxQ0FBQUEsZUFBZ0JrSyxpQkFBaUIsQ0FBQ3ZILEtBQUtDLEVBQUU7OEVBQUc7Ozs7Ozs7Ozs7OzBFQUdyRyw4REFBQzVFLDhEQUFTQTtnRUFBQ2lLLFdBQVU7MEVBQ2hCdEYsRUFBQUEsd0JBQUFBLEtBQUtKLGNBQWMsY0FBbkJJLDRDQUFBQSxzQkFBcUJNLEtBQUssT0FBSU4sd0JBQUFBLEtBQUtKLGNBQWMsY0FBbkJJLDRDQUFBQSxzQkFBcUJPLGNBQWMsa0JBQzlELDhEQUFDOEU7b0VBQUlDLFdBQVU7b0VBQXdEK0IsT0FBT3JILEtBQUtKLGNBQWMsQ0FBQ1UsS0FBSzs4RUFDbEdOLEtBQUtKLGNBQWMsQ0FBQ1UsS0FBSzs7Ozs7OEZBRzlCLDhEQUFDckQ7b0VBQWFxSyxNQUFNN0wsOEVBQVNBO29FQUFFaUssU0FBUyxJQUFNckksMkJBQUFBLHFDQUFBQSxlQUFnQm1LLGlCQUFpQixDQUFDeEgsS0FBS0MsRUFBRTs4RUFBRzs7Ozs7Ozs7Ozs7MEVBR2xHLDhEQUFDNUUsOERBQVNBO2dFQUFDaUssV0FBVTswRUFDakIsNEVBQUNwSTtvRUFDR3VLLHVCQUNJLDhEQUFDUDt3RUFBTzVCLFdBQVU7OzRFQUFpSTswRkFDckksOERBQUMxSixxRkFBZ0JBO2dGQUFDMEosV0FBVTs7Ozs7Ozs7Ozs7O29FQUc5Q29DLE9BQU9ySyxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCc0ssZUFBZSxDQUFDM0gsVUFBUyxFQUFFOzs7Ozs7Ozs7OzswRUFHMUQsOERBQUMzRSw4REFBU0E7Z0VBQUNpSyxXQUFVOzBFQUNqQiw0RUFBQ25JO29FQUNHc0ssdUJBQ0ksOERBQUN6TSw0REFBTUE7d0VBQ0h5SyxTQUFRO3dFQUNSSSxNQUFLO3dFQUNMUCxXQUFVO2tGQUVWLDRFQUFDNUoseUZBQW9CQTs0RUFBQzRKLFdBQVU7Ozs7Ozs7Ozs7O29FQUd4Q3NDLFVBQVU1SDtvRUFDVjZILGFBQWEsSUFBTXpLLHdCQUFBQSxrQ0FBQUEsWUFBYTBLLGVBQWUsQ0FBQzlILEtBQUtDLEVBQUUsRUFBRUQ7b0VBQ3pEK0gsaUJBQWlCLElBQU0zSyx3QkFBQUEsa0NBQUFBLFlBQWE0SyxtQkFBbUIsQ0FBQ2hJLEtBQUtDLEVBQUU7b0VBQy9EZ0ksaUJBQWlCLElBQU03Syx3QkFBQUEsa0NBQUFBLFlBQWE4SyxtQkFBbUIsQ0FBQ2xJLEtBQUtDLEVBQUUsRUFBRUQ7b0VBQ2pFbUksaUJBQWlCLElBQU0vSyx3QkFBQUEsa0NBQUFBLFlBQWFnTCxtQkFBbUIsQ0FBQ3BJLEtBQUtDLEVBQUU7Ozs7Ozs7Ozs7Ozt1REF6RDVERCxLQUFLQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztrREFpRWxDLDhEQUFDb0Y7d0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFZdkIsOERBQUM5SSw2REFBTUE7Z0JBQUM2TCxNQUFNaEo7Z0JBQWdCaUosY0FBY2hKOzBCQUN4Qyw0RUFBQzdDLG9FQUFhQTtvQkFBQzZJLFdBQVU7b0JBQWtEaUQsb0JBQWlCOztzQ0FDeEYsOERBQUM3TCxtRUFBWUE7c0NBQ1QsNEVBQUNDLGtFQUFXQTswQ0FBQzs7Ozs7Ozs7Ozs7c0NBRWpCLDhEQUFDMEk7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDdUI7b0NBQUU1RyxJQUFHO29DQUEwQnFGLFdBQVU7OENBQTZCOzs7Ozs7OENBQ3ZFLDhEQUFDckssMERBQUtBO29DQUNGMEwsT0FBT3BIO29DQUNQaUosVUFBVSxDQUFDbEMsSUFBTTlHLGNBQWM4RyxFQUFFbUMsTUFBTSxDQUFDOUIsS0FBSztvQ0FDN0NQLGFBQVk7b0NBQ1pDLFdBQVcsQ0FBQ0M7d0NBQ1IsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFNBQVM7NENBQ25CcEM7d0NBQ0o7b0NBQ0o7Ozs7Ozs7Ozs7OztzQ0FHUiw4REFBQ3ZILG1FQUFZQTs7OENBQ1QsOERBQUM1Qiw0REFBTUE7b0NBQUN5SyxTQUFRO29DQUFVSSxNQUFLO29DQUFLUCxXQUFVO29DQUFVSSxTQUFTLElBQU1wRyxrQkFBa0I7OENBQVE7Ozs7Ozs4Q0FHakcsOERBQUN0RSw0REFBTUE7b0NBQUN5SyxTQUFRO29DQUFVSSxNQUFLO29DQUFLUCxXQUFVO29DQUFVSSxTQUFTdkI7OENBQW1COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUTVHO0dBcnZCTXJIOztRQUNhUixpRUFBU0E7UUFDVEMsaUVBQVNBO1FBQ0ZKLGtFQUFZQTtRQUNoQkMsd0RBQU9BO1FBQ1BDLDBEQUFRQTs7O0tBTHhCUztBQXV2Qk4sK0RBQWVBLFNBQVNBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL3VpL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL2xlYWQtZ2VuZXJhdGlvbi9jb21wYW55L2ZpbmQtbGVhZHMudHN4P2RjMDEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VNZW1vIH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAdWkvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQHVpL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgQ2hlY2tib3ggfSBmcm9tIFwiQHVpL2NvbXBvbmVudHMvdWkvY2hlY2tib3hcIlxuaW1wb3J0IHsgVGFibGUsIFRhYmxlQm9keSwgVGFibGVDZWxsLCBUYWJsZUhlYWQsIFRhYmxlSGVhZGVyLCBUYWJsZVJvdyB9IGZyb20gXCJAdWkvY29tcG9uZW50cy91aS90YWJsZVwiXG5pbXBvcnQgeyBTY3JvbGxBcmVhIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL3Njcm9sbC1hcmVhXCJcbmltcG9ydCB7IFBsdXNJY29uLCBVc2VyR3JvdXBJY29uLCBFbnZlbG9wZUljb24sIFBob25lSWNvbiwgRWxsaXBzaXNWZXJ0aWNhbEljb24sIFRyYXNoSWNvbiwgQ2hldnJvblJpZ2h0SWNvbiwgQ2hhcnRMaW5lSWNvbiwgRGF0YWJhc2VJY29uLCBDb2RlTWVyZ2VJY29uLCBVcFJpZ2h0RnJvbVNxdWFyZUljb24sIE1hZ25pZnlpbmdHbGFzc0ljb24gfSBmcm9tIFwiQHVpL2NvbXBvbmVudHMvaWNvbnMvRm9udEF3ZXNvbWVSZWd1bGFyXCJcbmltcG9ydCB7IE1hZ25pZnlpbmdHbGFzc0NpcmNsZUljb24gfSBmcm9tIFwiQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lXCJcbmltcG9ydCB7IExvYWRlciB9IGZyb20gXCJAdWkvY29tcG9uZW50cy9jdXN0b20tdWkvbG9hZGVyXCJcbmltcG9ydCB7IENvbXBhbnlGaWx0ZXIgfSBmcm9tIFwiQHVpL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vbGVhZC1nZW5lcmF0aW9uL2ZpbHRlcnMvY29tcGFueVwiXG5pbXBvcnQgeyBVbmxvY2tFbWFpbE1vZGFsIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL2NvbW1vbi91bmxvY2tFbWFpbFwiXG5pbXBvcnQgeyBVbmxvY2tQaG9uZU1vZGFsIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL2NvbW1vbi91bmxvY2tQaG9uZVwiXG5pbXBvcnQgeyBQb3BvdmVyLCBQb3BvdmVyQ29udGVudCwgUG9wb3ZlclRyaWdnZXIgfSBmcm9tIFwiQHVpL2NvbXBvbmVudHMvdWkvcG9wb3ZlclwiXG5pbXBvcnQgeyBEcm9wZG93bk1lbnUsIERyb3Bkb3duTWVudUNvbnRlbnQsIERyb3Bkb3duTWVudUl0ZW0sIERyb3Bkb3duTWVudVRyaWdnZXIsIERyb3Bkb3duTWVudUdyb3VwIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL2Ryb3Bkb3duLW1lbnVcIlxuaW1wb3J0IHsgc2VhcmNoQ29tcGFueUxlYWRzLCBzYXZlU2VhcmNoIH0gZnJvbSBcIkB1aS9hcGkvbGVhZHNcIlxuaW1wb3J0IHsgU2VhcmNoRmlsdGVycywgU2VhcmNoTGVhZHNSZXF1ZXN0LCBMZWFkIGFzIEFQSUxlYWQsIE5vcm1hbGl6ZWRMZWFkRGF0YSB9IGZyb20gXCJAdWkvdHlwaW5ncy9sZWFkXCJcbmltcG9ydCB7IHVzZVdvcmtzcGFjZSB9IGZyb20gXCJAdWkvcHJvdmlkZXJzL3dvcmtzcGFjZVwiXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkB1aS9wcm92aWRlcnMvdXNlclwiXG5pbXBvcnQgeyB1c2VBbGVydCB9IGZyb20gXCJAdWkvcHJvdmlkZXJzL2FsZXJ0XCJcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlUGFyYW1zIH0gZnJvbSBcIkB1aS9jb250ZXh0L3JvdXRlckNvbnRleHRcIlxuaW1wb3J0IHsgRGlhbG9nLCBEaWFsb2dDb250ZW50LCBEaWFsb2dIZWFkZXIsIERpYWxvZ1RpdGxlLCBEaWFsb2dGb290ZXIgfSBmcm9tIFwiQHVpL2NvbXBvbmVudHMvdWkvZGlhbG9nXCJcbmltcG9ydCB7IENvbXBhbnlUYWJsZUhlYWRlciB9IGZyb20gXCIuL2luZGV4XCJcblxuXG5pbnRlcmZhY2UgTGVhZCB7XG4gICAgaWQ6IHN0cmluZ1xuICAgIG5hbWU6IHN0cmluZ1xuICAgIGNvbXBhbnk6IHN0cmluZ1xuICAgIGVtYWlsOiBzdHJpbmdcbiAgICBwaG9uZTogc3RyaW5nXG4gICAgbGlua3M6IHN0cmluZ1xuICAgIGluZHVzdHJ5OiBzdHJpbmdcbiAgICBsb2NhdGlvbjogc3RyaW5nXG59XG5cbmludGVyZmFjZSBGaW5kTGVhZHNQcm9wcyB7XG4gICAgb25MZWFkQ3JlYXRlZD86IChsZWFkOiBhbnkpID0+IHZvaWRcbiAgICBzaWRlYmFyU3RhdGU/OiB7XG4gICAgICAgIGlzT3BlbjogYm9vbGVhblxuICAgICAgICBzZXRJc09wZW46IChvcGVuOiBib29sZWFuKSA9PiB2b2lkXG4gICAgfVxuXG4gICAgQWN0aW9uQnV0dG9uPzogYW55XG4gICAgVmlld0xpbmtzTW9kYWw/OiBhbnlcbiAgICBMZWFkQWN0aW9uc0Ryb3Bkb3duPzogYW55XG4gICAgbGVhZEFjdGlvbnM/OiBhbnlcbiAgICBsZWFkTWFuYWdlbWVudD86IGFueVxufVxuXG4vLyBBbGwgY29tcG9uZW50cyBhcmUgbm93IGltcG9ydGVkIGZyb20gaW5kZXhcblxuY29uc3QgRmluZExlYWRzID0gKHsgb25MZWFkQ3JlYXRlZCwgc2lkZWJhclN0YXRlLCBBY3Rpb25CdXR0b24sIFZpZXdMaW5rc01vZGFsLCBMZWFkQWN0aW9uc0Ryb3Bkb3duLCBsZWFkQWN0aW9ucywgbGVhZE1hbmFnZW1lbnQgfTogRmluZExlYWRzUHJvcHMpID0+IHtcbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICAgIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpXG4gICAgY29uc3QgeyB3b3Jrc3BhY2UgfSA9IHVzZVdvcmtzcGFjZSgpXG4gICAgY29uc3QgeyB0b2tlbiB9ID0gdXNlQXV0aCgpXG4gICAgY29uc3QgeyB0b2FzdCB9ID0gdXNlQWxlcnQoKVxuICAgIFxuXG4gICAgY29uc3QgW2xlYWRzLCBzZXRMZWFkc10gPSB1c2VTdGF0ZTxMZWFkW10+KFtdKVxuICAgIGNvbnN0IFtpc1NlYXJjaGluZywgc2V0SXNTZWFyY2hpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gICAgY29uc3QgW2hhc1NlYXJjaGVkLCBzZXRIYXNTZWFyY2hlZF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgICBjb25zdCBbc2VhcmNoUmVzdWx0cywgc2V0U2VhcmNoUmVzdWx0c10gPSB1c2VTdGF0ZTxhbnk+KG51bGwpXG4gICAgXG4gICAgLy8gU2VhcmNoIGZpbHRlcnMgc3RhdGUgLSBBRERFRFxuICAgIGNvbnN0IFtzZWFyY2hGaWx0ZXJzLCBzZXRTZWFyY2hGaWx0ZXJzXSA9IHVzZVN0YXRlPFNlYXJjaEZpbHRlcnM+KHtcbiAgICAgICAgcGVyc29uOiB7fSxcbiAgICAgICAgY29tcGFueToge30sXG4gICAgICAgIHNpZ25hbHM6IHt9LFxuICAgICAgICBjdXN0b21GaWx0ZXJzOiB7fVxuICAgIH0pXG4gICAgXG4gICAgLy8gRXhjbHVkZSBteSBsZWFkcyBzdGF0ZSAtIEFEREVEXG4gICAgY29uc3QgW2V4Y2x1ZGVNeUxlYWRzLCBzZXRFeGNsdWRlTXlMZWFkc10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgICBcblxuICAgIGNvbnN0IFtjdXJyZW50UGFnZSwgc2V0Q3VycmVudFBhZ2VdID0gdXNlU3RhdGUoMSlcbiAgICBjb25zdCBbdG90YWxQYWdlcywgc2V0VG90YWxQYWdlc10gPSB1c2VTdGF0ZSgxKVxuICAgIGNvbnN0IFt0b3RhbENvdW50LCBzZXRUb3RhbENvdW50XSA9IHVzZVN0YXRlKDApXG4gICAgXG4gICAgLy8gTW9kYWwgc3RhdGVzIGFyZSBub3cgbWFuYWdlZCBieSBzaGFyZWQgaG9va1xuICAgIGNvbnN0IFtpc1NhdmluZ1NlYXJjaCwgc2V0SXNTYXZpbmdTZWFyY2hdID0gdXNlU3RhdGUoZmFsc2UpXG4gICAgY29uc3QgW2N1cnJlbnRTZWFyY2hJZCwgc2V0Q3VycmVudFNlYXJjaElkXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpXG4gICAgXG4gICAgLy8gRGlhbG9nIHN0YXRlIGZvciBzYXZlIHNlYXJjaFxuICAgIGNvbnN0IFtzYXZlRGlhbG9nT3Blbiwgc2V0U2F2ZURpYWxvZ09wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gICAgY29uc3QgW3NlYXJjaE5hbWUsIHNldFNlYXJjaE5hbWVdID0gdXNlU3RhdGUoXCJcIilcbiAgICBcblxuICAgIC8vIFJlZ2lzdGVyIHVubG9jayBzdWNjZXNzIGNhbGxiYWNrc1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChsZWFkTWFuYWdlbWVudD8uc2V0VW5sb2NrRW1haWxDYWxsYmFjaykge1xuICAgICAgICAgICAgbGVhZE1hbmFnZW1lbnQuc2V0VW5sb2NrRW1haWxDYWxsYmFjaygodW5sb2NrZWRMZWFkOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkRGF0YSA9IHVubG9ja2VkTGVhZD8ubm9ybWFsaXplZERhdGEgfHwgdW5sb2NrZWRMZWFkPy5hcG9sbG9EYXRhPy5ub3JtYWxpemVkRGF0YVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSB0aGUgbGVhZCBpbiB0aGUgbGlzdCB3aXRoIHVubG9ja2VkIGRhdGFcbiAgICAgICAgICAgICAgICBzZXRMZWFkcyhwcmV2ID0+IFxuICAgICAgICAgICAgICAgICAgICBwcmV2Lm1hcChsZWFkID0+IFxuICAgICAgICAgICAgICAgICAgICAgICAgbGVhZC5pZCA9PT0gbGVhZE1hbmFnZW1lbnQ/LnNlbGVjdGVkTGVhZElkIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8geyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ubGVhZCwgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVtYWlsOiBub3JtYWxpemVkRGF0YT8uZW1haWwgfHwgXCJ1bmxvY2tcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbm9ybWFsaXplZERhdGE6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLihsZWFkIGFzIGFueSkubm9ybWFsaXplZERhdGEsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbWFpbDogbm9ybWFsaXplZERhdGE/LmVtYWlsIHx8IChsZWFkIGFzIGFueSkubm9ybWFsaXplZERhdGE/LmVtYWlsLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNFbWFpbFZpc2libGU6IHRydWUgLy8gU2V0IHRvIHRydWUgYWZ0ZXIgc3VjY2Vzc2Z1bCB1bmxvY2tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGxlYWRcbiAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGlmIChsZWFkTWFuYWdlbWVudD8uc2V0VW5sb2NrUGhvbmVDYWxsYmFjaykge1xuICAgICAgICAgICAgbGVhZE1hbmFnZW1lbnQuc2V0VW5sb2NrUGhvbmVDYWxsYmFjaygodW5sb2NrZWRMZWFkOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkRGF0YSA9IHVubG9ja2VkTGVhZD8ubm9ybWFsaXplZERhdGEgfHwgdW5sb2NrZWRMZWFkPy5hcG9sbG9EYXRhPy5ub3JtYWxpemVkRGF0YVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSB0aGUgbGVhZCBpbiB0aGUgbGlzdCB3aXRoIHVubG9ja2VkIGRhdGFcbiAgICAgICAgICAgICAgICBzZXRMZWFkcyhwcmV2ID0+XG4gICAgICAgICAgICAgICAgICAgIHByZXYubWFwKGxlYWQgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgIGxlYWQuaWQgPT09IGxlYWRNYW5hZ2VtZW50Py5zZWxlY3RlZExlYWRJZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5sZWFkLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwaG9uZTogbm9ybWFsaXplZERhdGE/LnBob25lIHx8IFwidW5sb2NrXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVtYWlsOiBub3JtYWxpemVkRGF0YT8uZW1haWwgfHwgbGVhZC5lbWFpbCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbm9ybWFsaXplZERhdGE6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLihsZWFkIGFzIGFueSkubm9ybWFsaXplZERhdGEsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbWFpbDogbm9ybWFsaXplZERhdGE/LmVtYWlsIHx8IChsZWFkIGFzIGFueSkubm9ybWFsaXplZERhdGE/LmVtYWlsLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGhvbmU6IG5vcm1hbGl6ZWREYXRhPy5waG9uZSB8fCAobGVhZCBhcyBhbnkpLm5vcm1hbGl6ZWREYXRhPy5waG9uZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzRW1haWxWaXNpYmxlOiBub3JtYWxpemVkRGF0YT8uaXNFbWFpbFZpc2libGUgfHwgKGxlYWQgYXMgYW55KS5ub3JtYWxpemVkRGF0YT8uaXNFbWFpbFZpc2libGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1Bob25lVmlzaWJsZTogdHJ1ZSAvLyBTZXQgdG8gdHJ1ZSBhZnRlciBzdWNjZXNzZnVsIHVubG9ja1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogbGVhZFxuICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgIH0sIFtsZWFkTWFuYWdlbWVudCwgbGVhZHMubGVuZ3RoXSlcbiAgICBcbiAgICAvLyBJbml0aWFsaXplIHNlYXJjaEZpbHRlcnMgd2l0aCBkZWZhdWx0IHZhbHVlcyB3aGVuIGNvbXBvbmVudCBtb3VudHNcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBjb25zdCBkZWZhdWx0RmlsdGVyczogU2VhcmNoRmlsdGVycyA9IHtcbiAgICAgICAgICAgIHBlcnNvbjoge30sXG4gICAgICAgICAgICBjb21wYW55OiB7fSxcbiAgICAgICAgICAgIHNpZ25hbHM6IHt9LFxuICAgICAgICAgICAgY3VzdG9tRmlsdGVyczoge31cbiAgICAgICAgfVxuICAgICAgICBzZXRTZWFyY2hGaWx0ZXJzKGRlZmF1bHRGaWx0ZXJzKVxuICAgIH0sIFtdKVxuICAgIFxuICAgIC8vIFNlYXJjaCBmdW5jdGlvbmFsaXR5XG4gICAgY29uc3Qgc2VhcmNoTGVhZHMgPSBhc3luYyAoZmlsdGVyczogU2VhcmNoRmlsdGVycyA9IHNlYXJjaEZpbHRlcnMsIHBhZ2U6IG51bWJlciA9IDEpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSBzZWFyY2hMZWFkcyBjYWxsZWRgKTtcbiAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSBJbnB1dCBmaWx0ZXJzOmAsIEpTT04uc3RyaW5naWZ5KGZpbHRlcnMsIG51bGwsIDIpKTtcbiAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSBDdXJyZW50IHNlYXJjaEZpbHRlcnMgc3RhdGU6YCwgSlNPTi5zdHJpbmdpZnkoc2VhcmNoRmlsdGVycywgbnVsbCwgMikpO1xuICAgICAgICBcbiAgICAgICAgaWYgKCF3b3Jrc3BhY2U/LndvcmtzcGFjZT8uaWQgfHwgIXRva2VuPy50b2tlbikge1xuICAgICAgICAgICAgdG9hc3QuZXJyb3IoXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cblxuICAgICAgICAvLyBDaGVjayBpZiBBUEkgaXMgcHJvcGVybHkgY29uZmlndXJlZFxuICAgICAgICBpZiAoIXByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwpIHtcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiU2VhcmNoIHNlcnZpY2UgaXMgY3VycmVudGx5IHVuYXZhaWxhYmxlLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLlwiKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cblxuICAgICAgICBzZXRJc1NlYXJjaGluZyh0cnVlKVxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gQ2xlYW4gZmlsdGVycyBiZWZvcmUgc2VuZGluZyAtIGV4Y2x1ZGVNeUxlYWRzIGlzIE5PVCBhIHNlYXJjaCBmaWx0ZXJcbiAgICAgICAgICAgIGNvbnN0IGNsZWFuRmlsdGVycyA9IHsgLi4uZmlsdGVycyB9O1xuICAgICAgICAgICAgLy8gZXhjbHVkZU15TGVhZHMgaXMgYWxyZWFkeSBhIHNlcGFyYXRlIHN0YXRlIHZhcmlhYmxlLCBubyBuZWVkIHRvIGRlbGV0ZSBmcm9tIGZpbHRlcnNcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gUHJvcGVyIGZyb250ZW5kIHBhZ2luYXRpb24gdGhyb3VnaCBhY2N1bXVsYXRlZCByZXN1bHRzXG4gICAgICAgICAgICAvLyBDYWxjdWxhdGUgaG93IG1hbnkgcGFnZXMgd2UgaGF2ZSBhdmFpbGFibGUgZnJvbSBwcmV2aW91cyBzZWFyY2hlc1xuICAgICAgICAgICAgLy8gY29uc3QgdG90YWxQYWdlc0F2YWlsYWJsZSA9IHNlYXJjaFJlc3VsdHMgPyBNYXRoLmNlaWwoc2VhcmNoUmVzdWx0cy50b3RhbENvdW50IC8gNTApIDogMTsgLy8gUmVtb3ZlZFxuICAgICAgICAgICAgLy8gY29uc3QgY3VycmVudFBhZ2UgPSBzZWFyY2hSZXN1bHRzICYmIHNlYXJjaFJlc3VsdHMuaGFzTmV4dFBhZ2UgPyBcbiAgICAgICAgICAgIC8vICAgICAoKHBhZ2VDb3VudGVyICUgdG90YWxQYWdlc0F2YWlsYWJsZSkgKyAxKSA6IDE7IC8vIFJlbW92ZWRcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gc2V0UGFnZUNvdW50ZXIocHJldiA9PiBwcmV2ICsgMSkgLy8gUmVtb3ZlZFxuICAgICAgICAgICAgXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBbRlJPTlRFTkQgREVCVUddIPCflKcgRklMVEVSUyBQUkVQQVJBVElPTjpgKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gLSBTZWFyY2ggZmlsdGVyczogJHtKU09OLnN0cmluZ2lmeShmaWx0ZXJzLCBudWxsLCAyKX1gKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gLSBleGNsdWRlTXlMZWFkcyAoc2VwYXJhdGUpOiAke2V4Y2x1ZGVNeUxlYWRzfWApO1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSAtIGV4Y2x1ZGVNeUxlYWRzIGlzIE5PVCBwYXJ0IG9mIHNlYXJjaCBjcml0ZXJpYWApO1xuICAgICAgICAgICAgLy8gY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSAtIFRvdGFsIHBhZ2VzIGF2YWlsYWJsZTogJHt0b3RhbFBhZ2VzQXZhaWxhYmxlfWApOyAvLyBSZW1vdmVkXG4gICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhg8J+UjSBbRlJPTlRFTkQgREVCVUddIC0gUGFnZSBjb3VudGVyOiAke3BhZ2VDb3VudGVyICsgMX0sIFJlcXVlc3RpbmcgcGFnZTogJHtjdXJyZW50UGFnZX1gKTsgLy8gUmVtb3ZlZFxuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1gKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc3Qgc2VhcmNoUmVxdWVzdDogU2VhcmNoTGVhZHNSZXF1ZXN0ID0ge1xuICAgICAgICAgICAgICAgIGZpbHRlcnM6IGNsZWFuRmlsdGVycyxcbiAgICAgICAgICAgICAgICBleGNsdWRlTXlMZWFkczogZXhjbHVkZU15TGVhZHMsIC8vIEZJWEVEOiBOb3cgdXNlcyB0aGUgc3RhdGUgdmFyaWFibGVcbiAgICAgICAgICAgICAgICBwYWdpbmF0aW9uOiB7XG4gICAgICAgICAgICAgICAgICAgIHBhZ2U6IHBhZ2UsIC8vIEFsd2F5cyBwYWdlIDEgZm9yIG5vd1xuICAgICAgICAgICAgICAgICAgICBsaW1pdDogNTBcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gU2VhcmNoIHF1ZXJ5OiBcIlwiYCk7IC8vIHNlYXJjaFF1ZXJ5IHJlbW92ZWRcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gV29ya3NwYWNlIElEOiAke3dvcmtzcGFjZS53b3Jrc3BhY2UuaWR9YCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBbRlJPTlRFTkQgREVCVUddIFRva2VuIGV4aXN0czogJHshIXRva2VuLnRva2VufWApO1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSBBUEkgVVJMOiAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9YCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBbRlJPTlRFTkQgREVCVUddIPCfmoAgU2VuZGluZyBzZWFyY2ggcmVxdWVzdCB0byBBUEk6YCwgSlNPTi5zdHJpbmdpZnkoc2VhcmNoUmVxdWVzdCwgbnVsbCwgMikpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSBDYWxsaW5nIHNlYXJjaENvbXBhbnlMZWFkcyBBUEkgZnVuY3Rpb24uLi5gKTtcblxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzZWFyY2hDb21wYW55TGVhZHModG9rZW4udG9rZW4sIHdvcmtzcGFjZS53b3Jrc3BhY2UuaWQsIHNlYXJjaFJlcXVlc3QpXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gQVBJIHJlc3BvbnNlIHJlY2VpdmVkOmAsIHJlc3BvbnNlKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmVycm9yKSB7XG4gICAgICAgICAgICAgICAgLy8gUHJvdmlkZSB1c2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2VzXG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gcmVzcG9uc2UuZXJyb3IudG9Mb3dlckNhc2UoKVxuICAgICAgICAgICAgICAgIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJ3VuYXV0aG9yaXplZCcpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnYXV0aGVudGljYXRpb24nKSkge1xuICAgICAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcihcIlNlc3Npb24gZXhwaXJlZC4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi5cIilcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGVycm9yTWVzc2FnZS5pbmNsdWRlcygnbmV0d29yaycpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnY29ubmVjdGlvbicpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiQ29ubmVjdGlvbiBlcnJvci4gUGxlYXNlIGNoZWNrIHlvdXIgaW50ZXJuZXQgYW5kIHRyeSBhZ2Fpbi5cIilcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGVycm9yTWVzc2FnZS5pbmNsdWRlcygnZW52JykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCd1bmRlZmluZWQnKSkge1xuICAgICAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcihcIlNlYXJjaCBzZXJ2aWNlIGlzIGN1cnJlbnRseSB1bmF2YWlsYWJsZS4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci5cIilcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byBzZWFyY2ggY29tcGFueSBsZWFkcy4gUGxlYXNlIHRyeSBhZ2Fpbi5cIilcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGNvbnN0IGFwaUxlYWRzID0gcmVzcG9uc2UuZGF0YT8uZGF0YT8ubGVhZHMgfHwgW11cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSDinIUgQVBJIGxlYWRzIGV4dHJhY3RlZDpgLCB7XG4gICAgICAgICAgICAgICAgbGVhZHNDb3VudDogYXBpTGVhZHMubGVuZ3RoLFxuICAgICAgICAgICAgICAgIGZpcnN0TGVhZDogYXBpTGVhZHNbMF0gfHwgbnVsbCxcbiAgICAgICAgICAgICAgICBhbGxMZWFkczogYXBpTGVhZHNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyBDb252ZXJ0IEFQSSBsZWFkcyB0byBVSSBmb3JtYXQgdXNpbmcgc2hhcmVkIGxvZ2ljXG4gICAgICAgICAgICBjb25zdCBjb252ZXJ0ZWRMZWFkczogTGVhZFtdID0gbGVhZE1hbmFnZW1lbnQ/LmNvbnZlcnRBcGlMZWFkc1RvVUkoYXBpTGVhZHMpIHx8IFtdXG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gQ29udmVydGVkIGxlYWRzIGZvciBVSTpgLCB7XG4gICAgICAgICAgICAgICAgY29udmVydGVkQ291bnQ6IGNvbnZlcnRlZExlYWRzLmxlbmd0aCxcbiAgICAgICAgICAgICAgICBmaXJzdENvbnZlcnRlZDogY29udmVydGVkTGVhZHNbMF0gfHwgbnVsbFxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIHNldExlYWRzKGNvbnZlcnRlZExlYWRzKVxuICAgICAgICAgICAgLy8gU3RvcmUgc2VhcmNoIHJlc3VsdHMgd2l0aCBwcm9wZXIgc3RydWN0dXJlIGZvciBwYWdpbmF0aW9uIGRpc3BsYXlcbiAgICAgICAgICAgIHNldFNlYXJjaFJlc3VsdHMoe1xuICAgICAgICAgICAgICAgIHRvdGFsQ291bnQ6IHJlc3BvbnNlLmRhdGE/LmRhdGE/LnRvdGFsQ291bnQsXG4gICAgICAgICAgICAgICAgbWV0YWRhdGE6IHJlc3BvbnNlLmRhdGE/LmRhdGE/Lm1ldGFkYXRhIHx8IHt9LFxuICAgICAgICAgICAgICAgIGhhc05leHRQYWdlOiByZXNwb25zZS5kYXRhPy5kYXRhPy5oYXNOZXh0UGFnZVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gVXBkYXRlIHBhZ2luYXRpb24gc3RhdGVcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlVG90YWxDb3VudCA9IHJlc3BvbnNlLmRhdGE/LmRhdGE/LnRvdGFsQ291bnQgfHwgMFxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2VIYXNOZXh0UGFnZSA9IHJlc3BvbnNlLmRhdGE/LmRhdGE/Lmhhc05leHRQYWdlIHx8IGZhbHNlXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIENhbGN1bGF0ZSB0b3RhbCBwYWdlcyBiYXNlZCBvbiBjYWNoZWQgcmVzdWx0c1xuICAgICAgICAgICAgLy8gU2hvdyBBTEwgY2FjaGVkIHBhZ2VzICsgT05FIHBhZ2UgdG8gdHJpZ2dlciBBcG9sbG8gZXhwYW5zaW9uXG4gICAgICAgICAgICBsZXQgYXZhaWxhYmxlUGFnZXMgPSAxXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIFVzZSB0aGUgYmFja2VuZCdzIHRvdGFsUGFnZXNBdmFpbGFibGUgdG8gc2hvdyBhbGwgY2FjaGVkIHBhZ2VzXG4gICAgICAgICAgICAvLyBUaGlzIGVuc3VyZXMgdXNlcnMgY2FuIG5hdmlnYXRlIHRvIEFMTCBhdmFpbGFibGUgcGFnZXMsIHBsdXMgb25lIG1vcmUgdG8gdHJpZ2dlciBBcG9sbG9cbiAgICAgICAgICAgIC8vIEV4YW1wbGU6IElmIGJhY2tlbmQgaGFzIDE0IHBhZ2VzLCBzaG93IDEsMiwzLDQsNSw2LDcsOCw5LDEwLDExLDEyLDEzLDE0LDE1XG4gICAgICAgICAgICAvLyBXaGVyZSBwYWdlIDE1IHRyaWdnZXJzIEFwb2xsbyB0byBnZXQgbW9yZSBsZWFkc1xuICAgICAgICAgICAgY29uc3QgdG90YWxQYWdlc0F2YWlsYWJsZSA9IHJlc3BvbnNlLmRhdGE/LmRhdGE/Lm1ldGFkYXRhPy50b3RhbFBhZ2VzQXZhaWxhYmxlIHx8IDFcbiAgICAgICAgICAgIGF2YWlsYWJsZVBhZ2VzID0gdG90YWxQYWdlc0F2YWlsYWJsZSArIDFcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgc2V0VG90YWxDb3VudChyZXNwb25zZVRvdGFsQ291bnQpXG4gICAgICAgICAgICBzZXRUb3RhbFBhZ2VzKGF2YWlsYWJsZVBhZ2VzKVxuICAgICAgICAgICAgc2V0Q3VycmVudFBhZ2UocGFnZSkgLy8gU2V0IHRvIHRoZSBwYWdlIHdlIGp1c3Qgc2VhcmNoZWQgZm9yXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIFRyYWNrIHRoZSBjdXJyZW50IHNlYXJjaCBJRCBmb3Igc2F2aW5nIGZ1bmN0aW9uYWxpdHlcbiAgICAgICAgICAgIGNvbnN0IHNlYXJjaElkID0gcmVzcG9uc2UuZGF0YT8uZGF0YT8uc2VhcmNoSWQgfHwgJydcbiAgICAgICAgICAgIHNldEN1cnJlbnRTZWFyY2hJZChzZWFyY2hJZClcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSDwn5OKIFBhZ2luYXRpb24gdXBkYXRlZDpgLCB7XG4gICAgICAgICAgICAgICAgY3VycmVudFBhZ2U6IHBhZ2UsXG4gICAgICAgICAgICAgICAgdG90YWxQYWdlczogYXZhaWxhYmxlUGFnZXMsXG4gICAgICAgICAgICAgICAgdG90YWxDb3VudDogcmVzcG9uc2VUb3RhbENvdW50LFxuICAgICAgICAgICAgICAgIGxlYWRzUGVyUGFnZTogNTBcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHNldEhhc1NlYXJjaGVkKHRydWUpXG4gICAgICAgICAgICB0b2FzdC5zdWNjZXNzKGBGb3VuZCAke2NvbnZlcnRlZExlYWRzLmxlbmd0aH0gY29tcGFueSBsZWFkc2ApXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10g4pyFIFNlYXJjaCBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5LiBTdGF0ZSB1cGRhdGVkLmApO1xuICAgICAgICAgICAgXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiU2VhcmNoIGVycm9yOlwiLCBlcnJvcilcbiAgICAgICAgICAgIC8vIFByb3ZpZGUgdXNlci1mcmllbmRseSBlcnJvciBtZXNzYWdlIHdpdGhvdXQgZXhwb3NpbmcgdGVjaG5pY2FsIGRldGFpbHNcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiU2VhcmNoIHNlcnZpY2UgaXMgdGVtcG9yYXJpbHkgdW5hdmFpbGFibGUuIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuXCIpXG4gICAgICAgIH0gZmluYWxseSB7XG4gICAgICAgICAgICBzZXRJc1NlYXJjaGluZyhmYWxzZSlcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gU2VhcmNoIHN0YXRlIHJlc2V0IHRvIG5vdCBzZWFyY2hpbmdgKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBcbiAgICAvLyBIYW5kbGUgZmlsdGVyIGNoYW5nZXMgZnJvbSBDb21wYW55RmlsdGVyXG4gICAgY29uc3QgaGFuZGxlRmlsdGVyQ2hhbmdlID0gKGZpbHRlcnM6IFNlYXJjaEZpbHRlcnMpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSBoYW5kbGVGaWx0ZXJDaGFuZ2UgY2FsbGVkIHdpdGg6YCwgSlNPTi5zdHJpbmdpZnkoZmlsdGVycywgbnVsbCwgMikpO1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBbRlJPTlRFTkQgREVCVUddIFByZXZpb3VzIHNlYXJjaEZpbHRlcnM6YCwgSlNPTi5zdHJpbmdpZnkoc2VhcmNoRmlsdGVycywgbnVsbCwgMikpO1xuICAgICAgICBcbiAgICAgICAgLy8gUmVzZXQgcGFnaW5hdGlvbiB3aGVuIGZpbHRlcnMgY2hhbmdlIC0gdGhpcyBlbnN1cmVzIGZyZXNoIHJlc3VsdHMgZnJvbSBwYWdlIDFcbiAgICAgICAgLy8gd2hlbiB1c2VyIGNoYW5nZXMgc2VhcmNoIGNyaXRlcmlhIChpbmR1c3RyeSwgY29tcGFueSBzaXplLCBldGMuKVxuICAgICAgICBzZXRDdXJyZW50UGFnZSgxKVxuICAgICAgICBzZXRUb3RhbFBhZ2VzKDEpXG4gICAgICAgIHNldFRvdGFsQ291bnQoMClcbiAgICAgICAgc2V0Q3VycmVudFNlYXJjaElkKCcnKVxuICAgICAgICBzZXRIYXNTZWFyY2hlZChmYWxzZSlcbiAgICAgICAgc2V0TGVhZHMoW10pXG4gICAgICAgIHNldFNlYXJjaFJlc3VsdHMobnVsbClcbiAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSDwn5SEIFBhZ2luYXRpb24gYW5kIHNlYXJjaCBzdGF0ZSByZXNldCB0byBwYWdlIDEgKGZpbHRlcnMgY2hhbmdlZClgKTtcbiAgICAgICAgXG4gICAgICAgIC8vIFNpbXBseSByZXBsYWNlIHRoZSBmaWx0ZXJzIGNvbXBsZXRlbHkgLSBDb21wYW55RmlsdGVyIHNlbmRzIHRoZSBjb21wbGV0ZSBzdGF0ZVxuICAgICAgICAvLyBObyBuZWVkIHRvIG1lcmdlIGFzIGl0IGNhbiBjYXVzZSBjb25mbGljdHMgYW5kIHN0YXRlIGluY29uc2lzdGVuY2llc1xuICAgICAgICBzZXRTZWFyY2hGaWx0ZXJzKGZpbHRlcnMpIC8vIEZJWEVEOiBOb3cgcHJvcGVybHkgdXBkYXRlcyB0aGUgc3RhdGVcbiAgICAgICAgXG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gc2VhcmNoRmlsdGVycyBzdGF0ZSB1cGRhdGVkIHRvOmAsIEpTT04uc3RyaW5naWZ5KGZpbHRlcnMsIG51bGwsIDIpKTtcbiAgICAgICAgLy8gRG9uJ3QgYXV0by1zZWFyY2ghIExldCB1c2VyIGNvbnRyb2wgc2VhcmNoIHZpYSBTZWFyY2ggYnV0dG9uXG4gICAgICAgIC8vIFRoaXMgcHJvdmlkZXMgYSBtdWNoIGNsZWFuZXIgVVggZmxvd1xuICAgIH1cbiAgICBcbiAgICAvLyBQYWdpbmF0aW9uIGZ1bmN0aW9uc1xuICAgIGNvbnN0IGhhbmRsZVBhZ2VDaGFuZ2UgPSAocGFnZTogbnVtYmVyKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gUGFnZSBjaGFuZ2UgcmVxdWVzdGVkOiAke3BhZ2V9YCk7XG4gICAgICAgIGlmIChwYWdlIDwgMSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSDinYwgSW52YWxpZCBwYWdlOiAke3BhZ2V9IC0gY2Fubm90IGJlIGxlc3MgdGhhbiAxYCk7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLy8gQWxsb3cgY2xpY2tpbmcgYmV5b25kIGN1cnJlbnQgdG90YWxQYWdlcyAtIHRoaXMgd2lsbCB0cmlnZ2VyIEFwb2xsbyBjYWxsc1xuICAgICAgICAvLyB3aGVuIHRoZSBiYWNrZW5kIGRldGVjdHMgdGhlIHJlcXVlc3RlZCBwYWdlIGRvZXNuJ3QgZXhpc3QgaW4gY2FjaGVcbiAgICAgICAgaWYgKHBhZ2UgPiB0b3RhbFBhZ2VzKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBbRlJPTlRFTkQgREVCVUddIPCflIQgUGFnZSAke3BhZ2V9IHJlcXVlc3RlZCBiZXlvbmQgY3VycmVudCBjYWNoZSAoJHt0b3RhbFBhZ2VzfSBwYWdlcylgKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gVGhpcyB3aWxsIHRyaWdnZXIgQXBvbGxvIGNhbGwgdG8gZ2V0IG1vcmUgbGVhZHNgKTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLy8gQXV0b21hdGljYWxseSBzZWFyY2ggZm9yIHRoZSBwYWdlIHdpdGggY3VycmVudCBmaWx0ZXJzIC0gdGhpcyBpcyB0aGUgZml4IVxuICAgICAgICAvLyBUaGlzIGVuc3VyZXMgcGFnaW5hdGlvbiB3b3JrcyBjb3JyZWN0bHkgd2l0aG91dCByZXF1aXJpbmcgZXh0cmEgYnV0dG9uIGNsaWNrc1xuICAgICAgICBzZXRDdXJyZW50UGFnZShwYWdlKVxuICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBbRlJPTlRFTkQgREVCVUddIOKchSBQYWdlIHNldCB0byAke3BhZ2V9LiBBdXRvbWF0aWNhbGx5IHNlYXJjaGluZyBmb3IgcGFnZSAke3BhZ2V9IHJlc3VsdHNgKTtcbiAgICAgICAgXG4gICAgICAgIC8vIFVzZSBjdXJyZW50IGZpbHRlcnMgYW5kIHNlYXJjaCBmb3IgdGhlIHNwZWNpZmljIHBhZ2VcbiAgICAgICAgc2VhcmNoTGVhZHMoc2VhcmNoRmlsdGVycywgcGFnZSlcbiAgICB9XG4gICAgXG4gICAgY29uc3QgY2FsY3VsYXRlVG90YWxQYWdlcyA9ICh0b3RhbExlYWRzOiBudW1iZXIsIGxlYWRzUGVyUGFnZTogbnVtYmVyID0gNTApID0+IHtcbiAgICAgICAgcmV0dXJuIE1hdGguY2VpbCh0b3RhbExlYWRzIC8gbGVhZHNQZXJQYWdlKVxuICAgIH1cbiAgICBcbiAgICBjb25zdCBnZW5lcmF0ZVBhZ2VOdW1iZXJzID0gKCkgPT4ge1xuICAgICAgICBjb25zdCBwYWdlcyA9IFtdXG4gICAgICAgIGNvbnN0IG1heFZpc2libGVQYWdlcyA9IDUgLy8gU2hvdyBtYXggNSBwYWdlIG51bWJlcnMgYXQgb25jZVxuICAgICAgICBcbiAgICAgICAgaWYgKHRvdGFsUGFnZXMgPD0gbWF4VmlzaWJsZVBhZ2VzKSB7XG4gICAgICAgICAgICAvLyBTaG93IGFsbCBwYWdlcyBpZiB0b3RhbCBpcyBzbWFsbFxuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gdG90YWxQYWdlczsgaSsrKSB7XG4gICAgICAgICAgICAgICAgcGFnZXMucHVzaChpKVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gU2hvdyBzbWFydCBwYWdpbmF0aW9uIHdpdGggZWxsaXBzaXNcbiAgICAgICAgICAgIGlmIChjdXJyZW50UGFnZSA8PSAzKSB7XG4gICAgICAgICAgICAgICAgLy8gTmVhciBzdGFydDogc2hvdyAxLCAyLCAzLCA0LCA1LCAuLi4sIGxhc3RcbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8PSA1OyBpKyspIHtcbiAgICAgICAgICAgICAgICAgICAgcGFnZXMucHVzaChpKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBwYWdlcy5wdXNoKCcuLi4nKVxuICAgICAgICAgICAgICAgIHBhZ2VzLnB1c2godG90YWxQYWdlcylcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoY3VycmVudFBhZ2UgPj0gdG90YWxQYWdlcyAtIDIpIHtcbiAgICAgICAgICAgICAgICAvLyBOZWFyIGVuZDogc2hvdyAxLCAuLi4sIGxhc3QtNCwgbGFzdC0zLCBsYXN0LTIsIGxhc3QtMSwgbGFzdFxuICAgICAgICAgICAgICAgIHBhZ2VzLnB1c2goMSlcbiAgICAgICAgICAgICAgICBwYWdlcy5wdXNoKCcuLi4nKVxuICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSB0b3RhbFBhZ2VzIC0gNDsgaSA8PSB0b3RhbFBhZ2VzOyBpKyspIHtcbiAgICAgICAgICAgICAgICAgICAgcGFnZXMucHVzaChpKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgLy8gTWlkZGxlOiBzaG93IDEsIC4uLiwgY3VycmVudC0xLCBjdXJyZW50LCBjdXJyZW50KzEsIC4uLiwgbGFzdFxuICAgICAgICAgICAgICAgIHBhZ2VzLnB1c2goMSlcbiAgICAgICAgICAgICAgICBwYWdlcy5wdXNoKCcuLi4nKVxuICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSBjdXJyZW50UGFnZSAtIDE7IGkgPD0gY3VycmVudFBhZ2UgKyAxOyBpKyspIHtcbiAgICAgICAgICAgICAgICAgICAgcGFnZXMucHVzaChpKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBwYWdlcy5wdXNoKCcuLi4nKVxuICAgICAgICAgICAgICAgIHBhZ2VzLnB1c2godG90YWxQYWdlcylcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgcmV0dXJuIHBhZ2VzXG4gICAgfVxuICAgIFxuICAgIC8vIE1hbnVhbCBzZWFyY2ggdHJpZ2dlclxuICAgIGNvbnN0IGhhbmRsZVNlYXJjaCA9ICgpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSBoYW5kbGVTZWFyY2ggY2FsbGVkYCk7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gQ3VycmVudCBzZWFyY2hGaWx0ZXJzOmAsIEpTT04uc3RyaW5naWZ5KHNlYXJjaEZpbHRlcnMsIG51bGwsIDIpKTtcbiAgICAgICAgY29uc29sZS5sb2coYPCflI0gW0ZST05URU5EIERFQlVHXSBDdXJyZW50IHBhZ2U6ICR7Y3VycmVudFBhZ2V9YCk7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gQ3VycmVudCBzZWFyY2hRdWVyeTogXCJcImApOyAvLyBzZWFyY2hRdWVyeSByZW1vdmVkXG4gICAgICAgIFxuICAgICAgICAvLyBVc2UgdGhlIGN1cnJlbnQgcGFnZSB0aGF0IHVzZXIgc2VsZWN0ZWQgKGRvbid0IHJlc2V0IHRvIDEpXG4gICAgICAgIC8vIFRoaXMgYWxsb3dzIHVzZXJzIHRvIGNsaWNrIHBhZ2UgMiwgdGhlbiBjbGljayBTZWFyY2ggdG8gZ2V0IHBhZ2UgMiByZXN1bHRzXG4gICAgICAgIFxuICAgICAgICAvLyBPTkxZIHNlbmQgc2lkZWJhciBmaWx0ZXJzIC0gaWdub3JlIHNlYXJjaCBpbnB1dCBmaWVsZFxuICAgICAgICAvLyBTZWFyY2ggaW5wdXQgaXMgZm9yIGEgZGlmZmVyZW50IHB1cnBvc2UsIG5vdCBmb3IgQXBvbGxvIHNlYXJjaGVzXG4gICAgICAgIGNvbnN0IGZpbHRlcnNUb1NlbmQgPSB7XG4gICAgICAgICAgICAuLi5zZWFyY2hGaWx0ZXJzIC8vIEZJWEVEOiBOb3cgdXNlcyB0aGUgYWN0dWFsIHNlYXJjaEZpbHRlcnMgc3RhdGVcbiAgICAgICAgfTtcbiAgICAgICAgXG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFtGUk9OVEVORCBERUJVR10gRmluYWwgZmlsdGVycyBiZWluZyBzZW50IHRvIHNlYXJjaExlYWRzIChzaWRlYmFyIGZpbHRlcnMgb25seSk6YCwgSlNPTi5zdHJpbmdpZnkoZmlsdGVyc1RvU2VuZCwgbnVsbCwgMikpO1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBbRlJPTlRFTkQgREVCVUddIFNlYXJjaGluZyBmb3IgcGFnZTogJHtjdXJyZW50UGFnZX1gKTtcbiAgICAgICAgc2VhcmNoTGVhZHMoZmlsdGVyc1RvU2VuZCwgY3VycmVudFBhZ2UpIC8vIFVzZSBjdXJyZW50IHBhZ2UsIG5vdCBhbHdheXMgcGFnZSAxXG4gICAgfVxuICAgIFxuICAgIC8vIEV2ZW50IGhhbmRsZXJzXG4gICAgY29uc3QgaGFuZGxlQ3JlYXRlTGVhZCA9ICgpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coXCJDcmVhdGUgY29tcGFueSBsZWFkIGNsaWNrZWRcIilcbiAgICB9XG4gICAgXG4gICAgLy8gaGFuZGxlSW1wb3J0TGVhZHMgaXMgbm93IHByb3ZpZGVkIGJ5IHNoYXJlZCBob29rXG4gICAgXG4gICAgICAgIC8vIFNlbGVjdGlvbiBoYW5kbGVycyBhcmUgbm93IHByb3ZpZGVkIGJ5IHNoYXJlZCBob29rXG4gICAgXG4gICAgLy8gU2F2ZSBjdXJyZW50IHNlYXJjaCByZXN1bHRzIGFzIGEgc2F2ZWQgc2VhcmNoXG4gICAgY29uc3QgaGFuZGxlU2F2ZUxlYWRzID0gKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgU0FWRSBMRUFEUyAtIENPTVBBTlldIPCfmoAgVXNlciBjbGlja2VkIFNhdmUgTGVhZHMgYnV0dG9uJylcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW0ZST05URU5EIFNBVkUgTEVBRFMgLSBDT01QQU5ZXSDwn5OKIEN1cnJlbnQgc3RhdGU6Jywge1xuICAgICAgICAgICAgaGFzV29ya3NwYWNlOiAhIXdvcmtzcGFjZT8ud29ya3NwYWNlPy5pZCxcbiAgICAgICAgICAgIGhhc1Rva2VuOiAhIXRva2VuPy50b2tlbixcbiAgICAgICAgICAgIGhhc1NlYXJjaGVkLFxuICAgICAgICAgICAgbGVhZHNDb3VudDogbGVhZHMubGVuZ3RoLFxuICAgICAgICAgICAgY3VycmVudFNlYXJjaElkLFxuICAgICAgICAgICAgY3VycmVudFBhZ2UsXG4gICAgICAgICAgICBzZWFyY2hGaWx0ZXJzOiBzZWFyY2hGaWx0ZXJzXG4gICAgICAgIH0pXG4gICAgICAgIFxuICAgICAgICBpZiAoIXdvcmtzcGFjZT8ud29ya3NwYWNlPy5pZCB8fCAhdG9rZW4/LnRva2VuKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgU0FWRSBMRUFEUyAtIENPTVBBTlldIOKdjCBNaXNzaW5nIGF1dGhlbnRpY2F0aW9uJylcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWRcIilcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBpZiAoIWhhc1NlYXJjaGVkIHx8IGxlYWRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW0ZST05URU5EIFNBVkUgTEVBRFMgLSBDT01QQU5ZXSDinYwgTm8gc2VhcmNoIHJlc3VsdHMgdG8gc2F2ZScpXG4gICAgICAgICAgICB0b2FzdC5lcnJvcihcIk5vIHNlYXJjaCByZXN1bHRzIHRvIHNhdmVcIilcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgU0FWRSBMRUFEUyAtIENPTVBBTlldIOKchSBWYWxpZGF0aW9uIHBhc3NlZCwgb3BlbmluZyBzYXZlIGRpYWxvZycpXG4gICAgICAgIFxuICAgICAgICAvLyBTZXQgZGVmYXVsdCBuYW1lIGFuZCBvcGVuIGRpYWxvZ1xuICAgICAgICBjb25zdCBkZWZhdWx0TmFtZSA9IGBDb21wYW55IFNlYXJjaCAtICR7bmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoKX1gXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5K+IFtGUk9OVEVORCBTQVZFIExFQURTIC0gQ09NUEFOWV0g8J+TnSBTZXR0aW5nIGRlZmF1bHQgbmFtZTonLCBkZWZhdWx0TmFtZSlcbiAgICAgICAgc2V0U2VhcmNoTmFtZShkZWZhdWx0TmFtZSlcbiAgICAgICAgc2V0U2F2ZURpYWxvZ09wZW4odHJ1ZSlcbiAgICB9XG4gICAgXG4gICAgLy8gSGFuZGxlIHRoZSBhY3R1YWwgc2F2ZSBvcGVyYXRpb25cbiAgICBjb25zdCBoYW5kbGVDb25maXJtU2F2ZSA9IGFzeW5jICgpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW0ZST05URU5EIENPTkZJUk0gU0FWRSAtIENPTVBBTlldIPCfmoAgVXNlciBjb25maXJtZWQgc2F2ZSBvcGVyYXRpb24nKVxuICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g8J+TiiBTYXZlIGRldGFpbHM6Jywge1xuICAgICAgICAgICAgc2VhcmNoTmFtZTogc2VhcmNoTmFtZT8udHJpbSgpLFxuICAgICAgICAgICAgbGVhZHNDb3VudDogbGVhZHMubGVuZ3RoLFxuICAgICAgICAgICAgY3VycmVudFBhZ2UsXG4gICAgICAgICAgICBjdXJyZW50U2VhcmNoSWQsXG4gICAgICAgICAgICBoYXNUb2tlbjogISF0b2tlbj8udG9rZW4sXG4gICAgICAgICAgICBoYXNXb3Jrc3BhY2U6ICEhd29ya3NwYWNlPy53b3Jrc3BhY2U/LmlkXG4gICAgICAgIH0pXG4gICAgICAgIFxuICAgICAgICBpZiAoIXNlYXJjaE5hbWUgfHwgc2VhcmNoTmFtZS50cmltKCkgPT09IFwiXCIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5K+IFtGUk9OVEVORCBDT05GSVJNIFNBVkUgLSBDT01QQU5ZXSDinYwgRW1wdHkgc2VhcmNoIG5hbWUnKVxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoXCJTZWFyY2ggbmFtZSBjYW5ub3QgYmUgZW1wdHlcIilcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g4pyFIFN0YXJ0aW5nIHNhdmUgcHJvY2Vzcy4uLicpXG4gICAgICAgIHNldElzU2F2aW5nU2VhcmNoKHRydWUpXG4gICAgICAgIHNldFNhdmVEaWFsb2dPcGVuKGZhbHNlKVxuICAgICAgICBcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHNhdmVSZXF1ZXN0ID0ge1xuICAgICAgICAgICAgICAgIG5hbWU6IHNlYXJjaE5hbWUudHJpbSgpLFxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBgU2F2ZWQgY29tcGFueSBzZWFyY2ggd2l0aCAke2xlYWRzLmxlbmd0aH0gbGVhZHMgZnJvbSBwYWdlICR7Y3VycmVudFBhZ2V9YCxcbiAgICAgICAgICAgICAgICBmaWx0ZXJzOiBzZWFyY2hGaWx0ZXJzLFxuICAgICAgICAgICAgICAgIHNlYXJjaElkOiBjdXJyZW50U2VhcmNoSWQgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgICAgICAgIHNlYXJjaFR5cGU6ICdjb21wYW55JyBhcyBjb25zdFxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g8J+TpCBTZW5kaW5nIHNhdmUgcmVxdWVzdDonLCBzYXZlUmVxdWVzdClcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5K+IFtGUk9OVEVORCBDT05GSVJNIFNBVkUgLSBDT01QQU5ZXSDwn5SXIEFQSSBjYWxsOiBzYXZlU2VhcmNoKCknKVxuICAgICAgICAgICAgXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNhdmVTZWFyY2godG9rZW4udG9rZW4sIHdvcmtzcGFjZS53b3Jrc3BhY2UuaWQsIHNhdmVSZXF1ZXN0KVxuICAgICAgICAgICAgXG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g8J+TpSBSZWNlaXZlZCByZXNwb25zZSBmcm9tIGJhY2tlbmQnKVxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW0ZST05URU5EIENPTkZJUk0gU0FWRSAtIENPTVBBTlldIPCflI0gRnVsbCByZXNwb25zZTonLCByZXNwb25zZSlcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5K+IFtGUk9OVEVORCBDT05GSVJNIFNBVkUgLSBDT01QQU5ZXSDwn5SNIFJlc3BvbnNlIGtleXM6JywgT2JqZWN0LmtleXMocmVzcG9uc2UpKVxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW0ZST05URU5EIENPTkZJUk0gU0FWRSAtIENPTVBBTlldIPCflI0gaXNTdWNjZXNzOicsIHJlc3BvbnNlLmlzU3VjY2VzcylcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5K+IFtGUk9OVEVORCBDT05GSVJNIFNBVkUgLSBDT01QQU5ZXSDwn5SNIGVycm9yOicsIHJlc3BvbnNlLmVycm9yKVxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW0ZST05URU5EIENPTkZJUk0gU0FWRSAtIENPTVBBTlldIPCflI0gZGF0YTonLCByZXNwb25zZS5kYXRhKVxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfkr4gW0ZST05URU5EIENPTkZJUk0gU0FWRSAtIENPTVBBTlldIPCflI0gZGF0YS5kYXRhOicsIHJlc3BvbnNlLmRhdGE/LmRhdGEpXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS5pc1N1Y2Nlc3MpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g4pyFIFNhdmUgb3BlcmF0aW9uIHN1Y2Nlc3NmdWwhJylcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g8J+TiyBTYXZlZCBzZWFyY2ggZGV0YWlsczonLCB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6IHNlYXJjaE5hbWUsXG4gICAgICAgICAgICAgICAgICAgIGxlYWRzQ291bnQ6IGxlYWRzLmxlbmd0aCxcbiAgICAgICAgICAgICAgICAgICAgc2VhcmNoSWQ6IHJlc3BvbnNlLmRhdGE/LmRhdGE/LnNhdmVkU2VhcmNoPy5pZCxcbiAgICAgICAgICAgICAgICAgICAgc2VhcmNoVHlwZTogJ2NvbXBhbnknXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICB0b2FzdC5zdWNjZXNzKGBTYXZlZCBcIiR7c2VhcmNoTmFtZX1cIiB3aXRoICR7bGVhZHMubGVuZ3RofSBjb21wYW55IGxlYWRzYClcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g4p2MIFNhdmUgb3BlcmF0aW9uIGZhaWxlZCcpXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g4p2MIEVycm9yIGRldGFpbHM6Jywge1xuICAgICAgICAgICAgICAgICAgICBpc1N1Y2Nlc3M6IHJlc3BvbnNlLmlzU3VjY2VzcyxcbiAgICAgICAgICAgICAgICAgICAgZXJyb3I6IHJlc3BvbnNlLmVycm9yLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1c1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgdG9hc3QuZXJyb3IocmVzcG9uc2UuZXJyb3IgfHwgXCJGYWlsZWQgdG8gc2F2ZSBzZWFyY2hcIilcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ/Cfkr4gW0ZST05URU5EIENPTkZJUk0gU0FWRSAtIENPTVBBTlldIOKdjCBFeGNlcHRpb24gZHVyaW5nIHNhdmUgb3BlcmF0aW9uOicsIGVycm9yKVxuICAgICAgICAgICAgY29uc29sZS5lcnJvcign8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g4p2MIEVycm9yIHR5cGU6JywgdHlwZW9mIGVycm9yKVxuICAgICAgICAgICAgY29uc29sZS5lcnJvcign8J+SviBbRlJPTlRFTkQgQ09ORklSTSBTQVZFIC0gQ09NUEFOWV0g4p2MIEVycm9yIG1lc3NhZ2U6JywgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcicpXG4gICAgICAgICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byBzYXZlIHNlYXJjaC4gUGxlYXNlIHRyeSBhZ2Fpbi5cIilcbiAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5K+IFtGUk9OVEVORCBDT05GSVJNIFNBVkUgLSBDT01QQU5ZXSDwn4+BIFNhdmUgb3BlcmF0aW9uIGNvbXBsZXRlZCwgcmVzZXR0aW5nIFVJIHN0YXRlJylcbiAgICAgICAgICAgIHNldElzU2F2aW5nU2VhcmNoKGZhbHNlKVxuICAgICAgICB9XG4gICAgfVxuICAgIFxuICAgIC8vIFVubG9jayBoYW5kbGVycyBhcmUgbm93IHByb3ZpZGVkIGJ5IHNoYXJlZCBob29rXG4gICAgXG4gICAgLy8gR2VuZXJhdGUgY29udGFjdCBsaW5rcyBiYXNlZCBvbiBsZWFkIGRhdGFcbiAgICAvLyBnZXRDb250YWN0TGlua3MgaXMgbm93IHByb3ZpZGVkIGJ5IHNoYXJlZCBob29rXG5cbiAgICBjb25zdCBoYW5kbGVOYW1lQ2xpY2sgPSAobGVhZDogTGVhZCkgPT4ge1xuICAgICAgICAvLyBOYXZpZ2F0ZSB0byBjb21wYW55IGRldGFpbHMgcGFnZSB1c2luZyByb3V0ZXJcbiAgICAgICAgY29uc3QgZG9tYWluID0gcGFyYW1zLmRvbWFpblxuICAgICAgICByb3V0ZXIucHVzaChgLyR7ZG9tYWlufS9sZWFkLWdlbmVyYXRpb24vY29tcGFuaWVzL2RldGFpbHMvJHtsZWFkLmlkfWApXG4gICAgfVxuICAgIFxuICAgIC8vIGZpbHRlcmVkTGVhZHMgaXMgbm93IHByb3ZpZGVkIGJ5IHNoYXJlZCBob29rXG4gICAgY29uc3QgZmlsdGVyZWRMZWFkcyA9IGxlYWRNYW5hZ2VtZW50Py5nZXRGaWx0ZXJlZExlYWRzKGxlYWRzLCB1bmRlZmluZWQsIHVuZGVmaW5lZCkgfHwgbGVhZHNcblxuICAgIHJldHVybiAoXG4gICAgICAgIDw+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweC0zIGgtMTAgYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMjAwIGJnLXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtYmxhY2tcIj5GaW5kIENvbXBhbmllczwvaDE+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAge2xlYWRNYW5hZ2VtZW50Py5zZWxlY3RlZExlYWRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHJvdW5kZWQtZnVsbCBwLTEuNSAhcHgtMyBoLWF1dG8gZ2FwLTIganVzdGlmeS1zdGFydCBmb250LW1lZGl1bSB0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtNzAwIGhvdmVyOmJnLXJlZC01MCBjdXJzb3ItcG9pbnRlclwiIG9uQ2xpY2s9eygpID0+IHsgc2V0TGVhZHMobGVhZHMuZmlsdGVyKGxlYWQgPT4gIWxlYWRNYW5hZ2VtZW50Py5zZWxlY3RlZExlYWRzLmluY2x1ZGVzKGxlYWQuaWQpKSk7IGxlYWRNYW5hZ2VtZW50Py5zZXRTZWxlY3RlZExlYWRzKFtdKSB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2hJY29uIGNsYXNzTmFtZT1cInNpemUtM1wiLz5EZWxldGUge2xlYWRNYW5hZ2VtZW50Py5zZWxlY3RlZExlYWRzLmxlbmd0aH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICB7LyogU2VhcmNoIElucHV0IHdpdGggQnV0dG9uICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogSW5saW5lIFBhZ2luYXRpb24gLSBMZWZ0IHNpZGUgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICB7aGFzU2VhcmNoZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFJlc3VsdHMgaW5mbyAtIFJlbW92ZWQgcGFnZSBkaXNwbGF5IHRleHQgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogUGFnaW5hdGlvbiBjb250cm9scyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RvdGFsUGFnZXMgPiAxID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBQcmV2aW91cyBidXR0b24gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVBhZ2VDaGFuZ2UoTWF0aC5tYXgoMSwgY3VycmVudFBhZ2UgLSAxKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtjdXJyZW50UGFnZSA9PT0gMX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHctNiBwLTAgdGV4dC14c1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDihpBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogUGFnZSBudW1iZXJzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dlbmVyYXRlUGFnZU51bWJlcnMoKS5tYXAoKHBhZ2UsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UmVhY3QuRnJhZ21lbnQga2V5PXtpbmRleH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3BhZ2UgPT09ICcuLi4nID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0xIHRleHQtbmV1dHJhbC00MDAgdGV4dC14c1wiPi4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtjdXJyZW50UGFnZSA9PT0gcGFnZSA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUGFnZUNoYW5nZShwYWdlIGFzIG51bWJlcil9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLTYgdy02IHAtMCB0ZXh0LXhzICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFBhZ2UgPT09IHBhZ2UgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXByaW1hcnkgdGV4dC13aGl0ZScgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2hvdmVyOmJnLW5ldXRyYWwtNTAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3BhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1JlYWN0LkZyYWdtZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogTmV4dCBidXR0b24gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVBhZ2VDaGFuZ2UoTWF0aC5taW4odG90YWxQYWdlcywgY3VycmVudFBhZ2UgKyAxKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtjdXJyZW50UGFnZSA9PT0gdG90YWxQYWdlc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHctNiBwLTAgdGV4dC14c1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDihpJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbmV1dHJhbC01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTaW5nbGUgcGFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgcm91bmRlZC1mdWxsIHAtMS41ICFweC0zIGgtYXV0byBnYXAtMiBqdXN0aWZ5LXN0YXJ0IGZsZXggaG92ZXI6YmctbmV1dHJhbC0xMDAgZm9jdXM6YmctbmV1dHJhbC0xMDAgYWN0aXZlOmJnLW5ldXRyYWwtMTAwIGl0ZW1zLWNlbnRlciB3aGl0ZXNwYWNlLW5vd3JhcCBmb250LW1lZGl1bSBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNYWduaWZ5aW5nR2xhc3NDaXJjbGVJY29uIGNsYXNzTmFtZT1cInNpemUtNFwiLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXQgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGFuZCBmaW5kIGNvbXBhbmllc1wiIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyB2YWx1ZT17c2VhcmNoUXVlcnl9IC8vIHNlYXJjaFF1ZXJ5IHJlbW92ZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSl9IC8vIHNlYXJjaFF1ZXJ5IHJlbW92ZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4gZS5rZXkgPT09ICdFbnRlcicgJiYgaGFuZGxlU2VhcmNoKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdHJhbnNpdGlvbi1hbGwgb3V0bGluZS1ub25lIGgtYXV0byAhcC0wICFyaW5nLTAgdy0xMiBmb2N1czp3LTQ4ICFiZy10cmFuc3BhcmVudCBib3JkZXItMCBzaGFkb3ctbm9uZSBkcm9wLXNoYWRvdy1ub25lIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCIgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2VhcmNofVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1NlYXJjaGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHJvdW5kZWQtZnVsbCBoLWF1dG8gcHgtMyBweS0xLjVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpc1NlYXJjaGluZyA/IFwiU2VhcmNoaW5nLi4uXCIgOiBcIlNlYXJjaFwifVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFNhdmUgTGVhZHMgQnV0dG9uIC0gb25seSBzaG93IHdoZW4gd2UgaGF2ZSBzZWFyY2ggcmVzdWx0cyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNTZWFyY2hlZCAmJiBsZWFkcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmVMZWFkc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU2F2aW5nU2VhcmNofVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgcm91bmRlZC1mdWxsIGgtYXV0byBweC0zIHB5LTEuNSBnYXAtMS41XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEYXRhYmFzZUljb24gY2xhc3NOYW1lPVwic2l6ZS0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2lzU2F2aW5nU2VhcmNoID8gXCJTYXZpbmcuLi5cIiA6IGBTYXZlICR7bGVhZHMubGVuZ3RofSBDb21wYW5pZXNgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICB7LyogU2lkZWJhciBGaWx0ZXIgLSBBbHdheXMgdmlzaWJsZSBmb3IgZmluZC1sZWFkcyAqL31cbiAgICAgICAgICAgICAgICA8Q29tcGFueUZpbHRlciBcbiAgICAgICAgICAgICAgICAgICAgZm9yY2VTaWRlYmFyPXt0cnVlfVxuICAgICAgICAgICAgICAgICAgICBvbkZpbHRlckNoYW5nZT17aGFuZGxlRmlsdGVyQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBleGNsdWRlTXlMZWFkcz17ZXhjbHVkZU15TGVhZHN9IC8vIEZJWEVEOiBOb3cgdXNlcyB0aGUgc3RhdGUgdmFyaWFibGVcbiAgICAgICAgICAgICAgICAgICAgb25FeGNsdWRlTXlMZWFkc0NoYW5nZT17KHZhbHVlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UkiBbRlJPTlRFTkQgREVCVUddIFBhcmVudCByZWNlaXZlZCBleGNsdWRlTXlMZWFkcyBjaGFuZ2U6YCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UkiBbRlJPTlRFTkQgREVCVUddIC0gUHJldmlvdXMgdmFsdWU6ICR7ZXhjbHVkZU15TGVhZHN9YCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UkiBbRlJPTlRFTkQgREVCVUddIC0gTmV3IHZhbHVlOiAke3ZhbHVlfWApO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYPCflJIgW0ZST05URU5EIERFQlVHXSAtIFRoaXMgd2lsbCBhZmZlY3QgZGlzcGxheSBmaWx0ZXJpbmcsIG5vdCBzZWFyY2ggY3JpdGVyaWFgKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SSIFtGUk9OVEVORCBERUJVR10gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09YCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEV4Y2x1ZGVNeUxlYWRzKHZhbHVlKSAvLyBGSVhFRDogTm93IHByb3Blcmx5IHVwZGF0ZXMgdGhlIHN0YXRlXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UkiBbRlJPTlRFTkQgREVCVUddIGV4Y2x1ZGVNeUxlYWRzIHN0YXRlIHVwZGF0ZWQgdG86ICR7dmFsdWV9YCk7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB7LyogVGFibGUgQ29udGFpbmVyICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRMZWFkcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAvKiBFbXB0eSBTdGF0ZSAtIElOU0lERSB0aGUgY29udGFpbmVyICovXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1hZ25pZnlpbmdHbGFzc0ljb24gY2xhc3NOYW1lPVwic2l6ZS0xMiB0ZXh0LW5ldXRyYWwtNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtbmV1dHJhbC05MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2hhc1NlYXJjaGVkID8gXCJObyBjb21wYW5pZXMgZm91bmRcIiA6IFwiUmVhZHkgdG8gZmluZCBjb21wYW5pZXNcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTUwMCBtYi00IHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNTZWFyY2hlZCBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiVHJ5IGFkanVzdGluZyB5b3VyIHNlYXJjaCBxdWVyeSBvciBmaWx0ZXJzIHRvIGZpbmQgbW9yZSByZXN1bHRzXCIgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIlVzZSB0aGUgc2VhcmNoIGJveCBhYm92ZSBvciBhcHBseSBmaWx0ZXJzIG9uIHRoZSBsZWZ0IHRvIGRpc2NvdmVyIGNvbXBhbmllcy4gQ2xpY2sgdGhlIFNlYXJjaCBidXR0b24gdG8gc3RhcnQuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBUYWJsZSB3aXRoIFJlc3VsdHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNjcm9sbEFyZWEgY2xhc3NOYW1lPVwic2l6ZS1mdWxsIHNjcm9sbEJsb2NrQ2hpbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q29tcGFueVRhYmxlSGVhZGVyIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkTGVhZHM9e2xlYWRNYW5hZ2VtZW50Py5zZWxlY3RlZExlYWRzIHx8IFtdfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcmVkTGVhZHM9e2ZpbHRlcmVkTGVhZHN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2VsZWN0QWxsPXtsZWFkTWFuYWdlbWVudD8uaGFuZGxlU2VsZWN0QWxsIHx8ICgoKSA9PiB7fSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRMZWFkcy5tYXAoKGxlYWQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlUm93IGtleT17bGVhZC5pZH0gY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMjAwIGhvdmVyOmJnLW5ldXRyYWwtNTAgdHJhbnNpdGlvbi1jb2xvcnMgZ3JvdXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwidy0xMiBweC0zIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrYm94IGNoZWNrZWQ9e2xlYWRNYW5hZ2VtZW50Py5zZWxlY3RlZExlYWRzLmluY2x1ZGVzKGxlYWQuaWQpfSBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkOiBib29sZWFuKSA9PiBsZWFkTWFuYWdlbWVudD8uaGFuZGxlU2VsZWN0TGVhZChsZWFkLmlkLCBjaGVja2VkKX0gY2xhc3NOYW1lPXtgJHtsZWFkTWFuYWdlbWVudD8uc2VsZWN0ZWRMZWFkcy5pbmNsdWRlcyhsZWFkLmlkKSA/ICdvcGFjaXR5LTEwMCB2aXNpYmxlJyA6ICdpbnZpc2libGUgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIGdyb3VwLWhvdmVyOnZpc2libGUnfWB9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwicHgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeSBob3Zlcjp0ZXh0LXByaW1hcnkvODAgZm9udC1zZW1pYm9sZCB0ZXh0LXhzIGN1cnNvci1wb2ludGVyIGhvdmVyOmJvcmRlci1iIGhvdmVyOmJvcmRlci1uZXV0cmFsLTYwMCBiZy10cmFuc3BhcmVudCBib3JkZXItbm9uZSBwLTBcIiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTmFtZUNsaWNrKGxlYWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2xlYWQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJweC0xIHRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+e2xlYWQuaW5kdXN0cnl9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInB4LTEgdGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57bGVhZC5sb2NhdGlvbn08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwicHgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsZWFkLm5vcm1hbGl6ZWREYXRhPy5lbWFpbCAmJiBsZWFkLm5vcm1hbGl6ZWREYXRhPy5pc0VtYWlsVmlzaWJsZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmxhY2sgZm9udC1tZWRpdW0gdHJ1bmNhdGUgbWF4LXctWzEyMHB4XVwiIHRpdGxlPXtsZWFkLm5vcm1hbGl6ZWREYXRhLmVtYWlsfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsZWFkLm5vcm1hbGl6ZWREYXRhLmVtYWlsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWN0aW9uQnV0dG9uIGljb249e0VudmVsb3BlSWNvbn0gb25DbGljaz17KCkgPT4gbGVhZE1hbmFnZW1lbnQ/LmhhbmRsZVVubG9ja0VtYWlsKGxlYWQuaWQpfT5VbmxvY2sgRW1haWw8L0FjdGlvbkJ1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInB4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bGVhZC5ub3JtYWxpemVkRGF0YT8ucGhvbmUgJiYgbGVhZC5ub3JtYWxpemVkRGF0YT8uaXNQaG9uZVZpc2libGUgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsYWNrIGZvbnQtbWVkaXVtIHRydW5jYXRlIG1heC13LVsxMjBweF1cIiB0aXRsZT17bGVhZC5ub3JtYWxpemVkRGF0YS5waG9uZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bGVhZC5ub3JtYWxpemVkRGF0YS5waG9uZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjdGlvbkJ1dHRvbiBpY29uPXtQaG9uZUljb259IG9uQ2xpY2s9eygpID0+IGxlYWRNYW5hZ2VtZW50Py5oYW5kbGVVbmxvY2tQaG9uZShsZWFkLmlkKX0+VW5sb2NrIE1vYmlsZTwvQWN0aW9uQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwicHgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxWaWV3TGlua3NNb2RhbCBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcj17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeSBob3Zlcjp0ZXh0LXByaW1hcnkvODAgZm9udC1zZW1pYm9sZCB0ZXh0LXhzIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIGN1cnNvci1wb2ludGVyIGJnLXRyYW5zcGFyZW50IGJvcmRlci1ub25lIHAtMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZpZXcgbGlua3M8Q2hldnJvblJpZ2h0SWNvbiBjbGFzc05hbWU9XCJzaXplLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxpbmtzPXtsZWFkTWFuYWdlbWVudD8uZ2V0Q29udGFjdExpbmtzKGxlYWQpIHx8IFtdfSBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInctMTIgcHgtMiByZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMZWFkQWN0aW9uc0Ryb3Bkb3duIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCIgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCIgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC03IHctNyBwLTAgdGV4dC1uZXV0cmFsLTQwMCBob3Zlcjp0ZXh0LW5ldXRyYWwtNjAwIGludmlzaWJsZSBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgZ3JvdXAtaG92ZXI6dmlzaWJsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVsbGlwc2lzVmVydGljYWxJY29uIGNsYXNzTmFtZT1cInNpemUtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVhZERhdGE9e2xlYWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uU2VuZEVtYWlsPXsoKSA9PiBsZWFkQWN0aW9ucz8uaGFuZGxlU2VuZEVtYWlsKGxlYWQuaWQsIGxlYWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkFkZFRvU2VnbWVudHM9eygpID0+IGxlYWRBY3Rpb25zPy5oYW5kbGVBZGRUb1NlZ21lbnRzKGxlYWQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkFkZFRvRGF0YWJhc2U9eygpID0+IGxlYWRBY3Rpb25zPy5oYW5kbGVBZGRUb0RhdGFiYXNlKGxlYWQuaWQsIGxlYWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkFkZFRvV29ya2Zsb3c9eygpID0+IGxlYWRBY3Rpb25zPy5oYW5kbGVBZGRUb1dvcmtmbG93KGxlYWQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVCb2R5PlxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlPlxuICAgICAgICAgICAgICAgICAgICB7LyogSG9yaXpvbnRhbCBsaW5lIHVuZGVyIHRhYmxlICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTIwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvU2Nyb2xsQXJlYT5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgICAgIHsvKiBVbmxvY2sgbW9kYWxzIGFyZSBub3cgaGFuZGxlZCBieSBTaGFyZWRNb2RhbHMgaW4gdGhlIGluZGV4ICovfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogU2F2ZSBTZWFyY2ggRGlhbG9nICovfVxuICAgICAgICAgICAgPERpYWxvZyBvcGVuPXtzYXZlRGlhbG9nT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRTYXZlRGlhbG9nT3Blbn0+XG4gICAgICAgICAgICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwibWF4LXctWzk1dnddIHNtOm1heC13LVs2MDBweF0gIXJvdW5kZWQtbm9uZSBwLTRcIiBhcmlhLWRlc2NyaWJlZGJ5PVwic2F2ZS1zZWFyY2gtZGVzY3JpcHRpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxEaWFsb2dUaXRsZT5TYXZlIFNlYXJjaDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGlkPVwic2F2ZS1zZWFyY2gtZGVzY3JpcHRpb25cIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbWItNFwiPkVudGVyIGEgbmFtZSBmb3IgdGhpcyBzYXZlZCBzZWFyY2g6PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaE5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hOYW1lKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHNlYXJjaCBuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbktleURvd249eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ29uZmlybVNhdmUoKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8RGlhbG9nRm9vdGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInRleHQteHNcIiBvbkNsaWNrPXsoKSA9PiBzZXRTYXZlRGlhbG9nT3BlbihmYWxzZSl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiIG9uQ2xpY2s9e2hhbmRsZUNvbmZpcm1TYXZlfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBTYXZlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9EaWFsb2dGb290ZXI+XG4gICAgICAgICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgICAgICAgICAgPC9EaWFsb2c+XG4gICAgICAgIDwvPlxuICAgIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgRmluZExlYWRzXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIklucHV0IiwiQ2hlY2tib3giLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlUm93IiwiU2Nyb2xsQXJlYSIsIkVudmVsb3BlSWNvbiIsIlBob25lSWNvbiIsIkVsbGlwc2lzVmVydGljYWxJY29uIiwiVHJhc2hJY29uIiwiQ2hldnJvblJpZ2h0SWNvbiIsIkRhdGFiYXNlSWNvbiIsIk1hZ25pZnlpbmdHbGFzc0ljb24iLCJNYWduaWZ5aW5nR2xhc3NDaXJjbGVJY29uIiwiQ29tcGFueUZpbHRlciIsInNlYXJjaENvbXBhbnlMZWFkcyIsInNhdmVTZWFyY2giLCJ1c2VXb3Jrc3BhY2UiLCJ1c2VBdXRoIiwidXNlQWxlcnQiLCJ1c2VSb3V0ZXIiLCJ1c2VQYXJhbXMiLCJEaWFsb2ciLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJEaWFsb2dGb290ZXIiLCJDb21wYW55VGFibGVIZWFkZXIiLCJGaW5kTGVhZHMiLCJvbkxlYWRDcmVhdGVkIiwic2lkZWJhclN0YXRlIiwiQWN0aW9uQnV0dG9uIiwiVmlld0xpbmtzTW9kYWwiLCJMZWFkQWN0aW9uc0Ryb3Bkb3duIiwibGVhZEFjdGlvbnMiLCJsZWFkTWFuYWdlbWVudCIsInJvdXRlciIsInBhcmFtcyIsIndvcmtzcGFjZSIsInRva2VuIiwidG9hc3QiLCJsZWFkcyIsInNldExlYWRzIiwiaXNTZWFyY2hpbmciLCJzZXRJc1NlYXJjaGluZyIsImhhc1NlYXJjaGVkIiwic2V0SGFzU2VhcmNoZWQiLCJzZWFyY2hSZXN1bHRzIiwic2V0U2VhcmNoUmVzdWx0cyIsInNlYXJjaEZpbHRlcnMiLCJzZXRTZWFyY2hGaWx0ZXJzIiwicGVyc29uIiwiY29tcGFueSIsInNpZ25hbHMiLCJjdXN0b21GaWx0ZXJzIiwiZXhjbHVkZU15TGVhZHMiLCJzZXRFeGNsdWRlTXlMZWFkcyIsImN1cnJlbnRQYWdlIiwic2V0Q3VycmVudFBhZ2UiLCJ0b3RhbFBhZ2VzIiwic2V0VG90YWxQYWdlcyIsInRvdGFsQ291bnQiLCJzZXRUb3RhbENvdW50IiwiaXNTYXZpbmdTZWFyY2giLCJzZXRJc1NhdmluZ1NlYXJjaCIsImN1cnJlbnRTZWFyY2hJZCIsInNldEN1cnJlbnRTZWFyY2hJZCIsInNhdmVEaWFsb2dPcGVuIiwic2V0U2F2ZURpYWxvZ09wZW4iLCJzZWFyY2hOYW1lIiwic2V0U2VhcmNoTmFtZSIsInVzZUVmZmVjdCIsInNldFVubG9ja0VtYWlsQ2FsbGJhY2siLCJ1bmxvY2tlZExlYWQiLCJub3JtYWxpemVkRGF0YSIsImFwb2xsb0RhdGEiLCJwcmV2IiwibWFwIiwibGVhZCIsImlkIiwic2VsZWN0ZWRMZWFkSWQiLCJlbWFpbCIsImlzRW1haWxWaXNpYmxlIiwic2V0VW5sb2NrUGhvbmVDYWxsYmFjayIsInBob25lIiwiaXNQaG9uZVZpc2libGUiLCJsZW5ndGgiLCJkZWZhdWx0RmlsdGVycyIsInNlYXJjaExlYWRzIiwiZmlsdGVycyIsInBhZ2UiLCJjb25zb2xlIiwibG9nIiwiSlNPTiIsInN0cmluZ2lmeSIsImVycm9yIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJyZXNwb25zZSIsImNsZWFuRmlsdGVycyIsInNlYXJjaFJlcXVlc3QiLCJwYWdpbmF0aW9uIiwibGltaXQiLCJlcnJvck1lc3NhZ2UiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwiYXBpTGVhZHMiLCJkYXRhIiwibGVhZHNDb3VudCIsImZpcnN0TGVhZCIsImFsbExlYWRzIiwiY29udmVydGVkTGVhZHMiLCJjb252ZXJ0QXBpTGVhZHNUb1VJIiwiY29udmVydGVkQ291bnQiLCJmaXJzdENvbnZlcnRlZCIsIm1ldGFkYXRhIiwiaGFzTmV4dFBhZ2UiLCJyZXNwb25zZVRvdGFsQ291bnQiLCJyZXNwb25zZUhhc05leHRQYWdlIiwiYXZhaWxhYmxlUGFnZXMiLCJ0b3RhbFBhZ2VzQXZhaWxhYmxlIiwic2VhcmNoSWQiLCJsZWFkc1BlclBhZ2UiLCJzdWNjZXNzIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwiaGFuZGxlUGFnZUNoYW5nZSIsImNhbGN1bGF0ZVRvdGFsUGFnZXMiLCJ0b3RhbExlYWRzIiwiTWF0aCIsImNlaWwiLCJnZW5lcmF0ZVBhZ2VOdW1iZXJzIiwicGFnZXMiLCJtYXhWaXNpYmxlUGFnZXMiLCJpIiwicHVzaCIsImhhbmRsZVNlYXJjaCIsImZpbHRlcnNUb1NlbmQiLCJoYW5kbGVDcmVhdGVMZWFkIiwiaGFuZGxlU2F2ZUxlYWRzIiwiaGFzV29ya3NwYWNlIiwiaGFzVG9rZW4iLCJkZWZhdWx0TmFtZSIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJoYW5kbGVDb25maXJtU2F2ZSIsInRyaW0iLCJzYXZlUmVxdWVzdCIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsInVuZGVmaW5lZCIsInNlYXJjaFR5cGUiLCJPYmplY3QiLCJrZXlzIiwiaXNTdWNjZXNzIiwic2F2ZWRTZWFyY2giLCJzdGF0dXMiLCJFcnJvciIsIm1lc3NhZ2UiLCJoYW5kbGVOYW1lQ2xpY2siLCJkb21haW4iLCJmaWx0ZXJlZExlYWRzIiwiZ2V0RmlsdGVyZWRMZWFkcyIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwic2VsZWN0ZWRMZWFkcyIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZmlsdGVyIiwic2V0U2VsZWN0ZWRMZWFkcyIsInNpemUiLCJtYXgiLCJkaXNhYmxlZCIsImluZGV4IiwiRnJhZ21lbnQiLCJzcGFuIiwibWluIiwicGxhY2Vob2xkZXIiLCJvbktleURvd24iLCJlIiwia2V5IiwiZm9yY2VTaWRlYmFyIiwib25GaWx0ZXJDaGFuZ2UiLCJvbkV4Y2x1ZGVNeUxlYWRzQ2hhbmdlIiwidmFsdWUiLCJoMyIsInAiLCJoYW5kbGVTZWxlY3RBbGwiLCJjaGVja2VkIiwib25DaGVja2VkQ2hhbmdlIiwiaGFuZGxlU2VsZWN0TGVhZCIsImJ1dHRvbiIsImluZHVzdHJ5IiwibG9jYXRpb24iLCJ0aXRsZSIsImljb24iLCJoYW5kbGVVbmxvY2tFbWFpbCIsImhhbmRsZVVubG9ja1Bob25lIiwidHJpZ2dlciIsImxpbmtzIiwiZ2V0Q29udGFjdExpbmtzIiwibGVhZERhdGEiLCJvblNlbmRFbWFpbCIsImhhbmRsZVNlbmRFbWFpbCIsIm9uQWRkVG9TZWdtZW50cyIsImhhbmRsZUFkZFRvU2VnbWVudHMiLCJvbkFkZFRvRGF0YWJhc2UiLCJoYW5kbGVBZGRUb0RhdGFiYXNlIiwib25BZGRUb1dvcmtmbG93IiwiaGFuZGxlQWRkVG9Xb3JrZmxvdyIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJhcmlhLWRlc2NyaWJlZGJ5Iiwib25DaGFuZ2UiLCJ0YXJnZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/find-leads.tsx\n"));

/***/ })

});