"use client"

import React, { useMemo, useState } from "react"
import { useWorkspace } from "@ui/providers/workspace"
import { DatabaseColumn, DatabaseFieldDataType, DatabaseColumnDbValue } from "@repo/app-db-utils/src/typings/db"
import { But<PERSON> } from "@ui/components/ui/button"
import { TrashIcon, PlusIcon } from "@heroicons/react/24/outline"
import { DatabaseColumnSelect } from "@ui/components/workspace/main/common/databaseColumnSelect"
import { LeadFieldSelector } from "./LeadFieldSelector"
import { useLeadSubstitution } from "./LeadDataSubstitutionProvider"
import { Loader } from "@ui/components/custom-ui/loader"

interface Update {
  columnId: string
  value: string // Stores the lead field key (e.g., 'name', 'email', 'company')
}

interface EnhancedUpdateRecordEditorProps {
  databaseId: string
  updates: Update[]
  onUpdate: (updates: Update[]) => void
  variableKeyMap?: Record<string, string>
}

export const EnhancedUpdateRecordEditor = ({ 
  databaseId, 
  updates, 
  onUpdate, 
  variableKeyMap 
}: EnhancedUpdateRecordEditorProps) => {
  const { databaseStore } = useWorkspace()
  const { leadData } = useLeadSubstitution()
  const [processing, setProcessing] = useState(false)
  
  const database = databaseStore[databaseId]
  
  const updatableColumns = useMemo(() => {
    if (!database) return []
    
    const nonUpdatableTypes = [
      DatabaseFieldDataType.AI,                   
      DatabaseFieldDataType.ButtonGroup,     
      DatabaseFieldDataType.ScannableCode,   
      DatabaseFieldDataType.CreatedBy,       
      DatabaseFieldDataType.UpdatedBy,
      DatabaseFieldDataType.CreatedAt,
      DatabaseFieldDataType.UpdatedAt,
      DatabaseFieldDataType.Summarize,       
      DatabaseFieldDataType.Derived,
      DatabaseFieldDataType.UUID,
      DatabaseFieldDataType.Files,
    ]

    return Object.values(database.database.definition.columnsMap)
      .filter(col => !nonUpdatableTypes.includes(col.type) && col.id !== 'id')
  }, [database])

  if (!database) {
    return <p className="text-xs text-red-500">Selected database not found.</p>
  }

  const handleUpdate = (index: number, newUpdate: Partial<Update>) => {
    const updated = [...updates]
    updated[index] = { ...updated[index], ...newUpdate }
    onUpdate(updated)
  }

  const addField = () => {
    const updated = [...updates, { columnId: '', value: '' }]
    onUpdate(updated)
  }

  const deleteField = (index: number) => {
    const updated = updates.filter((_, i) => i !== index)
    onUpdate(updated)
  }


  const columnIds = updates.map(update => update.columnId).filter(Boolean)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Map Lead Fields to Database Columns</h3>
        <Button onClick={addField} size="sm" variant="outline">
          <PlusIcon className="w-4 h-4 mr-1" />
          Add Field
        </Button>
      </div>

      <div className="space-y-3">
        {updates.map((update, index) => {
          const selected = update.columnId ? [update.columnId] : []
          const column = selected.length > 0 ? database.database.definition.columnsMap[update.columnId] : undefined

          return (
            <div key={index} className="flex gap-4 items-center p-3 border rounded-lg">
              {/* Left: Database Column */}
              <div className="flex-1">
                <label className="text-xs font-medium text-muted-foreground mb-1 block">
                  Database Column
                </label>
                <DatabaseColumnSelect
                  databaseId={databaseId}
                  selected={selected}
                  onChange={(selectedIds) => handleUpdate(index, { columnId: selectedIds[0] || '' })}
                  isMultiple={false}
                  filterFn={(col) => !columnIds.includes(col.id) || col.id === update.columnId}
                />
              </div>
              
              {/* Right: Lead Field */}
              <div className="flex-1">
                <label className="text-xs font-medium text-muted-foreground mb-1 block">
                  Lead Field
                </label>
                <LeadFieldSelector
                  value={update.value}
                  onChange={(value) => handleUpdate(index, { value })}
                  placeholder="Select lead field..."
                />
              </div>

              {/* Delete Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => deleteField(index)}
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
              >
                <TrashIcon className="w-4 h-4" />
              </Button>
            </div>
          )
        })}
      </div>


      {updates.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p className="text-sm">No field mappings yet</p>
          <p className="text-xs">Click "Add Field" to start mapping lead data to database columns</p>
        </div>
      )}
    </div>
  )
}
