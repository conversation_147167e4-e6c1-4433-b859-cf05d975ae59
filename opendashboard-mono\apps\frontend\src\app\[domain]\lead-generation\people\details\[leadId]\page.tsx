"use client"

import React from "react"
import { Details } from "@ui/components/workspace/main/lead-generation/details/people"
import { useLeadManagement } from "@ui/components/workspace/main/lead-generation/people"
import { useParams, useRouter } from "next/navigation"
import { useAuth } from "@ui/providers/user"
import { useWorkspace } from "@ui/providers/workspace"

const PersonDetailsPage = () => {
    const params = useParams()
    const router = useRouter()
    const { token } = useAuth()
    const { workspace } = useWorkspace()
    const { handleSendEmail } = useLeadManagement()
    
    const handleBack = () => {
        const domain = params.domain
        router.push(`/${domain}/lead-generation/people/find-leads`)
    }

    // Don't render until we have the required data
    if (!token?.token || !workspace?.workspace?.id) {
        return (
            <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
        )
    }

    return (
        <Details 
            leadId={params.leadId as string}
            token={token.token}
            workspaceId={workspace.workspace.id}
            onBack={handleBack}
            handleSendEmail={handleSendEmail}
        />
    )
}

export default PersonDetailsPage
