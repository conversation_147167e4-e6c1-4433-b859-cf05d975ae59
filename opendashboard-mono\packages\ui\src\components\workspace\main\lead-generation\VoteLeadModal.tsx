"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@ui/components/ui/dialog"
import { Button } from "@ui/components/ui/button"
import { Textarea } from "@ui/components/ui/textarea"
import { Label } from "@ui/components/ui/label"
import { Cross2Icon } from "@radix-ui/react-icons"
import { HandThumbUpIcon, HandThumbDownIcon } from "@heroicons/react/24/outline"
import { CheckIcon } from "@ui/components/icons/FontAwesomeRegular"
import { voteLead, getVoteFeedbackOptions } from "@ui/api/leads"
import { VoteFeedbackOptions } from "@ui/typings/lead"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { useAlert } from "@ui/providers/alert"
import { cn } from "@ui/lib/utils"

interface VoteLeadModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  leadId: string
  onVoteSuccess?: () => void
}

export const VoteLeadModal = ({ open, onOpenChange, leadId, onVoteSuccess }: VoteLeadModalProps) => {
  const { workspace } = useWorkspace()
  const { token } = useAuth()
  const { toast } = useAlert()
  
  const [voting, setVoting] = useState(false)
  const [voted, setVoted] = useState(false)
  const [selectedVote, setSelectedVote] = useState<'up' | 'down' | null>(null)
  const [feedback, setFeedback] = useState("")
  const [customFeedback, setCustomFeedback] = useState("")
  const [feedbackOptions, setFeedbackOptions] = useState<VoteFeedbackOptions | null>(null)
  const [loadingOptions, setLoadingOptions] = useState(false)

  // Load feedback options when modal opens
  useEffect(() => {
    if (open && !feedbackOptions && workspace?.workspace?.id && token?.token) {
      loadFeedbackOptions()
    }
  }, [open, feedbackOptions, workspace?.workspace?.id, token?.token])

  const loadFeedbackOptions = async () => {
    if (!workspace?.workspace?.id || !token?.token) return

    setLoadingOptions(true)
    try {
      const response = await getVoteFeedbackOptions(token.token, workspace.workspace.id)
      
      if (response.isSuccess && response.data?.data) {
        setFeedbackOptions(response.data.data)
      } else {
        console.error("Failed to load feedback options:", response.error)
        // Set empty options if API fails
        setFeedbackOptions({
          upvote: [],
          downvote: []
        })
      }
    } catch (error) {
      console.error("Error loading feedback options:", error)
      // Set empty options
      setFeedbackOptions({
        upvote: [],
        downvote: []
      })
    } finally {
      setLoadingOptions(false)
    }
  }

  const handleVote = async () => {
    if (!selectedVote || !workspace?.workspace?.id || !token?.token) {
      toast.error("Error", {
        description: "Please select a vote"
      })
      return
    }

    const finalFeedback = feedback === "custom" ? customFeedback.trim() : feedback || "Vote recorded"

    setVoting(true)
    try {
      const response = await voteLead(token.token, workspace.workspace.id, leadId, {
        vote: selectedVote,
        feedback: finalFeedback,
        feedbackType: feedback === "custom" ? "custom" : "predefined"
      })

      if (response.isSuccess) {
        setVoted(true)
        toast.success("Success", {
          description: `Your ${selectedVote === 'up' ? 'upvote' : 'downvote'} has been recorded`
        })
        
        // Call the success callback
        onVoteSuccess?.()
        
        // Close modal immediately
        setVoting(false)
        onOpenChange(false)
        // Reset all state
        setSelectedVote(null)
        setFeedback("")
        setCustomFeedback("")
        setVoted(false)
      } else {
        toast.error("Error", {
          description: response.error || "Failed to record vote"
        })
      }
    } catch (error) {
      console.error("Vote error:", error)
      toast.error("Error", {
        description: "Failed to record vote"
      })
    } finally {
      setVoting(false)
    }
  }

  const handleClose = () => {
    if (!voting) {
      // Reset all state
      setSelectedVote(null)
      setFeedback("")
      setCustomFeedback("")
      setVoted(false)
      onOpenChange(false)
    }
  }

  const getCurrentFeedbackOptions = () => {
    if (!feedbackOptions || !selectedVote) return []
    return selectedVote === 'up' ? feedbackOptions.upvote : feedbackOptions.downvote
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-[500px] max-w-[90vw] p-0 rounded-none border border-neutral-200 bg-white shadow-lg" hideCloseBtn>
        <DialogTitle className="sr-only">Vote on Lead Quality</DialogTitle>
        <DialogDescription className="sr-only">
          Rate this lead quality by selecting upvote or downvote and providing feedback
        </DialogDescription>
        
        {/* Close Button */}
        <div className="absolute top-4 right-4 z-10">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-neutral-400 hover:text-neutral-600 cursor-pointer"
            onClick={handleClose}
            disabled={voting}
          >
            <Cross2Icon className="size-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-10 text-center">
          {/* Illustration */}
          <div className="mb-8 flex justify-center mt-8">
            {voted ? (
              <div className="w-24 h-14 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckIcon className="w-8 h-8 text-green-600" />
              </div>
            ) : (
              <div className="flex gap-4">
                <button
                  onClick={() => setSelectedVote(selectedVote === 'up' ? null : 'up')}
                  disabled={voting}
                  className={cn(
                    "w-16 h-16 rounded-lg border-2 transition-all flex items-center justify-center",
                    selectedVote === 'up'
                      ? "border-green-500 bg-green-100 text-green-600"
                      : "border-neutral-300 hover:border-neutral-400 text-neutral-500 hover:text-neutral-600"
                  )}
                >
                  <HandThumbUpIcon className="w-8 h-8" />
                </button>
                <button
                  onClick={() => setSelectedVote(selectedVote === 'down' ? null : 'down')}
                  disabled={voting}
                  className={cn(
                    "w-16 h-16 rounded-lg border-2 transition-all flex items-center justify-center",
                    selectedVote === 'down'
                      ? "border-red-500 bg-red-100 text-red-600"
                      : "border-neutral-300 hover:border-neutral-400 text-neutral-500 hover:text-neutral-600"
                  )}
                >
                  <HandThumbDownIcon className="w-8 h-8" />
                </button>
              </div>
            )}
          </div>

          {/* Heading */}
          <h2 className="text-2xl font-semibold text-black mb-4">
            {voted 
              ? "Vote Recorded!" 
              : selectedVote 
                ? `Why did you ${selectedVote === 'up' ? 'upvote' : 'downvote'} this lead?`
                : "Rate this lead quality"
            }
          </h2>

          {/* Description */}
          <p className="text-xs text-muted-foreground mb-8 leading-relaxed">
            {voted 
              ? "Thank you for your vote! This helps us improve lead quality."
              : selectedVote
                ? "You can optionally provide feedback to help us improve lead quality."
                : "Your vote helps us improve lead quality and relevance."
            }
          </p>

          {/* Feedback Selection - Optional */}
          {!voted && selectedVote && (
            <div className="mb-6 text-left">
              <Label className="text-sm font-medium mb-3 block text-neutral-600">
                Optional feedback:
              </Label>
              
              {loadingOptions ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-neutral-900"></div>
                </div>
              ) : (
                <div className="space-y-2 mb-4">
                  {(getCurrentFeedbackOptions() || []).map((option, index) => (
                    <label key={index} className="flex items-center gap-2 cursor-pointer hover:bg-neutral-50 p-2 rounded">
                      <input
                        type="radio"
                        name="feedback"
                        value={option}
                        checked={feedback === option}
                        onChange={(e) => setFeedback(e.target.value)}
                        className="w-4 h-4 text-neutral-900 border-neutral-300 focus:ring-neutral-500"
                      />
                      <span className="text-sm text-neutral-700">{option}</span>
                    </label>
                  ))}
                  
                  {/* Custom feedback option */}
                  <label className="flex items-center gap-2 cursor-pointer hover:bg-neutral-50 p-2 rounded">
                    <input
                      type="radio"
                      name="feedback"
                      value="custom"
                      checked={feedback === "custom"}
                      onChange={(e) => setFeedback(e.target.value)}
                      className="w-4 h-4 text-neutral-900 border-neutral-300 focus:ring-neutral-500"
                    />
                    <span className="text-sm text-neutral-700">
                      {getCurrentFeedbackOptions() && getCurrentFeedbackOptions().length > 0 
                        ? "Other (please specify)" 
                        : "Enter your feedback"
                      }
                    </span>
                  </label>
                </div>
              )}

              {/* Custom feedback textarea */}
              {feedback === "custom" && (
                <div className="mt-4">
                  <Textarea
                    placeholder="Please describe your feedback..."
                    value={customFeedback}
                    onChange={(e) => setCustomFeedback(e.target.value)}
                    className="w-full min-h-[80px] text-sm"
                    rows={3}
                  />
                </div>
              )}
            </div>
          )}

          {/* Action Button */}
          {voted ? (
            <div className="flex items-center justify-center gap-2 text-green-600">
              <CheckIcon className="w-5 h-5" />
              <span className="text-sm font-medium">Vote Recorded Successfully</span>
            </div>
          ) : (
            <Button
              onClick={handleVote}
              disabled={!selectedVote || voting}
              className="w-full h-14 bg-neutral-900 hover:bg-neutral-800 text-white font-semibold rounded-full text-lg cursor-pointer disabled:opacity-50"
            >
              {voting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Recording Vote...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  {selectedVote === 'up' ? (
                    <HandThumbUpIcon className="w-5 h-5" />
                  ) : selectedVote === 'down' ? (
                    <HandThumbDownIcon className="w-5 h-5" />
                  ) : null}
                  {selectedVote ? `Submit ${selectedVote === 'up' ? 'Upvote' : 'Downvote'}` : 'Select Vote'}
                </div>
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}