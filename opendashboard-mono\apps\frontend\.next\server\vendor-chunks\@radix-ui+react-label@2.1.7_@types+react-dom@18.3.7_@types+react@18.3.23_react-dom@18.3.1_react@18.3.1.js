"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-label/dist/index.mjs":
/*!*****************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \*****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // src/label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            props.onMouseDown?.(event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-label/dist/index.mjs\n");

/***/ })

};
;