"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-use-escape-keydown@1.0.0_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-use-escape-keydown@1.0.0_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-escape-keydown@1.0.0_react@18.3.1/node_modules/@radix-ui/react-use-escape-keydown/dist/index.module.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-use-escape-keydown@1.0.0_react@18.3.1/node_modules/@radix-ui/react-use-escape-keydown/dist/index.module.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ $addc16e1bbe58fd0$export$3a72a57244d6e765)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.0.0_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.module.js\");\n\n\n\n\n\n/**\n * Listens for when the escape key is down\n */ function $addc16e1bbe58fd0$export$3a72a57244d6e765(onEscapeKeyDownProp) {\n    const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            if (event.key === 'Escape') onEscapeKeyDown(event);\n        };\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>document.removeEventListener('keydown', handleKeyDown)\n        ;\n    }, [\n        onEscapeKeyDown\n    ]);\n}\n\n\n\n\n\n//# sourceMappingURL=index.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByYWRpeC11aStyZWFjdC11c2UtZXNjYXBlLWtleWRvd25AMS4wLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWVzY2FwZS1rZXlkb3duL2Rpc3QvaW5kZXgubW9kdWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0Q7QUFDcUM7Ozs7QUFJekY7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGdGQUFxQjtBQUNqRCxJQUFJLGdEQUFnQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBOzs7OztBQUt1RTtBQUN2RSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9AcmFkaXgtdWkrcmVhY3QtdXNlLWVzY2FwZS1rZXlkb3duQDEuMC4wX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1vZHVsZS5qcz8xMzk5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dXNlRWZmZWN0IGFzICRoUFNRNSR1c2VFZmZlY3R9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHt1c2VDYWxsYmFja1JlZiBhcyAkaFBTUTUkdXNlQ2FsbGJhY2tSZWZ9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZlwiO1xuXG5cblxuLyoqXG4gKiBMaXN0ZW5zIGZvciB3aGVuIHRoZSBlc2NhcGUga2V5IGlzIGRvd25cbiAqLyBmdW5jdGlvbiAkYWRkYzE2ZTFiYmU1OGZkMCRleHBvcnQkM2E3MmE1NzI0NGQ2ZTc2NShvbkVzY2FwZUtleURvd25Qcm9wKSB7XG4gICAgY29uc3Qgb25Fc2NhcGVLZXlEb3duID0gJGhQU1E1JHVzZUNhbGxiYWNrUmVmKG9uRXNjYXBlS2V5RG93blByb3ApO1xuICAgICRoUFNRNSR1c2VFZmZlY3QoKCk9PntcbiAgICAgICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudCk9PntcbiAgICAgICAgICAgIGlmIChldmVudC5rZXkgPT09ICdFc2NhcGUnKSBvbkVzY2FwZUtleURvd24oZXZlbnQpO1xuICAgICAgICB9O1xuICAgICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5RG93bik7XG4gICAgICAgIHJldHVybiAoKT0+ZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pXG4gICAgICAgIDtcbiAgICB9LCBbXG4gICAgICAgIG9uRXNjYXBlS2V5RG93blxuICAgIF0pO1xufVxuXG5cblxuXG5leHBvcnQgeyRhZGRjMTZlMWJiZTU4ZmQwJGV4cG9ydCQzYTcyYTU3MjQ0ZDZlNzY1IGFzIHVzZUVzY2FwZUtleWRvd259O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubW9kdWxlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-escape-keydown@1.0.0_react@18.3.1/node_modules/@radix-ui/react-use-escape-keydown/dist/index.module.js\n");

/***/ })

};
;