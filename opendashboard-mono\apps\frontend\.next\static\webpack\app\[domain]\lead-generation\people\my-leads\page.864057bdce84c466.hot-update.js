"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/lead-generation/people/my-leads/page",{

/***/ "(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/index.tsx":
/*!*****************************************************************************************!*\
  !*** ../../packages/ui/src/components/workspace/main/lead-generation/company/index.tsx ***!
  \*****************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActionButton: function() { return /* binding */ ActionButton; },\n/* harmony export */   CompanyTableHeader: function() { return /* binding */ CompanyTableHeader; },\n/* harmony export */   LeadActionsDropdown: function() { return /* binding */ LeadActionsDropdown; },\n/* harmony export */   ViewLinksModal: function() { return /* binding */ ViewLinksModal; },\n/* harmony export */   useLeadManagement: function() { return /* binding */ useLeadManagement; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _my_leads__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./my-leads */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/my-leads.tsx\");\n/* harmony import */ var _find_leads__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./find-leads */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/find-leads.tsx\");\n/* harmony import */ var _saved_search__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./saved-search */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/saved-search.tsx\");\n/* harmony import */ var _ui_providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ui/providers/user */ \"(app-pages-browser)/../../packages/ui/src/providers/user.tsx\");\n/* harmony import */ var _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ui/providers/workspace */ \"(app-pages-browser)/../../packages/ui/src/providers/workspace.tsx\");\n/* harmony import */ var _ui_components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ui/components/ui/button */ \"(app-pages-browser)/../../packages/ui/src/components/ui/button.tsx\");\n/* harmony import */ var _ui_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ui/components/ui/popover */ \"(app-pages-browser)/../../packages/ui/src/components/ui/popover.tsx\");\n/* harmony import */ var _ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ui/components/ui/dropdown-menu */ \"(app-pages-browser)/../../packages/ui/src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @ui/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/../../packages/ui/src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ui/components/ui/dialog */ \"(app-pages-browser)/../../packages/ui/src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ui/components/ui/table */ \"(app-pages-browser)/../../packages/ui/src/components/ui/table.tsx\");\n/* harmony import */ var _ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @ui/components/ui/checkbox */ \"(app-pages-browser)/../../packages/ui/src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_components_ui_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ui/components/ui/input */ \"(app-pages-browser)/../../packages/ui/src/components/ui/input.tsx\");\n/* harmony import */ var _ui_components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @ui/components/ui/textarea */ \"(app-pages-browser)/../../packages/ui/src/components/ui/textarea.tsx\");\n/* harmony import */ var _ui_components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @ui/components/ui/label */ \"(app-pages-browser)/../../packages/ui/src/components/ui/label.tsx\");\n/* harmony import */ var _ui_api_leads__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @ui/api/leads */ \"(app-pages-browser)/../../packages/ui/src/api/leads.ts\");\n/* harmony import */ var _ui_api_workflow__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @ui/api/workflow */ \"(app-pages-browser)/../../packages/ui/src/api/workflow.ts\");\n/* harmony import */ var _repo_app_db_utils_dist_typings_workflow__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @repo/app-db-utils/dist/typings/workflow */ \"(app-pages-browser)/../../packages/app-db-utils/dist/typings/workflow.js\");\n/* harmony import */ var _repo_app_db_utils_dist_typings_workflow__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(_repo_app_db_utils_dist_typings_workflow__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var _ui_providers_alert__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @ui/providers/alert */ \"(app-pages-browser)/../../packages/ui/src/providers/alert.tsx\");\n/* harmony import */ var _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @ui/context/routerContext */ \"(app-pages-browser)/../../packages/ui/src/context/routerContext.tsx\");\n/* harmony import */ var _ui_components_workspace_main_common_unlockEmail__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @ui/components/workspace/main/common/unlockEmail */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/common/unlockEmail.tsx\");\n/* harmony import */ var _ui_components_workspace_main_common_unlockPhone__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @ui/components/workspace/main/common/unlockPhone */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/common/unlockPhone.tsx\");\n/* harmony import */ var _common_TwoStepDatabaseMapping__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../common/TwoStepDatabaseMapping */ \"(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/common/TwoStepDatabaseMapping.tsx\");\n/* harmony import */ var _ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @ui/components/custom-ui/loader */ \"(app-pages-browser)/../../packages/ui/src/components/custom-ui/loader.tsx\");\n/* __next_internal_client_entry_do_not_use__ ActionButton,ViewLinksModal,LeadActionsDropdown,CompanyTableHeader,useLeadManagement,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActionButton = (param)=>{\n    let { icon: Icon, children, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n        variant: \"outline\",\n        size: \"sm\",\n        onClick: onClick,\n        className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"truncate\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ActionButton;\nconst ViewLinksModal = (param)=>{\n    let { trigger, links } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n        open: isOpen,\n        onOpenChange: setIsOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                asChild: true,\n                children: trigger\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                className: \"w-64 p-4\",\n                align: \"end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-semibold text-sm mb-2\",\n                        children: \"Social Links\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: links.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: \"No links available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 25\n                        }, undefined) : links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: link.url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors p-2 rounded hover:bg-blue-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_10__.UpRightFromSquareIcon, {\n                                        className: \"size-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"truncate\",\n                                        children: link.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, link.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 29\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 72,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n        lineNumber: 68,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ViewLinksModal, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c1 = ViewLinksModal;\nconst LeadActionsDropdown = (param)=>{\n    let { trigger, onSendEmail, onAddToSegments, onAddToDatabase, onAddToWorkflow, leadData } = param;\n    var _leadData_normalizedData, _leadData_apolloData;\n    const email = (leadData === null || leadData === void 0 ? void 0 : (_leadData_normalizedData = leadData.normalizedData) === null || _leadData_normalizedData === void 0 ? void 0 : _leadData_normalizedData.email) || (leadData === null || leadData === void 0 ? void 0 : (_leadData_apolloData = leadData.apolloData) === null || _leadData_apolloData === void 0 ? void 0 : _leadData_apolloData.email) || (leadData === null || leadData === void 0 ? void 0 : leadData.email);\n    const hasEmail = !!email;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                asChild: true,\n                children: trigger\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 120,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                className: \"w-56 rounded-none text-neutral-800 font-semibold\",\n                align: \"end\",\n                sideOffset: 4,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuGroup, {\n                    className: \"p-1 flex flex-col gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                            className: \"text-xs rounded-none p-2 py-1.5 flex items-center gap-2 \".concat(hasEmail ? \"cursor-pointer\" : \"cursor-not-allowed opacity-50\"),\n                            onClick: hasEmail ? onSendEmail : undefined,\n                            disabled: !hasEmail,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_10__.EnvelopeIcon, {\n                                    className: \"size-3 text-neutral-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Send Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 25\n                                }, undefined),\n                                !hasEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-auto text-xs text-gray-400\",\n                                    children: \"(Locked)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 39\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                            className: \"text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer\",\n                            onClick: onAddToSegments,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_10__.ChartLineIcon, {\n                                    className: \"size-3 text-neutral-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Add to Segments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                            className: \"text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer\",\n                            onClick: onAddToDatabase,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_10__.DatabaseIcon, {\n                                    className: \"size-3 text-neutral-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Add to Database\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                            className: \"text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer\",\n                            onClick: onAddToWorkflow,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_10__.CodeMergeIcon, {\n                                    className: \"size-3 text-neutral-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Add to Workflow\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 123,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n        lineNumber: 119,\n        columnNumber: 9\n    }, undefined);\n};\n_c2 = LeadActionsDropdown;\nconst CompanyTableHeader = (param)=>{\n    let { selectedLeads, filteredLeads, handleSelectAll } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHeader, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n            className: \"border-b border-neutral-200 bg-white sticky top-0 z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                    className: \"w-12 h-10 px-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_13__.Checkbox, {\n                        checked: selectedLeads.length === filteredLeads.length && filteredLeads.length > 0,\n                        onCheckedChange: (checked)=>handleSelectAll(checked, filteredLeads)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                    className: \"h-10 px-1 text-left font-bold text-black\",\n                    children: \"Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                    className: \"h-10 px-1 text-left font-bold text-black\",\n                    children: \"Industry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                    className: \"h-10 px-1 text-left font-bold text-black\",\n                    children: \"Location\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                    className: \"h-10 px-1 text-left font-bold text-black\",\n                    children: \"Email\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                    className: \"h-10 px-1 text-left font-bold text-black\",\n                    children: \"Phone number\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                    className: \"h-10 px-1 text-left font-bold text-black\",\n                    children: \"Social Links\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                    className: \"w-12 h-10 px-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n            lineNumber: 179,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, undefined);\n};\n_c3 = CompanyTableHeader;\nconst useLeadManagement = ()=>{\n    _s1();\n    var _s = $RefreshSig$();\n    const { toast } = (0,_ui_providers_alert__WEBPACK_IMPORTED_MODULE_20__.useAlert)();\n    const { token } = (0,_ui_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { workspace } = (0,_ui_providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace)();\n    const router = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_21__.useRouter)();\n    const params = (0,_ui_context_routerContext__WEBPACK_IMPORTED_MODULE_21__.useParams)();\n    const [selectedLeads, setSelectedLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [emailModalOpen, setEmailModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [phoneModalOpen, setPhoneModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLeadId, setSelectedLeadId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const unlockEmailCallbackRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const unlockPhoneCallbackRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [addToDatabaseDialogOpen, setAddToDatabaseDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLeadForDatabase, setSelectedLeadForDatabase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addingToDatabase, setAddingToDatabase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLeadIdForAction, setSelectedLeadIdForAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sendEmailDialogOpen, setSendEmailDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emailSubject, setEmailSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailBody, setEmailBody] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sendingEmail, setSendingEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLeadForEmail, setSelectedLeadForEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addToSegmentDialogOpen, setAddToSegmentDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [segmentName, setSegmentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addingToSegment, setAddingToSegment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addToWorkflowDialogOpen, setAddToWorkflowDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWorkflowId, setSelectedWorkflowId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addingToWorkflow, setAddingToWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableWorkflows, setAvailableWorkflows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorkflows, setLoadingWorkflows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSelectAll = (checked, leads)=>{\n        if (checked) {\n            setSelectedLeads(leads.map((lead)=>lead.id));\n        } else {\n            setSelectedLeads([]);\n        }\n    };\n    const handleSelectLead = (leadId, checked)=>{\n        if (checked) {\n            setSelectedLeads([\n                ...selectedLeads,\n                leadId\n            ]);\n        } else {\n            setSelectedLeads(selectedLeads.filter((id)=>id !== leadId));\n        }\n    };\n    const handleUnlockEmail = (leadId)=>{\n        setSelectedLeadId(leadId);\n        setEmailModalOpen(true);\n    };\n    const handleUnlockPhone = (leadId)=>{\n        setSelectedLeadId(leadId);\n        setPhoneModalOpen(true);\n    };\n    const handleNameClick = (lead)=>{\n        const domain = params.domain;\n        router.push(\"/\".concat(domain, \"/lead-generation/company/details/\").concat(lead.id));\n    };\n    const handleViewLinks = (leadId)=>{};\n    const getContactLinks = (lead)=>{\n        const links = [];\n        if (lead.name) {\n            links.push({\n                id: \"linkedin\",\n                title: \"LinkedIn Profile\",\n                url: \"https://linkedin.com/search/results/people/?keywords=\".concat(encodeURIComponent(lead.name + \" \" + lead.company))\n            });\n        }\n        if (lead.company) {\n            links.push({\n                id: \"company\",\n                title: \"Company Website\",\n                url: \"https://www.google.com/search?q=\".concat(encodeURIComponent(lead.company + \" official website\"))\n            });\n        }\n        if (lead.company) {\n            links.push({\n                id: \"company-linkedin\",\n                title: \"Company LinkedIn\",\n                url: \"https://linkedin.com/search/results/companies/?keywords=\".concat(encodeURIComponent(lead.company))\n            });\n        }\n        if (lead.company) {\n            links.push({\n                id: \"google\",\n                title: \"Google Search\",\n                url: \"https://www.google.com/search?q=\".concat(encodeURIComponent(lead.company))\n            });\n        }\n        if (lead.company) {\n            links.push({\n                id: \"employees\",\n                title: \"Company Employees\",\n                url: \"https://linkedin.com/search/results/people/?keywords=\".concat(encodeURIComponent(lead.company))\n            });\n        }\n        return links;\n    };\n    const handleImportLeads = ()=>{};\n    const convertApiLeadsToUI = (apiLeads)=>{\n        return apiLeads.map((apiLead)=>{\n            var _apiLead_apolloData, _apiLead_apolloData1, _apiLead_apolloData2, _apiLead_apolloData3, _apiLead_apolloData4, _apiLead_normalizedData, _apiLead_normalizedData1;\n            // Debug logging for BBC News unlock issue\n            if (apiLead.name && apiLead.name.toLowerCase().includes(\"bbc\")) {\n                var _apiLead_normalizedData2, _apiLead_normalizedData3, _apiLead_normalizedData4, _apiLead_normalizedData5;\n                console.log(\"\\uD83D\\uDD0D [BBC DEBUG] Processing BBC lead:\", {\n                    id: apiLead.id,\n                    name: apiLead.name,\n                    hasNormalizedData: !!apiLead.normalizedData,\n                    hasApolloData: !!apiLead.apolloData,\n                    normalizedData: apiLead.normalizedData,\n                    isEmailVisible: (_apiLead_normalizedData2 = apiLead.normalizedData) === null || _apiLead_normalizedData2 === void 0 ? void 0 : _apiLead_normalizedData2.isEmailVisible,\n                    isPhoneVisible: (_apiLead_normalizedData3 = apiLead.normalizedData) === null || _apiLead_normalizedData3 === void 0 ? void 0 : _apiLead_normalizedData3.isPhoneVisible,\n                    email: (_apiLead_normalizedData4 = apiLead.normalizedData) === null || _apiLead_normalizedData4 === void 0 ? void 0 : _apiLead_normalizedData4.email,\n                    phone: (_apiLead_normalizedData5 = apiLead.normalizedData) === null || _apiLead_normalizedData5 === void 0 ? void 0 : _apiLead_normalizedData5.phone\n                });\n            }\n            // Detect if this is actually person data in a company lead\n            const isPersonData = apiLead.apolloData && (apiLead.apolloData.first_name || apiLead.apolloData.last_name || apiLead.apolloData.person || apiLead.apolloData.title);\n            console.log(\"\\uD83D\\uDD0D [CONVERT] Data type detection:\", {\n                isPersonData,\n                hasFirstName: !!((_apiLead_apolloData = apiLead.apolloData) === null || _apiLead_apolloData === void 0 ? void 0 : _apiLead_apolloData.first_name),\n                hasLastName: !!((_apiLead_apolloData1 = apiLead.apolloData) === null || _apiLead_apolloData1 === void 0 ? void 0 : _apiLead_apolloData1.last_name),\n                hasPerson: !!((_apiLead_apolloData2 = apiLead.apolloData) === null || _apiLead_apolloData2 === void 0 ? void 0 : _apiLead_apolloData2.person),\n                hasTitle: !!((_apiLead_apolloData3 = apiLead.apolloData) === null || _apiLead_apolloData3 === void 0 ? void 0 : _apiLead_apolloData3.title),\n                hasIndustry: !!((_apiLead_apolloData4 = apiLead.apolloData) === null || _apiLead_apolloData4 === void 0 ? void 0 : _apiLead_apolloData4.industry)\n            });\n            let industry = \"-\";\n            let location = \"-\";\n            let name = \"Unknown\";\n            let company = \"Unknown\";\n            if (isPersonData) {\n                var _apiLead_apolloData_organization, _apiLead_apolloData5, _apiLead_apolloData6, _apiLead_apolloData7, _apiLead_apolloData8, _apiLead_apolloData_organization1, _apiLead_apolloData9, _apiLead_apolloData_organization2, _apiLead_apolloData10;\n                // This is person data, extract accordingly\n                console.log(\"\\uD83D\\uDD0D [CONVERT] Processing as PERSON data in company lead\");\n                // For person data, industry comes from organization\n                industry = ((_apiLead_apolloData5 = apiLead.apolloData) === null || _apiLead_apolloData5 === void 0 ? void 0 : (_apiLead_apolloData_organization = _apiLead_apolloData5.organization) === null || _apiLead_apolloData_organization === void 0 ? void 0 : _apiLead_apolloData_organization.industry) || \"-\";\n                // For person data, location comes from person fields\n                if (apiLead.apolloData.city || apiLead.apolloData.state || apiLead.apolloData.country) {\n                    location = [\n                        apiLead.apolloData.city,\n                        apiLead.apolloData.state,\n                        apiLead.apolloData.country\n                    ].filter(Boolean).join(\", \") || \"-\";\n                }\n                // For person data, name is first + last name\n                const firstName = ((_apiLead_apolloData6 = apiLead.apolloData) === null || _apiLead_apolloData6 === void 0 ? void 0 : _apiLead_apolloData6.first_name) || \"\";\n                const lastName = ((_apiLead_apolloData7 = apiLead.apolloData) === null || _apiLead_apolloData7 === void 0 ? void 0 : _apiLead_apolloData7.last_name) || \"\";\n                name = \"\".concat(firstName, \" \").concat(lastName).trim() || ((_apiLead_apolloData8 = apiLead.apolloData) === null || _apiLead_apolloData8 === void 0 ? void 0 : _apiLead_apolloData8.name) || \"Unknown Person\";\n                // For person data, company comes from organization\n                company = ((_apiLead_apolloData9 = apiLead.apolloData) === null || _apiLead_apolloData9 === void 0 ? void 0 : (_apiLead_apolloData_organization1 = _apiLead_apolloData9.organization) === null || _apiLead_apolloData_organization1 === void 0 ? void 0 : _apiLead_apolloData_organization1.name) || ((_apiLead_apolloData10 = apiLead.apolloData) === null || _apiLead_apolloData10 === void 0 ? void 0 : (_apiLead_apolloData_organization2 = _apiLead_apolloData10.organization) === null || _apiLead_apolloData_organization2 === void 0 ? void 0 : _apiLead_apolloData_organization2.company_name) || \"Unknown Company\";\n            } else {\n                var _apiLead_apolloData11, _apiLead_normalizedData6, _apiLead_apolloData12, _apiLead_normalizedData7, _apiLead_apolloData13;\n                // This is actual company data\n                console.log(\"\\uD83D\\uDD0D [CONVERT] Processing as COMPANY data\");\n                // Extract industry from Apollo data\n                industry = ((_apiLead_apolloData11 = apiLead.apolloData) === null || _apiLead_apolloData11 === void 0 ? void 0 : _apiLead_apolloData11.industry) || \"-\";\n                // Extract location - companies might not have city/state/country in Apollo data\n                if (apiLead.apolloData) {\n                    const apolloData = apiLead.apolloData;\n                    // Check if location data exists in apolloData\n                    if (apolloData.city || apolloData.state || apolloData.country) {\n                        location = [\n                            apolloData.city,\n                            apolloData.state,\n                            apolloData.country\n                        ].filter(Boolean).join(\", \") || \"-\";\n                    } else if (apolloData.location) {\n                        // Check if location is in a nested object\n                        const loc = apolloData.location;\n                        if (typeof loc === \"object\" && loc !== null) {\n                            location = [\n                                loc.city,\n                                loc.state,\n                                loc.country\n                            ].filter(Boolean).join(\", \") || \"-\";\n                        }\n                    }\n                }\n                // Extract name with fallback\n                name = ((_apiLead_normalizedData6 = apiLead.normalizedData) === null || _apiLead_normalizedData6 === void 0 ? void 0 : _apiLead_normalizedData6.name) || ((_apiLead_apolloData12 = apiLead.apolloData) === null || _apiLead_apolloData12 === void 0 ? void 0 : _apiLead_apolloData12.name) || \"Unknown Company\";\n                // Extract company name with fallback\n                company = ((_apiLead_normalizedData7 = apiLead.normalizedData) === null || _apiLead_normalizedData7 === void 0 ? void 0 : _apiLead_normalizedData7.company) || ((_apiLead_apolloData13 = apiLead.apolloData) === null || _apiLead_apolloData13 === void 0 ? void 0 : _apiLead_apolloData13.name) || name;\n            }\n            console.log(\"\\uD83D\\uDD0D [CONVERT] Extracted data:\", {\n                name,\n                company,\n                industry,\n                location,\n                isPersonData\n            });\n            return {\n                id: apiLead.id,\n                name,\n                company,\n                email: ((_apiLead_normalizedData = apiLead.normalizedData) === null || _apiLead_normalizedData === void 0 ? void 0 : _apiLead_normalizedData.isEmailVisible) ? apiLead.normalizedData.email || \"unlock\" : \"unlock\",\n                phone: ((_apiLead_normalizedData1 = apiLead.normalizedData) === null || _apiLead_normalizedData1 === void 0 ? void 0 : _apiLead_normalizedData1.isPhoneVisible) ? apiLead.normalizedData.phone || \"unlock\" : \"unlock\",\n                links: \"view\",\n                industry,\n                location\n            };\n        });\n    };\n    // Shared filtered leads logic (company version)\n    const getFilteredLeads = (leads, searchQuery, filter)=>{\n        _s();\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n            var _filter_conditions;\n            let filtered = leads;\n            // Apply search filter if user is searching locally\n            if (searchQuery === null || searchQuery === void 0 ? void 0 : searchQuery.trim()) {\n                const searchTerm = searchQuery.trim().toLowerCase();\n                filtered = filtered.filter((lead)=>Object.values(lead).some((value)=>typeof value === \"string\" && value.toLowerCase().includes(searchTerm)));\n            }\n            // Apply filter conditions (if any filters are set)\n            if ((filter === null || filter === void 0 ? void 0 : (_filter_conditions = filter.conditions) === null || _filter_conditions === void 0 ? void 0 : _filter_conditions.length) > 0) {\n                filtered = filtered.filter((lead)=>{\n                    return filter.conditions.every((condition)=>{\n                        var _condition_value, _lead_condition_columnId;\n                        const value = ((_condition_value = condition.value) === null || _condition_value === void 0 ? void 0 : _condition_value.toString().toLowerCase()) || \"\";\n                        const leadValue = ((_lead_condition_columnId = lead[condition.columnId]) === null || _lead_condition_columnId === void 0 ? void 0 : _lead_condition_columnId.toString().toLowerCase()) || \"\";\n                        return leadValue.includes(value);\n                    });\n                });\n            }\n            return filtered;\n        }, [\n            leads,\n            searchQuery,\n            filter\n        ]);\n    };\n    _s(getFilteredLeads, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\n    const handleSendEmail = async (leadId, leadData)=>{\n        var _lead_normalizedData, _lead_apolloData, _lead_normalizedData1, _lead_apolloData_normalizedData, _lead_apolloData1;\n        console.log(\"\\uD83D\\uDD0D [INDEX] handleSendEmail called\");\n        console.log(\"\\uD83D\\uDD0D [INDEX] leadId:\", leadId);\n        console.log(\"\\uD83D\\uDD0D [INDEX] leadData:\", leadData);\n        // Use provided lead data or create a basic object\n        const lead = leadData || {\n            id: leadId,\n            name: \"Unknown Lead\"\n        };\n        console.log(\"\\uD83D\\uDD0D [INDEX] lead object:\", lead);\n        // Check if email is unlocked and visible\n        const email = ((_lead_normalizedData = lead.normalizedData) === null || _lead_normalizedData === void 0 ? void 0 : _lead_normalizedData.email) || ((_lead_apolloData = lead.apolloData) === null || _lead_apolloData === void 0 ? void 0 : _lead_apolloData.email) || lead.email;\n        const isEmailVisible = ((_lead_normalizedData1 = lead.normalizedData) === null || _lead_normalizedData1 === void 0 ? void 0 : _lead_normalizedData1.isEmailVisible) || ((_lead_apolloData1 = lead.apolloData) === null || _lead_apolloData1 === void 0 ? void 0 : (_lead_apolloData_normalizedData = _lead_apolloData1.normalizedData) === null || _lead_apolloData_normalizedData === void 0 ? void 0 : _lead_apolloData_normalizedData.isEmailVisible);\n        console.log(\"\\uD83D\\uDD0D [INDEX] email found:\", email);\n        console.log(\"\\uD83D\\uDD0D [INDEX] isEmailVisible:\", isEmailVisible);\n        if (!email || !isEmailVisible) {\n            console.log(\"\\uD83D\\uDD0D [INDEX] Email not unlocked, showing error toast\");\n            toast.error(\"You have to unlock the email first before sending an email.\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [INDEX] Email found, opening modal\");\n        setSelectedLeadForEmail({\n            ...lead,\n            email\n        });\n        setEmailSubject(\"\");\n        setEmailBody(\"\");\n        setSendEmailDialogOpen(true);\n        console.log(\"\\uD83D\\uDD0D [INDEX] Modal state set to true\");\n    };\n    const handleConfirmSendEmail = async ()=>{\n        if (!selectedLeadForEmail || !emailSubject.trim() || !emailBody.trim()) {\n            toast.error(\"Please fill in both subject and body\");\n            return;\n        }\n        setSendingEmail(true);\n        try {\n            var _workspace_workspace;\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_17__.sendEmailToLead)((token === null || token === void 0 ? void 0 : token.token) || \"\", (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || \"\", selectedLeadForEmail.id, {\n                subject: emailSubject.trim(),\n                body: emailBody.trim()\n            });\n            // Check if the API call was actually successful\n            if (response.isSuccess) {\n                toast.success(\"Email sent successfully!\");\n                setSendEmailDialogOpen(false);\n                setEmailSubject(\"\");\n                setEmailBody(\"\");\n                setSelectedLeadForEmail(null);\n            } else {\n                toast.error(response.error || \"Failed to send email\");\n            }\n        } catch (error) {\n            console.error(\"Failed to send email:\", error);\n            toast.error(\"Failed to send email\");\n        } finally{\n            setSendingEmail(false);\n        }\n    };\n    const handleAddToSegments = async (leadId)=>{\n        setSelectedLeadIdForAction(leadId);\n        setSegmentName(\"\");\n        setAddToSegmentDialogOpen(true);\n    };\n    const handleConfirmAddToSegment = async ()=>{\n        if (!selectedLeadIdForAction || !segmentName.trim()) {\n            toast.error(\"Please enter a segment name\");\n            return;\n        }\n        setAddingToSegment(true);\n        try {\n            var _workspace_workspace;\n            await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_17__.addLeadToSegment)((token === null || token === void 0 ? void 0 : token.token) || \"\", (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || \"\", selectedLeadIdForAction, {\n                name: segmentName.trim()\n            });\n            toast.success(\"Lead added to segment successfully!\");\n            setAddToSegmentDialogOpen(false);\n            setSegmentName(\"\");\n            setSelectedLeadIdForAction(\"\");\n        } catch (error) {\n            console.error(\"Failed to add to segment:\", error);\n            toast.error(\"Failed to add to segment\");\n        } finally{\n            setAddingToSegment(false);\n        }\n    };\n    const handleAddToDatabase = async (leadId, leadData)=>{\n        console.log(\"\\uD83D\\uDD0D handleAddToDatabase called with leadId:\", leadId);\n        // Use provided lead data or create a basic object\n        const lead = leadData || {\n            id: leadId,\n            name: \"Unknown Company\"\n        };\n        setSelectedLeadForDatabase(lead);\n        setAddToDatabaseDialogOpen(true);\n    };\n    const handleConfirmAddToDatabase = async (mappings, databaseId)=>{\n        if (!selectedLeadForDatabase || !databaseId || mappings.length === 0) return;\n        setAddingToDatabase(true);\n        try {\n            var _workspace_workspace;\n            // For now, use the existing API with just databaseId\n            // TODO: Update API to support field mappings\n            const response = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_17__.addLeadToDatabase)((token === null || token === void 0 ? void 0 : token.token) || \"\", (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || \"\", selectedLeadForDatabase.id, {\n                targetDatabaseId: databaseId\n            });\n            if (response.isSuccess) {\n                toast.success(\"Lead added to database successfully!\");\n                setAddToDatabaseDialogOpen(false);\n                setSelectedLeadForDatabase(null);\n            } else {\n                toast.error(response.error || \"Failed to add lead to database\");\n            }\n        } catch (error) {\n            console.error(\"Failed to add lead to database:\", error);\n            toast.error(\"Failed to add lead to database\");\n        } finally{\n            setAddingToDatabase(false);\n        }\n    };\n    const handleAddToWorkflow = async (leadId)=>{\n        console.log(\"\\uD83D\\uDD0D [FRONTEND] handleAddToWorkflow called with leadId:\", leadId);\n        setSelectedLeadIdForAction(leadId);\n        // Load workflows when dialog opens\n        setLoadingWorkflows(true);\n        try {\n            var _workspace_workspace, _response_data_data, _response_data;\n            console.log(\"\\uD83D\\uDD0D [FRONTEND] Loading real workflows from API...\");\n            const response = await (0,_ui_api_workflow__WEBPACK_IMPORTED_MODULE_18__.getWorkflows)((token === null || token === void 0 ? void 0 : token.token) || \"\", (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id) || \"\");\n            if (response.isSuccess && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.workflows)) {\n                // Filter for OnDemand_Callable workflows that can be manually triggered\n                const onDemandWorkflows = response.data.data.workflows.filter((w)=>w.triggerType === _repo_app_db_utils_dist_typings_workflow__WEBPACK_IMPORTED_MODULE_19__.WorkflowTriggerType.OnDemand_Callable);\n                console.log(\"\\uD83D\\uDD0D [FRONTEND] Found OnDemand workflows:\", onDemandWorkflows.map((w)=>({\n                        id: w.id,\n                        name: w.name\n                    })));\n                if (onDemandWorkflows.length > 0) {\n                    setAvailableWorkflows(onDemandWorkflows);\n                } else {\n                    // If no OnDemand workflows, add default option\n                    setAvailableWorkflows([\n                        {\n                            id: \"default-workflow\",\n                            name: \"Default Lead Workflow\"\n                        }\n                    ]);\n                }\n            } else {\n                console.error(\"\\uD83D\\uDD0D [FRONTEND] Failed to load workflows:\", response.error);\n                setAvailableWorkflows([\n                    {\n                        id: \"default-workflow\",\n                        name: \"Default Lead Workflow\"\n                    }\n                ]);\n            }\n            console.log(\"\\uD83D\\uDD0D [FRONTEND] Loaded workflows successfully\");\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [FRONTEND] Failed to load workflows:\", error);\n            setAvailableWorkflows([\n                {\n                    id: \"default-workflow\",\n                    name: \"Default Lead Workflow\"\n                }\n            ]);\n        } finally{\n            setLoadingWorkflows(false);\n        }\n        setAddToWorkflowDialogOpen(true);\n    };\n    const handleConfirmAddToWorkflow = async ()=>{\n        var _workspace_workspace;\n        console.log(\"\\uD83D\\uDD0D [FRONTEND] handleConfirmAddToWorkflow called\");\n        console.log(\"\\uD83D\\uDD0D [FRONTEND] selectedLeadIdForAction:\", selectedLeadIdForAction);\n        console.log(\"\\uD83D\\uDD0D [FRONTEND] selectedWorkflowId:\", selectedWorkflowId);\n        console.log(\"\\uD83D\\uDD0D [FRONTEND] token:\", (token === null || token === void 0 ? void 0 : token.token) ? \"exists\" : \"missing\");\n        console.log(\"\\uD83D\\uDD0D [FRONTEND] workspaceId:\", workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id);\n        if (!selectedLeadIdForAction || !selectedWorkflowId) {\n            console.log(\"\\uD83D\\uDD0D [FRONTEND] ERROR: Missing leadId or workflowId\");\n            toast.error(\"Please select a workflow\");\n            return;\n        }\n        setAddingToWorkflow(true);\n        try {\n            var _workspace_workspace1;\n            console.log(\"\\uD83D\\uDD0D [FRONTEND] Calling addLeadToWorkflow API...\");\n            const result = await (0,_ui_api_leads__WEBPACK_IMPORTED_MODULE_17__.addLeadToWorkflow)((token === null || token === void 0 ? void 0 : token.token) || \"\", (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace1 = workspace.workspace) === null || _workspace_workspace1 === void 0 ? void 0 : _workspace_workspace1.id) || \"\", selectedLeadIdForAction, {\n                workflowId: selectedWorkflowId\n            });\n            console.log(\"\\uD83D\\uDD0D [FRONTEND] addLeadToWorkflow API result:\", result);\n            // Check if the API call was actually successful\n            if (result.isSuccess) {\n                toast.success(\"Lead added to workflow successfully!\");\n                setAddToWorkflowDialogOpen(false);\n                setSelectedWorkflowId(\"\");\n                setSelectedLeadIdForAction(\"\");\n            } else {\n                toast.error(result.error || \"Failed to add lead to workflow\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [FRONTEND] ERROR in addLeadToWorkflow:\", error);\n            console.error(\"\\uD83D\\uDD0D [FRONTEND] Error details:\", {\n                message: error.message,\n                status: error.status,\n                response: error.response\n            });\n            toast.error(\"Failed to add to workflow\");\n        } finally{\n            setAddingToWorkflow(false);\n        }\n    };\n    const AddToWorkflowDialog = (param)=>{\n        let { open, onOpenChange, selectedWorkflowId, setSelectedWorkflowId, addingToWorkflow, loadingWorkflows, availableWorkflows, handleConfirmAddToWorkflow } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n            open: open,\n            onOpenChange: onOpenChange,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                className: \"sm:max-w-[425px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                children: \"Add Lead to Workflow\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogDescription, {\n                                children: \"Select a workflow to add this lead to.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-4\",\n                        children: loadingWorkflows ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Loading workflows...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 29\n                        }, undefined) : availableWorkflows.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground mb-2\",\n                                    children: \"No workflows available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Please create a workflow first.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 29\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Select Workflow:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWorkflowId,\n                                        onChange: (e)=>setSelectedWorkflowId(e.target.value),\n                                        disabled: loadingWorkflows,\n                                        className: \"mt-1 w-full p-2 border border-gray-300 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Choose a workflow...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            availableWorkflows.map((workflow)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: workflow.id,\n                                                    children: workflow.name\n                                                }, workflow.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 45\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 728,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>onOpenChange(false),\n                                disabled: addingToWorkflow,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 762,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: handleConfirmAddToWorkflow,\n                                disabled: !selectedWorkflowId || addingToWorkflow || loadingWorkflows,\n                                className: \"gap-2\",\n                                children: addingToWorkflow ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_25__.Loader, {\n                                            theme: \"dark\",\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        \"Adding...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_10__.CodeMergeIcon, {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        \"Add to Workflow\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 769,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 761,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 721,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n            lineNumber: 720,\n            columnNumber: 13\n        }, undefined);\n    };\n    // Send Email Dialog Component\n    const SendEmailDialog = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n            open: sendEmailDialogOpen,\n            onOpenChange: setSendEmailDialogOpen,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                className: \"sm:max-w-[600px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                            children: \"Send Email to Lead\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 798,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-4 space-y-4\",\n                        children: [\n                            selectedLeadForEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-gray-50 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"To:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            \" \",\n                                            selectedLeadForEmail.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    selectedLeadForEmail.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                                lineNumber: 809,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            \" \",\n                                            selectedLeadForEmail.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 803,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                                        htmlFor: \"email-subject\",\n                                        children: \"Subject\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                        id: \"email-subject\",\n                                        value: emailSubject,\n                                        onChange: (e)=>setEmailSubject(e.target.value),\n                                        placeholder: \"Enter email subject\",\n                                        disabled: sendingEmail\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                                        htmlFor: \"email-body\",\n                                        children: \"Message\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__.Textarea, {\n                                        id: \"email-body\",\n                                        value: emailBody,\n                                        onChange: (e)=>setEmailBody(e.target.value),\n                                        placeholder: \"Enter your message here...\",\n                                        rows: 8,\n                                        disabled: sendingEmail\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 801,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setSendEmailDialogOpen(false),\n                                disabled: sendingEmail,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: handleConfirmSendEmail,\n                                disabled: !emailSubject.trim() || !emailBody.trim() || sendingEmail,\n                                children: sendingEmail ? \"Sending...\" : \"Send Email\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 838,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 797,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n            lineNumber: 796,\n            columnNumber: 13\n        }, undefined);\n    };\n    const AddToSegmentDialog = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n            open: addToSegmentDialogOpen,\n            onOpenChange: setAddToSegmentDialogOpen,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                className: \"sm:max-w-[425px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                children: \"Add Lead to Segment\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogDescription, {\n                                children: \"Enter a name for the segment to add this lead to.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                                    htmlFor: \"segment-name\",\n                                    children: \"Segment Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 870,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                    id: \"segment-name\",\n                                    value: segmentName,\n                                    onChange: (e)=>setSegmentName(e.target.value),\n                                    placeholder: \"e.g., High Priority Leads, Q1 Prospects\",\n                                    disabled: addingToSegment\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                    lineNumber: 871,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 869,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 868,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setAddToSegmentDialogOpen(false),\n                                disabled: addingToSegment,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 881,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: handleConfirmAddToSegment,\n                                disabled: !segmentName.trim() || addingToSegment,\n                                className: \"gap-2\",\n                                children: addingToSegment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_25__.Loader, {\n                                            theme: \"dark\",\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        \"Adding...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_10__.ChartLineIcon, {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                            lineNumber: 900,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        \"Add to Segment\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                        lineNumber: 880,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                lineNumber: 861,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n            lineNumber: 860,\n            columnNumber: 13\n        }, undefined);\n    };\n    // Shared modals\n    const SharedModals = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_workspace_main_common_unlockEmail__WEBPACK_IMPORTED_MODULE_22__.UnlockEmailModal, {\n                    open: emailModalOpen,\n                    onOpenChange: setEmailModalOpen,\n                    leadId: selectedLeadId,\n                    onUnlockSuccess: (unlockedLead)=>{\n                        if (unlockEmailCallbackRef.current) {\n                            unlockEmailCallbackRef.current(unlockedLead);\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 914,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_components_workspace_main_common_unlockPhone__WEBPACK_IMPORTED_MODULE_23__.UnlockPhoneModal, {\n                    open: phoneModalOpen,\n                    onOpenChange: setPhoneModalOpen,\n                    leadId: selectedLeadId,\n                    onUnlockSuccess: (unlockedLead)=>{\n                        if (unlockPhoneCallbackRef.current) {\n                            unlockPhoneCallbackRef.current(unlockedLead);\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 924,\n                    columnNumber: 13\n                }, undefined),\n                selectedLeadForDatabase && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_TwoStepDatabaseMapping__WEBPACK_IMPORTED_MODULE_24__.TwoStepDatabaseMapping, {\n                    open: addToDatabaseDialogOpen,\n                    onOpenChange: setAddToDatabaseDialogOpen,\n                    lead: selectedLeadForDatabase,\n                    onConfirm: handleConfirmAddToDatabase,\n                    loading: addingToDatabase\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 935,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddToSegmentDialog, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 943,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddToWorkflowDialog, {\n                    open: addToWorkflowDialogOpen,\n                    onOpenChange: setAddToWorkflowDialogOpen,\n                    selectedWorkflowId: selectedWorkflowId,\n                    setSelectedWorkflowId: setSelectedWorkflowId,\n                    addingToWorkflow: addingToWorkflow,\n                    loadingWorkflows: loadingWorkflows,\n                    availableWorkflows: availableWorkflows,\n                    handleConfirmAddToWorkflow: handleConfirmAddToWorkflow\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 944,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SendEmailDialog, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                    lineNumber: 954,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true);\n    return {\n        // State\n        selectedLeads,\n        setSelectedLeads,\n        emailModalOpen,\n        phoneModalOpen,\n        selectedLeadId,\n        // Selection handlers\n        handleSelectAll,\n        handleSelectLead,\n        // Unlock handlers\n        handleUnlockEmail,\n        handleUnlockPhone,\n        // Navigation handlers\n        handleNameClick,\n        handleViewLinks,\n        // Contact links\n        getContactLinks,\n        // Import handler\n        handleImportLeads,\n        // API conversion\n        convertApiLeadsToUI,\n        // Filter logic\n        getFilteredLeads,\n        // Lead actions\n        handleSendEmail,\n        handleConfirmSendEmail,\n        handleAddToSegments,\n        handleAddToDatabase,\n        handleAddToWorkflow,\n        handleConfirmAddToDatabase,\n        // Database dialog state\n        addToDatabaseDialogOpen,\n        setAddToDatabaseDialogOpen,\n        selectedLeadForDatabase,\n        setSelectedLeadForDatabase,\n        addingToDatabase,\n        setAddingToDatabase,\n        selectedLeadIdForAction,\n        setSelectedLeadIdForAction,\n        // Email dialog state\n        sendEmailDialogOpen,\n        setSendEmailDialogOpen,\n        emailSubject,\n        setEmailSubject,\n        emailBody,\n        setEmailBody,\n        sendingEmail,\n        setSendingEmail,\n        selectedLeadForEmail,\n        setSelectedLeadForEmail,\n        // Segment dialog state\n        addToSegmentDialogOpen,\n        setAddToSegmentDialogOpen,\n        segmentName,\n        setSegmentName,\n        addingToSegment,\n        setAddingToSegment,\n        handleConfirmAddToSegment,\n        // Workflow dialog state\n        addToWorkflowDialogOpen,\n        setAddToWorkflowDialogOpen,\n        selectedWorkflowId,\n        setSelectedWorkflowId,\n        addingToWorkflow,\n        setAddingToWorkflow,\n        availableWorkflows,\n        setAvailableWorkflows,\n        loadingWorkflows,\n        setLoadingWorkflows,\n        handleConfirmAddToWorkflow,\n        // Callback setters\n        setUnlockEmailCallback: (callback)=>{\n            unlockEmailCallbackRef.current = callback;\n        },\n        setUnlockPhoneCallback: (callback)=>{\n            unlockPhoneCallbackRef.current = callback;\n        },\n        // Components\n        SharedModals,\n        CompanyTableHeader\n    };\n};\n_s1(useLeadManagement, \"1Zvxn7tjE0YJVv5EMoFXy79ZRco=\", false, function() {\n    return [\n        _ui_providers_alert__WEBPACK_IMPORTED_MODULE_20__.useAlert,\n        _ui_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_21__.useRouter,\n        _ui_context_routerContext__WEBPACK_IMPORTED_MODULE_21__.useParams\n    ];\n});\nconst Companies = (param)=>{\n    let { activeSubTab, onLeadCreated } = param;\n    var _workspace_workspace;\n    _s2();\n    const { workspace } = (0,_ui_providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace)();\n    const { token } = (0,_ui_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    // Shared sidebar state that persists across tab switches\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Centralized lead management and actions\n    const leadManagement = useLeadManagement();\n    // Create leadActions directly from leadManagement to avoid duplicate state\n    const leadActions = {\n        handleSendEmail: leadManagement.handleSendEmail,\n        handleAddToSegments: leadManagement.handleAddToSegments,\n        handleAddToDatabase: leadManagement.handleAddToDatabase,\n        handleAddToWorkflow: leadManagement.handleAddToWorkflow,\n        handleUnlockEmail: leadManagement.handleUnlockEmail,\n        handleUnlockPhone: leadManagement.handleUnlockPhone\n    };\n    const sidebarState = {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n    };\n    // Shared props for all sub-components\n    const sharedProps = {\n        onLeadCreated,\n        token: token === null || token === void 0 ? void 0 : token.token,\n        workspaceId: workspace === null || workspace === void 0 ? void 0 : (_workspace_workspace = workspace.workspace) === null || _workspace_workspace === void 0 ? void 0 : _workspace_workspace.id,\n        // Pass shared components and actions\n        ActionButton,\n        ViewLinksModal,\n        LeadActionsDropdown,\n        leadActions,\n        leadManagement\n    };\n    const renderContent = ()=>{\n        switch(activeSubTab){\n            case \"my-leads\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_leads__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            ...sharedProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 1101,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(leadManagement.SharedModals, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true);\n            case \"find-leads\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_find_leads__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            ...sharedProps,\n                            sidebarState: sidebarState\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 1108,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(leadManagement.SharedModals, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 1109,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true);\n            case \"saved-search\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_saved_search__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            ...sharedProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 1115,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(leadManagement.SharedModals, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 1116,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_leads__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            ...sharedProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 1122,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(leadManagement.SharedModals, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\opendashboard-mono\\\\packages\\\\ui\\\\src\\\\components\\\\workspace\\\\main\\\\lead-generation\\\\company\\\\index.tsx\",\n                            lineNumber: 1123,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true);\n        }\n    };\n    return renderContent();\n};\n_s2(Companies, \"FzGLT64w531tfbZL+Xh1ZDyoyeU=\", false, function() {\n    return [\n        _ui_providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace,\n        _ui_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        useLeadManagement\n    ];\n});\n_c4 = Companies;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Companies);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ActionButton\");\n$RefreshReg$(_c1, \"ViewLinksModal\");\n$RefreshReg$(_c2, \"LeadActionsDropdown\");\n$RefreshReg$(_c3, \"CompanyTableHeader\");\n$RefreshReg$(_c4, \"Companies\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS9zcmMvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi9sZWFkLWdlbmVyYXRpb24vY29tcGFueS9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0Q7QUFDeEI7QUFDSTtBQUNJO0FBQ0k7QUFDVTtBQUNMO0FBQ2tDO0FBQzBEO0FBQ0o7QUFDYjtBQUNyQjtBQUNsRDtBQUNOO0FBQ007QUFDTjtBQUNvRTtBQUVwRTtBQUMrQjtBQUNoQztBQUNrQjtBQUNtQjtBQUNBO0FBR1Y7QUFDakI7QUFRakQsTUFBTWlELGVBQWU7UUFBQyxFQUN6QkMsTUFBTUMsSUFBSSxFQUNWQyxRQUFRLEVBQ1JDLE9BQU8sRUFLVjt5QkFDRyw4REFBQzVDLDREQUFNQTtRQUNINkMsU0FBUTtRQUNSQyxNQUFLO1FBQ0xGLFNBQVNBO1FBQ1RHLFdBQVU7OzBCQUVWLDhEQUFDTDtnQkFBS0ssV0FBVTs7Ozs7OzBCQUNoQiw4REFBQ0M7Z0JBQUtELFdBQVU7MEJBQVlKOzs7Ozs7Ozs7Ozs7RUFFbkM7S0FsQllIO0FBb0JOLE1BQU1TLGlCQUFpQjtRQUFDLEVBQzNCQyxPQUFPLEVBQ1BDLEtBQUssRUFJUjs7SUFDRyxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBRzdELCtDQUFRQSxDQUFDO0lBRXJDLHFCQUNJLDhEQUFDUyw4REFBT0E7UUFBQ3FELE1BQU1GO1FBQVFHLGNBQWNGOzswQkFDakMsOERBQUNsRCxxRUFBY0E7Z0JBQUNxRCxPQUFPOzBCQUNsQk47Ozs7OzswQkFFTCw4REFBQ2hELHFFQUFjQTtnQkFBQzZDLFdBQVU7Z0JBQVdVLE9BQU07O2tDQUN2Qyw4REFBQ0M7d0JBQUdYLFdBQVU7a0NBQTZCOzs7Ozs7a0NBQzNDLDhEQUFDWTt3QkFBSVosV0FBVTtrQ0FDVkksTUFBTVMsTUFBTSxLQUFLLGtCQUNkLDhEQUFDRDs0QkFBSVosV0FBVTtzQ0FBZ0M7Ozs7O3dDQUUvQ0ksTUFBTVUsR0FBRyxDQUFDLENBQUNDLHFCQUNQLDhEQUFDQztnQ0FFR0MsTUFBTUYsS0FBS0csR0FBRztnQ0FDZEMsUUFBTztnQ0FDUEMsS0FBSTtnQ0FDSnBCLFdBQVU7O2tEQUVWLDhEQUFDbEMsMkZBQXFCQTt3Q0FBQ2tDLFdBQVU7Ozs7OztrREFDakMsOERBQUNDO3dDQUFLRCxXQUFVO2tEQUFZZSxLQUFLTSxLQUFLOzs7Ozs7OytCQVBqQ04sS0FBS08sRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWU1QyxFQUFDO0dBckNZcEI7TUFBQUE7QUFnRE4sTUFBTXFCLHNCQUFzQjtRQUFDLEVBQ2hDcEIsT0FBTyxFQUNQcUIsV0FBVyxFQUNYQyxlQUFlLEVBQ2ZDLGVBQWUsRUFDZkMsZUFBZSxFQUNmQyxRQUFRLEVBQ2U7UUFFVEEsMEJBQW1DQTtJQUFqRCxNQUFNQyxRQUFRRCxDQUFBQSxxQkFBQUEsZ0NBQUFBLDJCQUFBQSxTQUFVRSxjQUFjLGNBQXhCRiwrQ0FBQUEseUJBQTBCQyxLQUFLLE1BQUlELHFCQUFBQSxnQ0FBQUEsdUJBQUFBLFNBQVVHLFVBQVUsY0FBcEJILDJDQUFBQSxxQkFBc0JDLEtBQUssTUFBSUQscUJBQUFBLCtCQUFBQSxTQUFVQyxLQUFLO0lBQy9GLE1BQU1HLFdBQVcsQ0FBQyxDQUFDSDtJQUVuQixxQkFDSSw4REFBQ3hFLHlFQUFZQTs7MEJBQ1QsOERBQUNHLGdGQUFtQkE7Z0JBQUNpRCxPQUFPOzBCQUN2Qk47Ozs7OzswQkFFTCw4REFBQzdDLGdGQUFtQkE7Z0JBQ2hCMEMsV0FBVTtnQkFDVlUsT0FBTTtnQkFDTnVCLFlBQVk7MEJBRVosNEVBQUN4RSw4RUFBaUJBO29CQUFDdUMsV0FBVTs7c0NBQ3pCLDhEQUFDekMsNkVBQWdCQTs0QkFDYnlDLFdBQVcsMkRBQXlILE9BQTlEZ0MsV0FBVyxtQkFBbUI7NEJBQ3BHbkMsU0FBU21DLFdBQVdSLGNBQWNVOzRCQUNsQ0MsVUFBVSxDQUFDSDs7OENBRVgsOERBQUN0RSxrRkFBWUE7b0NBQUNzQyxXQUFVOzs7Ozs7OENBQ3hCLDhEQUFDQzs4Q0FBSzs7Ozs7O2dDQUNMLENBQUMrQiwwQkFBWSw4REFBQy9CO29DQUFLRCxXQUFVOzhDQUFnQzs7Ozs7Ozs7Ozs7O3NDQUdsRSw4REFBQ3pDLDZFQUFnQkE7NEJBQ2J5QyxXQUFVOzRCQUNWSCxTQUFTNEI7OzhDQUVULDhEQUFDOUQsbUZBQWFBO29DQUFDcUMsV0FBVTs7Ozs7OzhDQUN6Qiw4REFBQ0M7OENBQUs7Ozs7Ozs7Ozs7OztzQ0FHViw4REFBQzFDLDZFQUFnQkE7NEJBQ2J5QyxXQUFVOzRCQUNWSCxTQUFTNkI7OzhDQUVULDhEQUFDOUQsa0ZBQVlBO29DQUFDb0MsV0FBVTs7Ozs7OzhDQUN4Qiw4REFBQ0M7OENBQUs7Ozs7Ozs7Ozs7OztzQ0FHViw4REFBQzFDLDZFQUFnQkE7NEJBQ2J5QyxXQUFVOzRCQUNWSCxTQUFTOEI7OzhDQUVULDhEQUFDOUQsbUZBQWFBO29DQUFDbUMsV0FBVTs7Ozs7OzhDQUN6Qiw4REFBQ0M7OENBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTlCLEVBQUM7TUE1RFlzQjtBQStETixNQUFNYSxxQkFBcUI7UUFBQyxFQUMvQkMsYUFBYSxFQUNiQyxhQUFhLEVBQ2JDLGVBQWUsRUFLbEI7eUJBQ0csOERBQUNqRSxpRUFBV0E7a0JBQ1IsNEVBQUNDLDhEQUFRQTtZQUFDeUIsV0FBVTs7OEJBQ2hCLDhEQUFDM0IsK0RBQVNBO29CQUFDMkIsV0FBVTs4QkFDakIsNEVBQUN4QixpRUFBUUE7d0JBQ0xnRSxTQUFTSCxjQUFjeEIsTUFBTSxLQUFLeUIsY0FBY3pCLE1BQU0sSUFBSXlCLGNBQWN6QixNQUFNLEdBQUc7d0JBQ2pGNEIsaUJBQWlCLENBQUNELFVBQXFCRCxnQkFBZ0JDLFNBQVNGOzs7Ozs7Ozs7Ozs4QkFHeEUsOERBQUNqRSwrREFBU0E7b0JBQUMyQixXQUFVOzhCQUEyQzs7Ozs7OzhCQUNoRSw4REFBQzNCLCtEQUFTQTtvQkFBQzJCLFdBQVU7OEJBQTJDOzs7Ozs7OEJBQ2hFLDhEQUFDM0IsK0RBQVNBO29CQUFDMkIsV0FBVTs4QkFBMkM7Ozs7Ozs4QkFDaEUsOERBQUMzQiwrREFBU0E7b0JBQUMyQixXQUFVOzhCQUEyQzs7Ozs7OzhCQUNoRSw4REFBQzNCLCtEQUFTQTtvQkFBQzJCLFdBQVU7OEJBQTJDOzs7Ozs7OEJBQ2hFLDhEQUFDM0IsK0RBQVNBO29CQUFDMkIsV0FBVTs4QkFBMkM7Ozs7Ozs4QkFDaEUsOERBQUMzQiwrREFBU0E7b0JBQUMyQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztFQUdoQztNQTFCWW9DO0FBNkJOLE1BQU1NLG9CQUFvQjs7O0lBQzdCLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUd6RCw4REFBUUE7SUFDMUIsTUFBTSxFQUFFMEQsS0FBSyxFQUFFLEdBQUc3RiwyREFBT0E7SUFDekIsTUFBTSxFQUFFOEYsU0FBUyxFQUFFLEdBQUc3RixxRUFBWUE7SUFDbEMsTUFBTThGLFNBQVMzRCxxRUFBU0E7SUFDeEIsTUFBTTRELFNBQVMzRCxxRUFBU0E7SUFHeEIsTUFBTSxDQUFDaUQsZUFBZVcsaUJBQWlCLEdBQUd2RywrQ0FBUUEsQ0FBVyxFQUFFO0lBQy9ELE1BQU0sQ0FBQ3dHLGdCQUFnQkMsa0JBQWtCLEdBQUd6RywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUMwRyxnQkFBZ0JDLGtCQUFrQixHQUFHM0csK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDNEcsZ0JBQWdCQyxrQkFBa0IsR0FBRzdHLCtDQUFRQSxDQUFnQjtJQUdwRSxNQUFNOEcseUJBQXlCN0csNkNBQU1BLENBQStCO0lBQ3BFLE1BQU04Ryx5QkFBeUI5Ryw2Q0FBTUEsQ0FBK0I7SUFHcEUsTUFBTSxDQUFDK0cseUJBQXlCQywyQkFBMkIsR0FBR2pILCtDQUFRQSxDQUFDO0lBQ3ZFLE1BQU0sQ0FBQ2tILHlCQUF5QkMsMkJBQTJCLEdBQUduSCwrQ0FBUUEsQ0FBTTtJQUM1RSxNQUFNLENBQUNvSCxrQkFBa0JDLG9CQUFvQixHQUFHckgsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDc0gseUJBQXlCQywyQkFBMkIsR0FBR3ZILCtDQUFRQSxDQUFnQjtJQUd0RixNQUFNLENBQUN3SCxxQkFBcUJDLHVCQUF1QixHQUFHekgsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDMEgsY0FBY0MsZ0JBQWdCLEdBQUczSCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUM0SCxXQUFXQyxhQUFhLEdBQUc3SCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUM4SCxjQUFjQyxnQkFBZ0IsR0FBRy9ILCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2dJLHNCQUFzQkMsd0JBQXdCLEdBQUdqSSwrQ0FBUUEsQ0FBTTtJQUd0RSxNQUFNLENBQUNrSSx3QkFBd0JDLDBCQUEwQixHQUFHbkksK0NBQVFBLENBQUM7SUFDckUsTUFBTSxDQUFDb0ksYUFBYUMsZUFBZSxHQUFHckksK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDc0ksaUJBQWlCQyxtQkFBbUIsR0FBR3ZJLCtDQUFRQSxDQUFDO0lBR3ZELE1BQU0sQ0FBQ3dJLHlCQUF5QkMsMkJBQTJCLEdBQUd6SSwrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUMwSSxvQkFBb0JDLHNCQUFzQixHQUFHM0ksK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDNEksa0JBQWtCQyxvQkFBb0IsR0FBRzdJLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzhJLG9CQUFvQkMsc0JBQXNCLEdBQUcvSSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3RFLE1BQU0sQ0FBQ2dKLGtCQUFrQkMsb0JBQW9CLEdBQUdqSiwrQ0FBUUEsQ0FBQztJQUd6RCxNQUFNOEYsa0JBQWtCLENBQUNDLFNBQWtCbUQ7UUFDdkMsSUFBSW5ELFNBQVM7WUFDVFEsaUJBQWlCMkMsTUFBTTdFLEdBQUcsQ0FBQzhFLENBQUFBLE9BQVFBLEtBQUt0RSxFQUFFO1FBQzlDLE9BQU87WUFDSDBCLGlCQUFpQixFQUFFO1FBQ3ZCO0lBQ0o7SUFFQSxNQUFNNkMsbUJBQW1CLENBQUNDLFFBQWdCdEQ7UUFDdEMsSUFBSUEsU0FBUztZQUNUUSxpQkFBaUI7bUJBQUlYO2dCQUFleUQ7YUFBTztRQUMvQyxPQUFPO1lBQ0g5QyxpQkFBaUJYLGNBQWMwRCxNQUFNLENBQUN6RSxDQUFBQSxLQUFNQSxPQUFPd0U7UUFDdkQ7SUFDSjtJQUdBLE1BQU1FLG9CQUFvQixDQUFDRjtRQUN2QnhDLGtCQUFrQndDO1FBQ2xCNUMsa0JBQWtCO0lBQ3RCO0lBRUEsTUFBTStDLG9CQUFvQixDQUFDSDtRQUN2QnhDLGtCQUFrQndDO1FBQ2xCMUMsa0JBQWtCO0lBQ3RCO0lBR0EsTUFBTThDLGtCQUFrQixDQUFDTjtRQUNyQixNQUFNTyxTQUFTcEQsT0FBT29ELE1BQU07UUFDNUJyRCxPQUFPc0QsSUFBSSxDQUFDLElBQThDUixPQUExQ08sUUFBTyxxQ0FBMkMsT0FBUlAsS0FBS3RFLEVBQUU7SUFDckU7SUFFQSxNQUFNK0Usa0JBQWtCLENBQUNQLFVBRXpCO0lBR0EsTUFBTVEsa0JBQWtCLENBQUNWO1FBQ3JCLE1BQU14RixRQUFRLEVBQUU7UUFHaEIsSUFBSXdGLEtBQUtXLElBQUksRUFBRTtZQUNYbkcsTUFBTWdHLElBQUksQ0FBQztnQkFDUDlFLElBQUk7Z0JBQ0pELE9BQU87Z0JBQ1BILEtBQUssd0RBQTJHLE9BQW5Ec0YsbUJBQW1CWixLQUFLVyxJQUFJLEdBQUcsTUFBTVgsS0FBS2EsT0FBTztZQUNsSDtRQUNKO1FBR0EsSUFBSWIsS0FBS2EsT0FBTyxFQUFFO1lBQ2RyRyxNQUFNZ0csSUFBSSxDQUFDO2dCQUNQOUUsSUFBSTtnQkFDSkQsT0FBTztnQkFDUEgsS0FBSyxtQ0FBMEYsT0FBdkRzRixtQkFBbUJaLEtBQUthLE9BQU8sR0FBRztZQUM5RTtRQUNKO1FBR0EsSUFBSWIsS0FBS2EsT0FBTyxFQUFFO1lBQ2RyRyxNQUFNZ0csSUFBSSxDQUFDO2dCQUNQOUUsSUFBSTtnQkFDSkQsT0FBTztnQkFDUEgsS0FBSywyREFBNEYsT0FBakNzRixtQkFBbUJaLEtBQUthLE9BQU87WUFDbkc7UUFDSjtRQUdBLElBQUliLEtBQUthLE9BQU8sRUFBRTtZQUNkckcsTUFBTWdHLElBQUksQ0FBQztnQkFDUDlFLElBQUk7Z0JBQ0pELE9BQU87Z0JBQ1BILEtBQUssbUNBQW9FLE9BQWpDc0YsbUJBQW1CWixLQUFLYSxPQUFPO1lBQzNFO1FBQ0o7UUFHQSxJQUFJYixLQUFLYSxPQUFPLEVBQUU7WUFDZHJHLE1BQU1nRyxJQUFJLENBQUM7Z0JBQ1A5RSxJQUFJO2dCQUNKRCxPQUFPO2dCQUNQSCxLQUFLLHdEQUF5RixPQUFqQ3NGLG1CQUFtQlosS0FBS2EsT0FBTztZQUNoRztRQUNKO1FBRUEsT0FBT3JHO0lBQ1g7SUFHQSxNQUFNc0csb0JBQW9CLEtBRTFCO0lBSUEsTUFBTUMsc0JBQXNCLENBQUNDO1FBR3pCLE9BQU9BLFNBQVM5RixHQUFHLENBQUMsQ0FBQytGO2dCQTBCR0EscUJBQ0RBLHNCQUNGQSxzQkFDREEsc0JBQ0dBLHNCQTJFUkEseUJBQ0FBO1lBekdYLDBDQUEwQztZQUMxQyxJQUFJQSxRQUFRTixJQUFJLElBQUlNLFFBQVFOLElBQUksQ0FBQ08sV0FBVyxHQUFHQyxRQUFRLENBQUMsUUFBUTtvQkFPeENGLDBCQUNBQSwwQkFDVEEsMEJBQ0FBO2dCQVRYRyxRQUFRQyxHQUFHLENBQUMsaURBQXVDO29CQUMvQzNGLElBQUl1RixRQUFRdkYsRUFBRTtvQkFDZGlGLE1BQU1NLFFBQVFOLElBQUk7b0JBQ2xCVyxtQkFBbUIsQ0FBQyxDQUFDTCxRQUFRL0UsY0FBYztvQkFDM0NxRixlQUFlLENBQUMsQ0FBQ04sUUFBUTlFLFVBQVU7b0JBQ25DRCxnQkFBZ0IrRSxRQUFRL0UsY0FBYztvQkFDdENzRixjQUFjLEdBQUVQLDJCQUFBQSxRQUFRL0UsY0FBYyxjQUF0QitFLCtDQUFBQSx5QkFBd0JPLGNBQWM7b0JBQ3REQyxjQUFjLEdBQUVSLDJCQUFBQSxRQUFRL0UsY0FBYyxjQUF0QitFLCtDQUFBQSx5QkFBd0JRLGNBQWM7b0JBQ3REeEYsS0FBSyxHQUFFZ0YsMkJBQUFBLFFBQVEvRSxjQUFjLGNBQXRCK0UsK0NBQUFBLHlCQUF3QmhGLEtBQUs7b0JBQ3BDeUYsS0FBSyxHQUFFVCwyQkFBQUEsUUFBUS9FLGNBQWMsY0FBdEIrRSwrQ0FBQUEseUJBQXdCUyxLQUFLO2dCQUN4QztZQUNKO1lBRUEsMkRBQTJEO1lBQzNELE1BQU1DLGVBQWVWLFFBQVE5RSxVQUFVLElBQ25DOEUsQ0FBQUEsUUFBUTlFLFVBQVUsQ0FBQ3lGLFVBQVUsSUFDN0JYLFFBQVE5RSxVQUFVLENBQUMwRixTQUFTLElBQzVCWixRQUFROUUsVUFBVSxDQUFDMkYsTUFBTSxJQUN6QmIsUUFBUTlFLFVBQVUsQ0FBQ1YsS0FBSztZQUc1QjJGLFFBQVFDLEdBQUcsQ0FBQywrQ0FBcUM7Z0JBQzdDTTtnQkFDQUksY0FBYyxDQUFDLEdBQUNkLHNCQUFBQSxRQUFROUUsVUFBVSxjQUFsQjhFLDBDQUFBQSxvQkFBb0JXLFVBQVU7Z0JBQzlDSSxhQUFhLENBQUMsR0FBQ2YsdUJBQUFBLFFBQVE5RSxVQUFVLGNBQWxCOEUsMkNBQUFBLHFCQUFvQlksU0FBUztnQkFDNUNJLFdBQVcsQ0FBQyxHQUFDaEIsdUJBQUFBLFFBQVE5RSxVQUFVLGNBQWxCOEUsMkNBQUFBLHFCQUFvQmEsTUFBTTtnQkFDdkNJLFVBQVUsQ0FBQyxHQUFDakIsdUJBQUFBLFFBQVE5RSxVQUFVLGNBQWxCOEUsMkNBQUFBLHFCQUFvQnhGLEtBQUs7Z0JBQ3JDMEcsYUFBYSxDQUFDLEdBQUNsQix1QkFBQUEsUUFBUTlFLFVBQVUsY0FBbEI4RSwyQ0FBQUEscUJBQW9CbUIsUUFBUTtZQUMvQztZQUVBLElBQUlBLFdBQVc7WUFDZixJQUFJQyxXQUFXO1lBQ2YsSUFBSTFCLE9BQU87WUFDWCxJQUFJRSxVQUFVO1lBRWQsSUFBSWMsY0FBYztvQkFLSFYsa0NBQUFBLHNCQVVPQSxzQkFDREEsc0JBQzJCQSxzQkFHbENBLG1DQUFBQSxzQkFBMENBLG1DQUFBQTtnQkFuQnBELDJDQUEyQztnQkFDM0NHLFFBQVFDLEdBQUcsQ0FBQztnQkFFWixvREFBb0Q7Z0JBQ3BEZSxXQUFXbkIsRUFBQUEsdUJBQUFBLFFBQVE5RSxVQUFVLGNBQWxCOEUsNENBQUFBLG1DQUFBQSxxQkFBb0JxQixZQUFZLGNBQWhDckIsdURBQUFBLGlDQUFrQ21CLFFBQVEsS0FBSTtnQkFFekQscURBQXFEO2dCQUNyRCxJQUFJbkIsUUFBUTlFLFVBQVUsQ0FBQ29HLElBQUksSUFBSXRCLFFBQVE5RSxVQUFVLENBQUNxRyxLQUFLLElBQUl2QixRQUFROUUsVUFBVSxDQUFDc0csT0FBTyxFQUFFO29CQUNuRkosV0FBVzt3QkFBQ3BCLFFBQVE5RSxVQUFVLENBQUNvRyxJQUFJO3dCQUFFdEIsUUFBUTlFLFVBQVUsQ0FBQ3FHLEtBQUs7d0JBQUV2QixRQUFROUUsVUFBVSxDQUFDc0csT0FBTztxQkFBQyxDQUNyRnRDLE1BQU0sQ0FBQ3VDLFNBQ1BDLElBQUksQ0FBQyxTQUFTO2dCQUN2QjtnQkFFQSw2Q0FBNkM7Z0JBQzdDLE1BQU1DLFlBQVkzQixFQUFBQSx1QkFBQUEsUUFBUTlFLFVBQVUsY0FBbEI4RSwyQ0FBQUEscUJBQW9CVyxVQUFVLEtBQUk7Z0JBQ3BELE1BQU1pQixXQUFXNUIsRUFBQUEsdUJBQUFBLFFBQVE5RSxVQUFVLGNBQWxCOEUsMkNBQUFBLHFCQUFvQlksU0FBUyxLQUFJO2dCQUNsRGxCLE9BQU8sR0FBZ0JrQyxPQUFiRCxXQUFVLEtBQVksT0FBVEMsVUFBV0MsSUFBSSxRQUFNN0IsdUJBQUFBLFFBQVE5RSxVQUFVLGNBQWxCOEUsMkNBQUFBLHFCQUFvQk4sSUFBSSxLQUFJO2dCQUV4RSxtREFBbUQ7Z0JBQ25ERSxVQUFVSSxFQUFBQSx1QkFBQUEsUUFBUTlFLFVBQVUsY0FBbEI4RSw0Q0FBQUEsb0NBQUFBLHFCQUFvQnFCLFlBQVksY0FBaENyQix3REFBQUEsa0NBQWtDTixJQUFJLE9BQUlNLHdCQUFBQSxRQUFROUUsVUFBVSxjQUFsQjhFLDZDQUFBQSxvQ0FBQUEsc0JBQW9CcUIsWUFBWSxjQUFoQ3JCLHdEQUFBQSxrQ0FBa0M4QixZQUFZLEtBQUk7WUFFMUcsT0FBTztvQkFLUTlCLHVCQXNCSkEsMEJBQWdDQSx1QkFHN0JBLDBCQUFtQ0E7Z0JBN0I3Qyw4QkFBOEI7Z0JBQzlCRyxRQUFRQyxHQUFHLENBQUM7Z0JBRVosb0NBQW9DO2dCQUNwQ2UsV0FBV25CLEVBQUFBLHdCQUFBQSxRQUFROUUsVUFBVSxjQUFsQjhFLDRDQUFBQSxzQkFBb0JtQixRQUFRLEtBQUk7Z0JBRTNDLGdGQUFnRjtnQkFDaEYsSUFBSW5CLFFBQVE5RSxVQUFVLEVBQUU7b0JBQ3BCLE1BQU1BLGFBQWE4RSxRQUFROUUsVUFBVTtvQkFDckMsOENBQThDO29CQUM5QyxJQUFJQSxXQUFXb0csSUFBSSxJQUFJcEcsV0FBV3FHLEtBQUssSUFBSXJHLFdBQVdzRyxPQUFPLEVBQUU7d0JBQzNESixXQUFXOzRCQUFDbEcsV0FBV29HLElBQUk7NEJBQUVwRyxXQUFXcUcsS0FBSzs0QkFBRXJHLFdBQVdzRyxPQUFPO3lCQUFDLENBQzdEdEMsTUFBTSxDQUFDdUMsU0FDUEMsSUFBSSxDQUFDLFNBQVM7b0JBQ3ZCLE9BQU8sSUFBSXhHLFdBQVdrRyxRQUFRLEVBQUU7d0JBQzVCLDBDQUEwQzt3QkFDMUMsTUFBTVcsTUFBTTdHLFdBQVdrRyxRQUFRO3dCQUMvQixJQUFJLE9BQU9XLFFBQVEsWUFBWUEsUUFBUSxNQUFNOzRCQUN6Q1gsV0FBVztnQ0FBQ1csSUFBSVQsSUFBSTtnQ0FBRVMsSUFBSVIsS0FBSztnQ0FBRVEsSUFBSVAsT0FBTzs2QkFBQyxDQUN4Q3RDLE1BQU0sQ0FBQ3VDLFNBQ1BDLElBQUksQ0FBQyxTQUFTO3dCQUN2QjtvQkFDSjtnQkFDSjtnQkFFQSw2QkFBNkI7Z0JBQzdCaEMsT0FBT00sRUFBQUEsMkJBQUFBLFFBQVEvRSxjQUFjLGNBQXRCK0UsK0NBQUFBLHlCQUF3Qk4sSUFBSSxPQUFJTSx3QkFBQUEsUUFBUTlFLFVBQVUsY0FBbEI4RSw0Q0FBQUEsc0JBQW9CTixJQUFJLEtBQUk7Z0JBRW5FLHFDQUFxQztnQkFDckNFLFVBQVVJLEVBQUFBLDJCQUFBQSxRQUFRL0UsY0FBYyxjQUF0QitFLCtDQUFBQSx5QkFBd0JKLE9BQU8sT0FBSUksd0JBQUFBLFFBQVE5RSxVQUFVLGNBQWxCOEUsNENBQUFBLHNCQUFvQk4sSUFBSSxLQUFJQTtZQUM3RTtZQUVBUyxRQUFRQyxHQUFHLENBQUMsMENBQWdDO2dCQUN4Q1Y7Z0JBQ0FFO2dCQUNBdUI7Z0JBQ0FDO2dCQUNBVjtZQUNKO1lBRUEsT0FBTztnQkFDSGpHLElBQUl1RixRQUFRdkYsRUFBRTtnQkFDZGlGO2dCQUNBRTtnQkFDQTVFLE9BQU9nRixFQUFBQSwwQkFBQUEsUUFBUS9FLGNBQWMsY0FBdEIrRSw4Q0FBQUEsd0JBQXdCTyxjQUFjLElBQUdQLFFBQVEvRSxjQUFjLENBQUNELEtBQUssSUFBSSxXQUFXO2dCQUMzRnlGLE9BQU9ULEVBQUFBLDJCQUFBQSxRQUFRL0UsY0FBYyxjQUF0QitFLCtDQUFBQSx5QkFBd0JRLGNBQWMsSUFBR1IsUUFBUS9FLGNBQWMsQ0FBQ3dGLEtBQUssSUFBSSxXQUFXO2dCQUMzRmxILE9BQU87Z0JBQ1A0SDtnQkFDQUM7WUFDSjtRQUNKO0lBQ0o7SUFFQSxnREFBZ0Q7SUFDaEQsTUFBTVksbUJBQW1CLENBQUNsRCxPQUFjbUQsYUFBc0IvQzs7UUFDMUQsT0FBT3BKLDhDQUFPQSxDQUFDO2dCQWNQb0o7WUFiSixJQUFJZ0QsV0FBV3BEO1lBRWYsbURBQW1EO1lBQ25ELElBQUltRCx3QkFBQUEsa0NBQUFBLFlBQWFKLElBQUksSUFBSTtnQkFDckIsTUFBTU0sYUFBYUYsWUFBWUosSUFBSSxHQUFHNUIsV0FBVztnQkFDakRpQyxXQUFXQSxTQUFTaEQsTUFBTSxDQUFDSCxDQUFBQSxPQUN2QnFELE9BQU9DLE1BQU0sQ0FBQ3RELE1BQU11RCxJQUFJLENBQUNDLENBQUFBLFFBQ3JCLE9BQU9BLFVBQVUsWUFBWUEsTUFBTXRDLFdBQVcsR0FBR0MsUUFBUSxDQUFDaUM7WUFHdEU7WUFFQSxtREFBbUQ7WUFDbkQsSUFBSWpELENBQUFBLG1CQUFBQSw4QkFBQUEscUJBQUFBLE9BQVFzRCxVQUFVLGNBQWxCdEQseUNBQUFBLG1CQUFvQmxGLE1BQU0sSUFBRyxHQUFHO2dCQUNoQ2tJLFdBQVdBLFNBQVNoRCxNQUFNLENBQUNILENBQUFBO29CQUN2QixPQUFPRyxPQUFPc0QsVUFBVSxDQUFDQyxLQUFLLENBQUMsQ0FBQ0M7NEJBQ2RBLGtCQUNJM0Q7d0JBRGxCLE1BQU13RCxRQUFRRyxFQUFBQSxtQkFBQUEsVUFBVUgsS0FBSyxjQUFmRyx1Q0FBQUEsaUJBQWlCQyxRQUFRLEdBQUcxQyxXQUFXLE9BQU07d0JBQzNELE1BQU0yQyxZQUFZN0QsRUFBQUEsMkJBQUFBLElBQUksQ0FBQzJELFVBQVVHLFFBQVEsQ0FBYyxjQUFyQzlELCtDQUFBQSx5QkFBdUM0RCxRQUFRLEdBQUcxQyxXQUFXLE9BQU07d0JBQ3JGLE9BQU8yQyxVQUFVMUMsUUFBUSxDQUFDcUM7b0JBQzlCO2dCQUNKO1lBQ0o7WUFFQSxPQUFPTDtRQUNYLEdBQUc7WUFBQ3BEO1lBQU9tRDtZQUFhL0M7U0FBTztJQUNuQztPQTNCTThDO0lBNkJOLE1BQU1jLGtCQUFrQixPQUFPN0QsUUFBZ0JsRTtZQVU3QmdFLHNCQUE4QkEsa0JBQ3JCQSx1QkFBdUNBLGlDQUFBQTtRQVY5RG9CLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsZ0NBQXNCbkI7UUFDbENrQixRQUFRQyxHQUFHLENBQUMsa0NBQXdCckY7UUFFcEMsa0RBQWtEO1FBQ2xELE1BQU1nRSxPQUFPaEUsWUFBWTtZQUFFTixJQUFJd0U7WUFBUVMsTUFBTTtRQUFlO1FBQzVEUyxRQUFRQyxHQUFHLENBQUMscUNBQTJCckI7UUFFdkMseUNBQXlDO1FBQ3pDLE1BQU0vRCxRQUFRK0QsRUFBQUEsdUJBQUFBLEtBQUs5RCxjQUFjLGNBQW5COEQsMkNBQUFBLHFCQUFxQi9ELEtBQUssT0FBSStELG1CQUFBQSxLQUFLN0QsVUFBVSxjQUFmNkQsdUNBQUFBLGlCQUFpQi9ELEtBQUssS0FBSStELEtBQUsvRCxLQUFLO1FBQ2hGLE1BQU11RixpQkFBaUJ4QixFQUFBQSx3QkFBQUEsS0FBSzlELGNBQWMsY0FBbkI4RCw0Q0FBQUEsc0JBQXFCd0IsY0FBYyxPQUFJeEIsb0JBQUFBLEtBQUs3RCxVQUFVLGNBQWY2RCx5Q0FBQUEsa0NBQUFBLGtCQUFpQjlELGNBQWMsY0FBL0I4RCxzREFBQUEsZ0NBQWlDd0IsY0FBYztRQUM3R0osUUFBUUMsR0FBRyxDQUFDLHFDQUEyQnBGO1FBQ3ZDbUYsUUFBUUMsR0FBRyxDQUFDLHdDQUE4Qkc7UUFFMUMsSUFBSSxDQUFDdkYsU0FBUyxDQUFDdUYsZ0JBQWdCO1lBQzNCSixRQUFRQyxHQUFHLENBQUM7WUFDWnRFLE1BQU1pSCxLQUFLLENBQUM7WUFDWjtRQUNKO1FBRUE1QyxRQUFRQyxHQUFHLENBQUM7UUFDWnZDLHdCQUF3QjtZQUFFLEdBQUdrQixJQUFJO1lBQUUvRDtRQUFNO1FBQ3pDdUMsZ0JBQWdCO1FBQ2hCRSxhQUFhO1FBQ2JKLHVCQUF1QjtRQUN2QjhDLFFBQVFDLEdBQUcsQ0FBQztJQUNoQjtJQUVBLE1BQU00Qyx5QkFBeUI7UUFDM0IsSUFBSSxDQUFDcEYsd0JBQXdCLENBQUNOLGFBQWF1RSxJQUFJLE1BQU0sQ0FBQ3JFLFVBQVVxRSxJQUFJLElBQUk7WUFDcEUvRixNQUFNaUgsS0FBSyxDQUFDO1lBQ1o7UUFDSjtRQUVBcEYsZ0JBQWdCO1FBQ2hCLElBQUk7Z0JBQzJEM0I7WUFBM0QsTUFBTWlILFdBQVcsTUFBTS9LLCtEQUFlQSxDQUFDNkQsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPQSxLQUFLLEtBQUksSUFBSUMsQ0FBQUEsc0JBQUFBLGlDQUFBQSx1QkFBQUEsVUFBV0EsU0FBUyxjQUFwQkEsMkNBQUFBLHFCQUFzQnZCLEVBQUUsS0FBSSxJQUFJbUQscUJBQXFCbkQsRUFBRSxFQUFFO2dCQUNoSHlJLFNBQVM1RixhQUFhdUUsSUFBSTtnQkFDMUJzQixNQUFNM0YsVUFBVXFFLElBQUk7WUFDeEI7WUFFQSxnREFBZ0Q7WUFDaEQsSUFBSW9CLFNBQVNHLFNBQVMsRUFBRTtnQkFDcEJ0SCxNQUFNdUgsT0FBTyxDQUFDO2dCQUNkaEcsdUJBQXVCO2dCQUN2QkUsZ0JBQWdCO2dCQUNoQkUsYUFBYTtnQkFDYkksd0JBQXdCO1lBQzVCLE9BQU87Z0JBQ0gvQixNQUFNaUgsS0FBSyxDQUFDRSxTQUFTRixLQUFLLElBQUk7WUFDbEM7UUFDSixFQUFFLE9BQU9BLE9BQU87WUFDWjVDLFFBQVE0QyxLQUFLLENBQUMseUJBQXlCQTtZQUN2Q2pILE1BQU1pSCxLQUFLLENBQUM7UUFDaEIsU0FBVTtZQUNOcEYsZ0JBQWdCO1FBQ3BCO0lBQ0o7SUFFQSxNQUFNMkYsc0JBQXNCLE9BQU9yRTtRQUMvQjlCLDJCQUEyQjhCO1FBQzNCaEIsZUFBZTtRQUNmRiwwQkFBMEI7SUFDOUI7SUFFQSxNQUFNd0YsNEJBQTRCO1FBQzlCLElBQUksQ0FBQ3JHLDJCQUEyQixDQUFDYyxZQUFZNkQsSUFBSSxJQUFJO1lBQ2pEL0YsTUFBTWlILEtBQUssQ0FBQztZQUNaO1FBQ0o7UUFFQTVFLG1CQUFtQjtRQUNuQixJQUFJO2dCQUMyQ25DO1lBQTNDLE1BQU1oRSxnRUFBZ0JBLENBQUMrRCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9BLEtBQUssS0FBSSxJQUFJQyxDQUFBQSxzQkFBQUEsaUNBQUFBLHVCQUFBQSxVQUFXQSxTQUFTLGNBQXBCQSwyQ0FBQUEscUJBQXNCdkIsRUFBRSxLQUFJLElBQUl5Qyx5QkFBeUI7Z0JBQ2hHd0MsTUFBTTFCLFlBQVk2RCxJQUFJO1lBQzFCO1lBQ0EvRixNQUFNdUgsT0FBTyxDQUFDO1lBQ2R0RiwwQkFBMEI7WUFDMUJFLGVBQWU7WUFDZmQsMkJBQTJCO1FBQy9CLEVBQUUsT0FBTzRGLE9BQU87WUFDWjVDLFFBQVE0QyxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQ2pILE1BQU1pSCxLQUFLLENBQUM7UUFDaEIsU0FBVTtZQUNONUUsbUJBQW1CO1FBQ3ZCO0lBQ0o7SUFFQSxNQUFNcUYsc0JBQXNCLE9BQU92RSxRQUFnQmxFO1FBQy9Db0YsUUFBUUMsR0FBRyxDQUFDLHdEQUE4Q25CO1FBRTFELGtEQUFrRDtRQUNsRCxNQUFNRixPQUFPaEUsWUFBWTtZQUFFTixJQUFJd0U7WUFBUVMsTUFBTTtRQUFrQjtRQUMvRDNDLDJCQUEyQmdDO1FBQzNCbEMsMkJBQTJCO0lBQy9CO0lBSUEsTUFBTTRHLDZCQUE2QixPQUFPQyxVQUFpQkM7UUFDdkQsSUFBSSxDQUFDN0csMkJBQTJCLENBQUM2RyxjQUFjRCxTQUFTMUosTUFBTSxLQUFLLEdBQUc7UUFFdEVpRCxvQkFBb0I7UUFDcEIsSUFBSTtnQkFLSWpCO1lBSkoscURBQXFEO1lBQ3JELDZDQUE2QztZQUM3QyxNQUFNaUgsV0FBVyxNQUFNbEwsaUVBQWlCQSxDQUNwQ2dFLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT0EsS0FBSyxLQUFJLElBQ2hCQyxDQUFBQSxzQkFBQUEsaUNBQUFBLHVCQUFBQSxVQUFXQSxTQUFTLGNBQXBCQSwyQ0FBQUEscUJBQXNCdkIsRUFBRSxLQUFJLElBQzVCcUMsd0JBQXdCckMsRUFBRSxFQUMxQjtnQkFDSW1KLGtCQUFrQkQ7WUFDdEI7WUFHSixJQUFJVixTQUFTRyxTQUFTLEVBQUU7Z0JBQ3BCdEgsTUFBTXVILE9BQU8sQ0FBQztnQkFDZHhHLDJCQUEyQjtnQkFDM0JFLDJCQUEyQjtZQUMvQixPQUFPO2dCQUNIakIsTUFBTWlILEtBQUssQ0FBQ0UsU0FBU0YsS0FBSyxJQUFJO1lBQ2xDO1FBQ0osRUFBRSxPQUFPQSxPQUFPO1lBQ1o1QyxRQUFRNEMsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakRqSCxNQUFNaUgsS0FBSyxDQUFDO1FBQ2hCLFNBQVU7WUFDTjlGLG9CQUFvQjtRQUN4QjtJQUNKO0lBRUEsTUFBTTRHLHNCQUFzQixPQUFPNUU7UUFDL0JrQixRQUFRQyxHQUFHLENBQUMsbUVBQXlEbkI7UUFDckU5QiwyQkFBMkI4QjtRQUUzQixtQ0FBbUM7UUFDbkNKLG9CQUFvQjtRQUNwQixJQUFJO2dCQUV3RDdDLHNCQUU5QmlILHFCQUFBQTtZQUgxQjlDLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU02QyxXQUFXLE1BQU05SywrREFBWUEsQ0FBQzRELENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT0EsS0FBSyxLQUFJLElBQUlDLENBQUFBLHNCQUFBQSxpQ0FBQUEsdUJBQUFBLFVBQVdBLFNBQVMsY0FBcEJBLDJDQUFBQSxxQkFBc0J2QixFQUFFLEtBQUk7WUFFcEYsSUFBSXdJLFNBQVNHLFNBQVMsTUFBSUgsaUJBQUFBLFNBQVNhLElBQUksY0FBYmIsc0NBQUFBLHNCQUFBQSxlQUFlYSxJQUFJLGNBQW5CYiwwQ0FBQUEsb0JBQXFCYyxTQUFTLEdBQUU7Z0JBQ3RELHdFQUF3RTtnQkFDeEUsTUFBTUMsb0JBQW9CZixTQUFTYSxJQUFJLENBQUNBLElBQUksQ0FBQ0MsU0FBUyxDQUFDN0UsTUFBTSxDQUFDK0UsQ0FBQUEsSUFBS0EsRUFBRUMsV0FBVyxLQUFLOUwsMEZBQW1CQSxDQUFDK0wsaUJBQWlCO2dCQUMxSGhFLFFBQVFDLEdBQUcsQ0FBQyxxREFBMkM0RCxrQkFBa0IvSixHQUFHLENBQUNnSyxDQUFBQSxJQUFNO3dCQUFFeEosSUFBSXdKLEVBQUV4SixFQUFFO3dCQUFFaUYsTUFBTXVFLEVBQUV2RSxJQUFJO29CQUFDO2dCQUU1RyxJQUFJc0Usa0JBQWtCaEssTUFBTSxHQUFHLEdBQUc7b0JBQzlCMkUsc0JBQXNCcUY7Z0JBQzFCLE9BQU87b0JBQ0gsK0NBQStDO29CQUMvQ3JGLHNCQUFzQjt3QkFDbEI7NEJBQUVsRSxJQUFJOzRCQUFvQmlGLE1BQU07d0JBQXdCO3FCQUMzRDtnQkFDTDtZQUNKLE9BQU87Z0JBQ0hTLFFBQVE0QyxLQUFLLENBQUMscURBQTJDRSxTQUFTRixLQUFLO2dCQUN2RXBFLHNCQUFzQjtvQkFDbEI7d0JBQUVsRSxJQUFJO3dCQUFvQmlGLE1BQU07b0JBQXdCO2lCQUMzRDtZQUNMO1lBQ0FTLFFBQVFDLEdBQUcsQ0FBQztRQUNoQixFQUFFLE9BQU8yQyxPQUFPO1lBQ1o1QyxRQUFRNEMsS0FBSyxDQUFDLHFEQUEyQ0E7WUFDekRwRSxzQkFBc0I7Z0JBQ2xCO29CQUFFbEUsSUFBSTtvQkFBb0JpRixNQUFNO2dCQUF3QjthQUMzRDtRQUNMLFNBQVU7WUFDTmIsb0JBQW9CO1FBQ3hCO1FBRUFSLDJCQUEyQjtJQUMvQjtJQUVBLE1BQU0rRiw2QkFBNkI7WUFLV3BJO1FBSjFDbUUsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxvREFBMENsRDtRQUN0RGlELFFBQVFDLEdBQUcsQ0FBQywrQ0FBcUM5QjtRQUNqRDZCLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBd0JyRSxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9BLEtBQUssSUFBRyxXQUFXO1FBQzlEb0UsUUFBUUMsR0FBRyxDQUFDLHdDQUE4QnBFLHNCQUFBQSxpQ0FBQUEsdUJBQUFBLFVBQVdBLFNBQVMsY0FBcEJBLDJDQUFBQSxxQkFBc0J2QixFQUFFO1FBRWxFLElBQUksQ0FBQ3lDLDJCQUEyQixDQUFDb0Isb0JBQW9CO1lBQ2pENkIsUUFBUUMsR0FBRyxDQUFDO1lBQ1p0RSxNQUFNaUgsS0FBSyxDQUFDO1lBQ1o7UUFDSjtRQUVBdEUsb0JBQW9CO1FBQ3BCLElBQUk7Z0JBRTJEekM7WUFEM0RtRSxRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNaUUsU0FBUyxNQUFNcE0saUVBQWlCQSxDQUFDOEQsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPQSxLQUFLLEtBQUksSUFBSUMsQ0FBQUEsc0JBQUFBLGlDQUFBQSx3QkFBQUEsVUFBV0EsU0FBUyxjQUFwQkEsNENBQUFBLHNCQUFzQnZCLEVBQUUsS0FBSSxJQUFJeUMseUJBQXlCO2dCQUNoSG9ILFlBQVloRztZQUNoQjtZQUNBNkIsUUFBUUMsR0FBRyxDQUFDLHlEQUErQ2lFO1lBRTNELGdEQUFnRDtZQUNoRCxJQUFJQSxPQUFPakIsU0FBUyxFQUFFO2dCQUNsQnRILE1BQU11SCxPQUFPLENBQUM7Z0JBQ2RoRiwyQkFBMkI7Z0JBQzNCRSxzQkFBc0I7Z0JBQ3RCcEIsMkJBQTJCO1lBQy9CLE9BQU87Z0JBQ0hyQixNQUFNaUgsS0FBSyxDQUFDc0IsT0FBT3RCLEtBQUssSUFBSTtZQUNoQztRQUNKLEVBQUUsT0FBT0EsT0FBTztZQUNaNUMsUUFBUTRDLEtBQUssQ0FBQyx1REFBNkNBO1lBQzNENUMsUUFBUTRDLEtBQUssQ0FBQywwQ0FBZ0M7Z0JBQzFDd0IsU0FBU3hCLE1BQU13QixPQUFPO2dCQUN0QkMsUUFBUXpCLE1BQU15QixNQUFNO2dCQUNwQnZCLFVBQVVGLE1BQU1FLFFBQVE7WUFDNUI7WUFDQW5ILE1BQU1pSCxLQUFLLENBQUM7UUFDaEIsU0FBVTtZQUNOdEUsb0JBQW9CO1FBQ3hCO0lBQ0o7SUFFQSxNQUFNZ0csc0JBQXNCO1lBQUMsRUFDekIvSyxJQUFJLEVBQ0pDLFlBQVksRUFDWjJFLGtCQUFrQixFQUNsQkMscUJBQXFCLEVBQ3JCQyxnQkFBZ0IsRUFDaEJJLGdCQUFnQixFQUNoQkYsa0JBQWtCLEVBQ2xCMEYsMEJBQTBCLEVBVTdCO1FBQ0cscUJBQ0ksOERBQUNsTiw2REFBTUE7WUFBQ3dDLE1BQU1BO1lBQU1DLGNBQWNBO3NCQUM5Qiw0RUFBQ3hDLG9FQUFhQTtnQkFBQ2dDLFdBQVU7O2tDQUNyQiw4REFBQy9CLG1FQUFZQTs7MENBQ1QsOERBQUNDLGtFQUFXQTswQ0FBQzs7Ozs7OzBDQUNiLDhEQUFDQyx3RUFBaUJBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBSXZCLDhEQUFDeUM7d0JBQUlaLFdBQVU7a0NBQ1Z5RixpQ0FDRyw4REFBQzdFOzRCQUFJWixXQUFVO3NDQUNYLDRFQUFDWTtnQ0FBSVosV0FBVTswQ0FBZ0M7Ozs7Ozs7Ozs7d0NBRW5EdUYsbUJBQW1CMUUsTUFBTSxLQUFLLGtCQUM5Qiw4REFBQ0Q7NEJBQUlaLFdBQVU7OzhDQUNYLDhEQUFDWTtvQ0FBSVosV0FBVTs4Q0FBcUM7Ozs7Ozs4Q0FDcEQsOERBQUNZO29DQUFJWixXQUFVOzhDQUFnQzs7Ozs7Ozs7Ozs7c0RBS25ELDhEQUFDWTs0QkFBSVosV0FBVTtzQ0FDWCw0RUFBQ1k7O2tEQUNHLDhEQUFDMks7d0NBQU12TCxXQUFVO2tEQUFzQjs7Ozs7O2tEQUN2Qyw4REFBQ3dMO3dDQUNHcEMsT0FBT2pFO3dDQUNQc0csVUFBVSxDQUFDQyxJQUFNdEcsc0JBQXNCc0csRUFBRXZLLE1BQU0sQ0FBQ2lJLEtBQUs7d0NBQ3JEakgsVUFBVXNEO3dDQUNWekYsV0FBVTs7MERBRVYsOERBQUMyTDtnREFBT3ZDLE9BQU07MERBQUc7Ozs7Ozs0Q0FDaEI3RCxtQkFBbUJ6RSxHQUFHLENBQUMsQ0FBQzhLLHlCQUNyQiw4REFBQ0Q7b0RBQXlCdkMsT0FBT3dDLFNBQVN0SyxFQUFFOzhEQUN2Q3NLLFNBQVNyRixJQUFJO21EQURMcUYsU0FBU3RLLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTaEQsOERBQUNsRCxtRUFBWUE7OzBDQUNULDhEQUFDbkIsNERBQU1BO2dDQUNINkMsU0FBUTtnQ0FDUkQsU0FBUyxJQUFNVyxhQUFhO2dDQUM1QjJCLFVBQVVrRDswQ0FDYjs7Ozs7OzBDQUdELDhEQUFDcEksNERBQU1BO2dDQUNINEMsU0FBU29MO2dDQUNUOUksVUFBVSxDQUFDZ0Qsc0JBQXNCRSxvQkFBb0JJO2dDQUNyRHpGLFdBQVU7MENBRVRxRixpQ0FDRzs7c0RBQ0ksOERBQUM3RixvRUFBTUE7NENBQUNxTSxPQUFNOzRDQUFPN0wsV0FBVTs7Ozs7O3dDQUFXOztpRUFJOUM7O3NEQUNJLDhEQUFDbkMsbUZBQWFBOzRDQUFDbUMsV0FBVTs7Ozs7O3dDQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBU3BFO0lBR0EsOEJBQThCO0lBQzlCLE1BQU04TCxrQkFBa0I7UUFDcEIscUJBQ0ksOERBQUMvTiw2REFBTUE7WUFBQ3dDLE1BQU0wRDtZQUFxQnpELGNBQWMwRDtzQkFDN0MsNEVBQUNsRyxvRUFBYUE7Z0JBQUNnQyxXQUFVOztrQ0FDckIsOERBQUMvQixtRUFBWUE7a0NBQ1QsNEVBQUNDLGtFQUFXQTtzQ0FBQzs7Ozs7Ozs7Ozs7a0NBRWpCLDhEQUFDMEM7d0JBQUlaLFdBQVU7OzRCQUNWeUUsc0NBQ0csOERBQUM3RDtnQ0FBSVosV0FBVTs7a0RBQ1gsOERBQUMrTDt3Q0FBRS9MLFdBQVU7OzBEQUNULDhEQUFDQztnREFBS0QsV0FBVTswREFBYzs7Ozs7OzRDQUFVOzRDQUFFeUUscUJBQXFCOEIsSUFBSTs7Ozs7OztvQ0FFdEU5QixxQkFBcUI1QyxLQUFLLGtCQUN2Qiw4REFBQ2tLO3dDQUFFL0wsV0FBVTs7MERBQ1QsOERBQUNDO2dEQUFLRCxXQUFVOzBEQUFjOzs7Ozs7NENBQWE7NENBQUV5RSxxQkFBcUI1QyxLQUFLOzs7Ozs7Ozs7Ozs7OzBDQU12Riw4REFBQ2pCO2dDQUFJWixXQUFVOztrREFDWCw4REFBQ3JCLDJEQUFLQTt3Q0FBQ3FOLFNBQVE7a0RBQWdCOzs7Ozs7a0RBQy9CLDhEQUFDdk4sMkRBQUtBO3dDQUNGNkMsSUFBRzt3Q0FDSDhILE9BQU9qRjt3Q0FDUHNILFVBQVUsQ0FBQ0MsSUFBTXRILGdCQUFnQnNILEVBQUV2SyxNQUFNLENBQUNpSSxLQUFLO3dDQUMvQzZDLGFBQVk7d0NBQ1o5SixVQUFVb0M7Ozs7Ozs7Ozs7OzswQ0FJbEIsOERBQUMzRDtnQ0FBSVosV0FBVTs7a0RBQ1gsOERBQUNyQiwyREFBS0E7d0NBQUNxTixTQUFRO2tEQUFhOzs7Ozs7a0RBQzVCLDhEQUFDdE4saUVBQVFBO3dDQUNMNEMsSUFBRzt3Q0FDSDhILE9BQU8vRTt3Q0FDUG9ILFVBQVUsQ0FBQ0MsSUFBTXBILGFBQWFvSCxFQUFFdkssTUFBTSxDQUFDaUksS0FBSzt3Q0FDNUM2QyxhQUFZO3dDQUNaQyxNQUFNO3dDQUNOL0osVUFBVW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSXRCLDhEQUFDbkcsbUVBQVlBOzswQ0FDVCw4REFBQ25CLDREQUFNQTtnQ0FDSDZDLFNBQVE7Z0NBQ1JELFNBQVMsSUFBTXFFLHVCQUF1QjtnQ0FDdEMvQixVQUFVb0M7MENBQ2I7Ozs7OzswQ0FHRCw4REFBQ3RILDREQUFNQTtnQ0FDSDRDLFNBQVNnSztnQ0FDVDFILFVBQVUsQ0FBQ2dDLGFBQWF1RSxJQUFJLE1BQU0sQ0FBQ3JFLFVBQVVxRSxJQUFJLE1BQU1uRTswQ0FFdERBLGVBQWUsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNdkQ7SUFFQSxNQUFNNEgscUJBQXFCO1FBQ3ZCLHFCQUNJLDhEQUFDcE8sNkRBQU1BO1lBQUN3QyxNQUFNb0U7WUFBd0JuRSxjQUFjb0U7c0JBQ2hELDRFQUFDNUcsb0VBQWFBO2dCQUFDZ0MsV0FBVTs7a0NBQ3JCLDhEQUFDL0IsbUVBQVlBOzswQ0FDVCw4REFBQ0Msa0VBQVdBOzBDQUFDOzs7Ozs7MENBQ2IsOERBQUNDLHdFQUFpQkE7MENBQUM7Ozs7Ozs7Ozs7OztrQ0FJdkIsOERBQUN5Qzt3QkFBSVosV0FBVTtrQ0FDWCw0RUFBQ1k7NEJBQUlaLFdBQVU7OzhDQUNYLDhEQUFDckIsMkRBQUtBO29DQUFDcU4sU0FBUTs4Q0FBZTs7Ozs7OzhDQUM5Qiw4REFBQ3ZOLDJEQUFLQTtvQ0FDRjZDLElBQUc7b0NBQ0g4SCxPQUFPdkU7b0NBQ1A0RyxVQUFVLENBQUNDLElBQU01RyxlQUFlNEcsRUFBRXZLLE1BQU0sQ0FBQ2lJLEtBQUs7b0NBQzlDNkMsYUFBWTtvQ0FDWjlKLFVBQVU0Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSXRCLDhEQUFDM0csbUVBQVlBOzswQ0FDVCw4REFBQ25CLDREQUFNQTtnQ0FDSDZDLFNBQVE7Z0NBQ1JELFNBQVMsSUFBTStFLDBCQUEwQjtnQ0FDekN6QyxVQUFVNEM7MENBQ2I7Ozs7OzswQ0FHRCw4REFBQzlILDREQUFNQTtnQ0FDSDRDLFNBQVN1SztnQ0FDVGpJLFVBQVUsQ0FBQzBDLFlBQVk2RCxJQUFJLE1BQU0zRDtnQ0FDakMvRSxXQUFVOzBDQUVUK0UsZ0NBQ0c7O3NEQUNJLDhEQUFDdkYsb0VBQU1BOzRDQUFDcU0sT0FBTTs0Q0FBTzdMLFdBQVU7Ozs7Ozt3Q0FBVzs7aUVBSTlDOztzREFDSSw4REFBQ3JDLG1GQUFhQTs0Q0FBQ3FDLFdBQVU7Ozs7Ozt3Q0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVNwRTtJQUVBLGdCQUFnQjtJQUNoQixNQUFNb00sZUFBZSxrQkFDakI7OzhCQUNJLDhEQUFDL00sK0ZBQWdCQTtvQkFDYmtCLE1BQU0wQztvQkFDTnpDLGNBQWMwQztvQkFDZDRDLFFBQVF6QztvQkFDUmdKLGlCQUFpQixDQUFDQzt3QkFDZCxJQUFJL0ksdUJBQXVCZ0osT0FBTyxFQUFFOzRCQUNoQ2hKLHVCQUF1QmdKLE9BQU8sQ0FBQ0Q7d0JBQ25DO29CQUNKOzs7Ozs7OEJBRUosOERBQUNoTiwrRkFBZ0JBO29CQUNiaUIsTUFBTTRDO29CQUNOM0MsY0FBYzRDO29CQUNkMEMsUUFBUXpDO29CQUNSZ0osaUJBQWlCLENBQUNDO3dCQUNkLElBQUk5SSx1QkFBdUIrSSxPQUFPLEVBQUU7NEJBQ2hDL0ksdUJBQXVCK0ksT0FBTyxDQUFDRDt3QkFDbkM7b0JBQ0o7Ozs7OztnQkFFSDNJLHlDQUNHLDhEQUFDcEUsbUZBQXNCQTtvQkFDbkJnQixNQUFNa0Q7b0JBQ05qRCxjQUFja0Q7b0JBQ2RrQyxNQUFNakM7b0JBQ042SSxXQUFXbEM7b0JBQ1htQyxTQUFTNUk7Ozs7Ozs4QkFHakIsOERBQUNzSTs7Ozs7OEJBQ0QsOERBQUNiO29CQUNHL0ssTUFBTTBFO29CQUNOekUsY0FBYzBFO29CQUNkQyxvQkFBb0JBO29CQUNwQkMsdUJBQXVCQTtvQkFDdkJDLGtCQUFrQkE7b0JBQ2xCSSxrQkFBa0JBO29CQUNsQkYsb0JBQW9CQTtvQkFDcEIwRiw0QkFBNEJBOzs7Ozs7OEJBRWhDLDhEQUFDYTs7Ozs7OztJQUlULE9BQU87UUFDSCxRQUFRO1FBQ1J6SjtRQUNBVztRQUNBQztRQUNBRTtRQUNBRTtRQUVBLHFCQUFxQjtRQUNyQmQ7UUFDQXNEO1FBRUEsa0JBQWtCO1FBQ2xCRztRQUNBQztRQUVBLHNCQUFzQjtRQUN0QkM7UUFDQUc7UUFFQSxnQkFBZ0I7UUFDaEJDO1FBRUEsaUJBQWlCO1FBQ2pCSTtRQUVBLGlCQUFpQjtRQUNqQkM7UUFFQSxlQUFlO1FBQ2ZrQztRQUVBLGVBQWU7UUFDZmM7UUFDQUU7UUFDQU07UUFDQUU7UUFDQUs7UUFDQUo7UUFFQSx3QkFBd0I7UUFDeEI3RztRQUNBQztRQUNBQztRQUNBQztRQUNBQztRQUNBQztRQUNBQztRQUNBQztRQUVBLHFCQUFxQjtRQUNyQkM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFFQSx1QkFBdUI7UUFDdkJDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FvRjtRQUVBLHdCQUF3QjtRQUN4Qm5GO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0F1RjtRQUVBLG1CQUFtQjtRQUNuQnlCLHdCQUF3QixDQUFDQztZQUNyQnBKLHVCQUF1QmdKLE9BQU8sR0FBR0k7UUFDckM7UUFDQUMsd0JBQXdCLENBQUNEO1lBQ3JCbkosdUJBQXVCK0ksT0FBTyxHQUFHSTtRQUNyQztRQUVBLGFBQWE7UUFDYlA7UUFDQWhLO0lBQ0o7QUFDSixFQUFDO0lBeDFCWU07O1FBQ1N4RCwwREFBUUE7UUFDUm5DLHVEQUFPQTtRQUNIQyxpRUFBWUE7UUFDbkJtQyxpRUFBU0E7UUFDVEMsaUVBQVNBOzs7QUF1MUI1QixNQUFNeU4sWUFBWTtRQUFDLEVBQUVDLFlBQVksRUFBRUMsYUFBYSxFQUFrQjtRQTZCN0NsSzs7SUE1QmpCLE1BQU0sRUFBRUEsU0FBUyxFQUFFLEdBQUc3RixxRUFBWUE7SUFDbEMsTUFBTSxFQUFFNEYsS0FBSyxFQUFFLEdBQUc3RiwyREFBT0E7SUFFekIseURBQXlEO0lBQ3pELE1BQU0sQ0FBQ2lRLGFBQWFDLGVBQWUsR0FBR3hRLCtDQUFRQSxDQUFDO0lBRS9DLDBDQUEwQztJQUMxQyxNQUFNeVEsaUJBQWlCeEs7SUFFdkIsMkVBQTJFO0lBQzNFLE1BQU15SyxjQUFjO1FBQ2hCeEQsaUJBQWlCdUQsZUFBZXZELGVBQWU7UUFDL0NRLHFCQUFxQitDLGVBQWUvQyxtQkFBbUI7UUFDdkRFLHFCQUFxQjZDLGVBQWU3QyxtQkFBbUI7UUFDdkRLLHFCQUFxQndDLGVBQWV4QyxtQkFBbUI7UUFDdkQxRSxtQkFBbUJrSCxlQUFlbEgsaUJBQWlCO1FBQ25EQyxtQkFBbUJpSCxlQUFlakgsaUJBQWlCO0lBQ3ZEO0lBRUEsTUFBTW1ILGVBQWU7UUFDakIvTSxRQUFRMk07UUFDUjFNLFdBQVcyTTtJQUNmO0lBRUEsc0NBQXNDO0lBQ3RDLE1BQU1JLGNBQWM7UUFDaEJOO1FBQ0FuSyxLQUFLLEVBQUVBLGtCQUFBQSw0QkFBQUEsTUFBT0EsS0FBSztRQUNuQjBLLFdBQVcsRUFBRXpLLHNCQUFBQSxpQ0FBQUEsdUJBQUFBLFVBQVdBLFNBQVMsY0FBcEJBLDJDQUFBQSxxQkFBc0J2QixFQUFFO1FBQ3JDLHFDQUFxQztRQUNyQzdCO1FBQ0FTO1FBQ0FxQjtRQUNBNEw7UUFDQUQ7SUFDSjtJQUVBLE1BQU1LLGdCQUFnQjtRQUNsQixPQUFRVDtZQUNKLEtBQUs7Z0JBQ0QscUJBQ0k7O3NDQUNJLDhEQUFDbFEsaURBQU9BOzRCQUFFLEdBQUd5USxXQUFXOzs7Ozs7c0NBQ3hCLDhEQUFDSCxlQUFlZCxZQUFZOzs7Ozs7O1lBR3hDLEtBQUs7Z0JBQ0QscUJBQ0k7O3NDQUNJLDhEQUFDdlAsbURBQVNBOzRCQUFFLEdBQUd3USxXQUFXOzRCQUFFRCxjQUFjQTs7Ozs7O3NDQUMxQyw4REFBQ0YsZUFBZWQsWUFBWTs7Ozs7OztZQUd4QyxLQUFLO2dCQUNELHFCQUNJOztzQ0FDSSw4REFBQ3RQLHFEQUFXQTs0QkFBRSxHQUFHdVEsV0FBVzs7Ozs7O3NDQUM1Qiw4REFBQ0gsZUFBZWQsWUFBWTs7Ozs7OztZQUd4QztnQkFDSSxxQkFDSTs7c0NBQ0ksOERBQUN4UCxpREFBT0E7NEJBQUUsR0FBR3lRLFdBQVc7Ozs7OztzQ0FDeEIsOERBQUNILGVBQWVkLFlBQVk7Ozs7Ozs7UUFHNUM7SUFDSjtJQUVBLE9BQU9tQjtBQUNYO0lBeEVNVjs7UUFDb0I3UCxpRUFBWUE7UUFDaEJELHVEQUFPQTtRQU1GMkY7OztNQVJyQm1LO0FBMEVOLCtEQUFlQSxTQUFTQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS9zcmMvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi9sZWFkLWdlbmVyYXRpb24vY29tcGFueS9pbmRleC50c3g/YzY1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlTWVtbyB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgTXlMZWFkcyBmcm9tIFwiLi9teS1sZWFkc1wiXG5pbXBvcnQgRmluZExlYWRzIGZyb20gXCIuL2ZpbmQtbGVhZHNcIlxuaW1wb3J0IFNhdmVkU2VhcmNoIGZyb20gXCIuL3NhdmVkLXNlYXJjaFwiXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkB1aS9wcm92aWRlcnMvdXNlclwiXG5pbXBvcnQgeyB1c2VXb3Jrc3BhY2UgfSBmcm9tIFwiQHVpL3Byb3ZpZGVycy93b3Jrc3BhY2VcIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBQb3BvdmVyLCBQb3BvdmVyQ29udGVudCwgUG9wb3ZlclRyaWdnZXIgfSBmcm9tIFwiQHVpL2NvbXBvbmVudHMvdWkvcG9wb3ZlclwiXG5pbXBvcnQgeyBEcm9wZG93bk1lbnUsIERyb3Bkb3duTWVudUNvbnRlbnQsIERyb3Bkb3duTWVudUl0ZW0sIERyb3Bkb3duTWVudVRyaWdnZXIsIERyb3Bkb3duTWVudUdyb3VwIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL2Ryb3Bkb3duLW1lbnVcIlxuaW1wb3J0IHsgRW52ZWxvcGVJY29uLCBDaGFydExpbmVJY29uLCBEYXRhYmFzZUljb24sIENvZGVNZXJnZUljb24sIFVwUmlnaHRGcm9tU3F1YXJlSWNvbiB9IGZyb20gXCJAdWkvY29tcG9uZW50cy9pY29ucy9Gb250QXdlc29tZVJlZ3VsYXJcIlxuaW1wb3J0IHsgRGlhbG9nLCBEaWFsb2dDb250ZW50LCBEaWFsb2dIZWFkZXIsIERpYWxvZ1RpdGxlLCBEaWFsb2dEZXNjcmlwdGlvbiwgRGlhbG9nRm9vdGVyIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL2RpYWxvZ1wiXG5pbXBvcnQgeyBUYWJsZSwgVGFibGVCb2R5LCBUYWJsZUNlbGwsIFRhYmxlSGVhZCwgVGFibGVIZWFkZXIsIFRhYmxlUm93IH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL3RhYmxlXCJcbmltcG9ydCB7IENoZWNrYm94IH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL2NoZWNrYm94XCJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL2lucHV0XCJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL3RleHRhcmVhXCJcbmltcG9ydCB7IExhYmVsIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3VpL2xhYmVsXCJcbmltcG9ydCB7IGFkZExlYWRUb0RhdGFiYXNlLCBhZGRMZWFkVG9TZWdtZW50LCBhZGRMZWFkVG9Xb3JrZmxvdywgc2VuZEVtYWlsVG9MZWFkLCB1bmxvY2tMZWFkIH0gZnJvbSBcIkB1aS9hcGkvbGVhZHNcIlxuaW1wb3J0IHsgZ2V0RGF0YWJhc2VzIH0gZnJvbSBcIkB1aS9hcGkvZGF0YWJhc2VcIlxuaW1wb3J0IHsgZ2V0V29ya2Zsb3dzIH0gZnJvbSBcIkB1aS9hcGkvd29ya2Zsb3dcIlxuaW1wb3J0IHsgV29ya2Zsb3dUcmlnZ2VyVHlwZSB9IGZyb20gXCJAcmVwby9hcHAtZGItdXRpbHMvZGlzdC90eXBpbmdzL3dvcmtmbG93XCJcbmltcG9ydCB7IHVzZUFsZXJ0IH0gZnJvbSBcIkB1aS9wcm92aWRlcnMvYWxlcnRcIlxuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VQYXJhbXMgfSBmcm9tIFwiQHVpL2NvbnRleHQvcm91dGVyQ29udGV4dFwiXG5pbXBvcnQgeyBVbmxvY2tFbWFpbE1vZGFsIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL2NvbW1vbi91bmxvY2tFbWFpbFwiXG5pbXBvcnQgeyBVbmxvY2tQaG9uZU1vZGFsIH0gZnJvbSBcIkB1aS9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL2NvbW1vbi91bmxvY2tQaG9uZVwiXG5pbXBvcnQgeyBEYXRhYmFzZVNlbGVjdCB9IGZyb20gXCJAdWkvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi9jb21tb24vZGF0YWJhc2VTZWxlY3RcIlxuaW1wb3J0IHsgT25EZW1hbmRXb3JrZmxvd1NlbGVjdCB9IGZyb20gXCJAdWkvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi9jb21tb24vb25EZW1hbmRXb3JrZmxvd1NlbGVjdFwiXG5pbXBvcnQgeyBUd29TdGVwRGF0YWJhc2VNYXBwaW5nIH0gZnJvbSBcIi4uL2NvbW1vbi9Ud29TdGVwRGF0YWJhc2VNYXBwaW5nXCJcbmltcG9ydCB7IExvYWRlciB9IGZyb20gXCJAdWkvY29tcG9uZW50cy9jdXN0b20tdWkvbG9hZGVyXCJcblxuaW50ZXJmYWNlIENvbXBhbmllc1Byb3BzIHtcbiAgICBhY3RpdmVTdWJUYWI6ICdteS1sZWFkcycgfCAnZmluZC1sZWFkcycgfCAnc2F2ZWQtc2VhcmNoJ1xuICAgIG9uTGVhZENyZWF0ZWQ/OiAobGVhZDogYW55KSA9PiB2b2lkXG59XG5cblxuZXhwb3J0IGNvbnN0IEFjdGlvbkJ1dHRvbiA9ICh7IFxuICAgIGljb246IEljb24sIFxuICAgIGNoaWxkcmVuLCBcbiAgICBvbkNsaWNrIFxufTogeyBcbiAgICBpY29uOiBSZWFjdC5Db21wb25lbnRUeXBlPHsgY2xhc3NOYW1lPzogc3RyaW5nIH0+OyBcbiAgICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlOyBcbiAgICBvbkNsaWNrOiAoKSA9PiB2b2lkOyBcbn0pID0+IChcbiAgICA8QnV0dG9uXG4gICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgb25DbGljaz17b25DbGlja31cbiAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xLjUgaC1hdXRvIGZvbnQtc2VtaWJvbGQgZ2FwLTEgZmxleCBpdGVtcy1jZW50ZXJcIlxuICAgID5cbiAgICAgICAgPEljb24gY2xhc3NOYW1lPVwic2l6ZS0zXCIgLz5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHJ1bmNhdGVcIj57Y2hpbGRyZW59PC9zcGFuPlxuICAgIDwvQnV0dG9uPlxuKVxuXG5leHBvcnQgY29uc3QgVmlld0xpbmtzTW9kYWwgPSAoeyBcbiAgICB0cmlnZ2VyLFxuICAgIGxpbmtzIFxufTogeyBcbiAgICB0cmlnZ2VyOiBSZWFjdC5SZWFjdE5vZGU7XG4gICAgbGlua3M6IEFycmF5PHsgaWQ6IHN0cmluZzsgdGl0bGU6IHN0cmluZzsgdXJsOiBzdHJpbmcgfT4gXG59KSA9PiB7XG4gICAgY29uc3QgW2lzT3Blbiwgc2V0SXNPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgIFxuICAgIHJldHVybiAoXG4gICAgICAgIDxQb3BvdmVyIG9wZW49e2lzT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRJc09wZW59PlxuICAgICAgICAgICAgPFBvcG92ZXJUcmlnZ2VyIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgICAge3RyaWdnZXJ9XG4gICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxuICAgICAgICAgICAgPFBvcG92ZXJDb250ZW50IGNsYXNzTmFtZT1cInctNjQgcC00XCIgYWxpZ249XCJlbmRcIj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNtIG1iLTJcIj5Tb2NpYWwgTGlua3M8L2g0PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHtsaW5rcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+Tm8gbGlua3MgYXZhaWxhYmxlPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICBsaW5rcy5tYXAoKGxpbmspID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2xpbmsuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2xpbmsudXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC14cyB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDAgdHJhbnNpdGlvbi1jb2xvcnMgcC0yIHJvdW5kZWQgaG92ZXI6YmctYmx1ZS01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VXBSaWdodEZyb21TcXVhcmVJY29uIGNsYXNzTmFtZT1cInNpemUtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRydW5jYXRlXCI+e2xpbmsudGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgICAgICkpXG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L1BvcG92ZXJDb250ZW50PlxuICAgICAgICA8L1BvcG92ZXI+XG4gICAgKVxufVxuXG5pbnRlcmZhY2UgTGVhZEFjdGlvbnNEcm9wZG93blByb3BzIHtcbiAgICB0cmlnZ2VyOiBSZWFjdC5SZWFjdE5vZGVcbiAgICBvblNlbmRFbWFpbD86ICgpID0+IHZvaWRcbiAgICBvbkFkZFRvU2VnbWVudHM/OiAoKSA9PiB2b2lkXG4gICAgb25BZGRUb0RhdGFiYXNlPzogKCkgPT4gdm9pZFxuICAgIG9uQWRkVG9Xb3JrZmxvdz86ICgpID0+IHZvaWRcbiAgICBsZWFkRGF0YT86IGFueVxufVxuXG5leHBvcnQgY29uc3QgTGVhZEFjdGlvbnNEcm9wZG93biA9ICh7IFxuICAgIHRyaWdnZXIsIFxuICAgIG9uU2VuZEVtYWlsLCBcbiAgICBvbkFkZFRvU2VnbWVudHMsIFxuICAgIG9uQWRkVG9EYXRhYmFzZSwgXG4gICAgb25BZGRUb1dvcmtmbG93LFxuICAgIGxlYWREYXRhIFxufTogTGVhZEFjdGlvbnNEcm9wZG93blByb3BzKSA9PiB7XG5cbiAgICBjb25zdCBlbWFpbCA9IGxlYWREYXRhPy5ub3JtYWxpemVkRGF0YT8uZW1haWwgfHwgbGVhZERhdGE/LmFwb2xsb0RhdGE/LmVtYWlsIHx8IGxlYWREYXRhPy5lbWFpbFxuICAgIGNvbnN0IGhhc0VtYWlsID0gISFlbWFpbFxuICAgIFxuICAgIHJldHVybiAoXG4gICAgICAgIDxEcm9wZG93bk1lbnU+XG4gICAgICAgICAgICA8RHJvcGRvd25NZW51VHJpZ2dlciBhc0NoaWxkPlxuICAgICAgICAgICAgICAgIHt0cmlnZ2VyfVxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVUcmlnZ2VyPlxuICAgICAgICAgICAgPERyb3Bkb3duTWVudUNvbnRlbnQgXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy01NiByb3VuZGVkLW5vbmUgdGV4dC1uZXV0cmFsLTgwMCBmb250LXNlbWlib2xkXCJcbiAgICAgICAgICAgICAgICBhbGlnbj1cImVuZFwiXG4gICAgICAgICAgICAgICAgc2lkZU9mZnNldD17NH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51R3JvdXAgY2xhc3NOYW1lPVwicC0xIGZsZXggZmxleC1jb2wgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXhzIHJvdW5kZWQtbm9uZSBwLTIgcHktMS41IGZsZXggaXRlbXMtY2VudGVyIGdhcC0yICR7aGFzRW1haWwgPyAnY3Vyc29yLXBvaW50ZXInIDogJ2N1cnNvci1ub3QtYWxsb3dlZCBvcGFjaXR5LTUwJ31gfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFzRW1haWwgPyBvblNlbmRFbWFpbCA6IHVuZGVmaW5lZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshaGFzRW1haWx9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxFbnZlbG9wZUljb24gY2xhc3NOYW1lPVwic2l6ZS0zIHRleHQtbmV1dHJhbC02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+U2VuZCBFbWFpbDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHshaGFzRW1haWwgJiYgPHNwYW4gY2xhc3NOYW1lPVwibWwtYXV0byB0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj4oTG9ja2VkKTwvc3Bhbj59XG4gICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtIFxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLW5vbmUgcC0yIHB5LTEuNSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkFkZFRvU2VnbWVudHN9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGFydExpbmVJY29uIGNsYXNzTmFtZT1cInNpemUtMyB0ZXh0LW5ldXRyYWwtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkFkZCB0byBTZWdtZW50czwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHJvdW5kZWQtbm9uZSBwLTIgcHktMS41IGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uQWRkVG9EYXRhYmFzZX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPERhdGFiYXNlSWNvbiBjbGFzc05hbWU9XCJzaXplLTMgdGV4dC1uZXV0cmFsLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5BZGQgdG8gRGF0YWJhc2U8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtIFxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLW5vbmUgcC0yIHB5LTEuNSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkFkZFRvV29ya2Zsb3d9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDb2RlTWVyZ2VJY29uIGNsYXNzTmFtZT1cInNpemUtMyB0ZXh0LW5ldXRyYWwtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkFkZCB0byBXb3JrZmxvdzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51R3JvdXA+XG4gICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUNvbnRlbnQ+XG4gICAgICAgIDwvRHJvcGRvd25NZW51PlxuICAgIClcbn1cblxuXG5leHBvcnQgY29uc3QgQ29tcGFueVRhYmxlSGVhZGVyID0gKHsgXG4gICAgc2VsZWN0ZWRMZWFkcywgXG4gICAgZmlsdGVyZWRMZWFkcywgXG4gICAgaGFuZGxlU2VsZWN0QWxsIFxufTogeyBcbiAgICBzZWxlY3RlZExlYWRzOiBzdHJpbmdbXVxuICAgIGZpbHRlcmVkTGVhZHM6IGFueVtdXG4gICAgaGFuZGxlU2VsZWN0QWxsOiAoY2hlY2tlZDogYm9vbGVhbiwgbGVhZHM6IGFueVtdKSA9PiB2b2lkXG59KSA9PiAoXG4gICAgPFRhYmxlSGVhZGVyPlxuICAgICAgICA8VGFibGVSb3cgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMjAwIGJnLXdoaXRlIHN0aWNreSB0b3AtMCB6LTEwXCI+XG4gICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cInctMTIgaC0xMCBweC0zXCI+XG4gICAgICAgICAgICAgICAgPENoZWNrYm94XG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3NlbGVjdGVkTGVhZHMubGVuZ3RoID09PSBmaWx0ZXJlZExlYWRzLmxlbmd0aCAmJiBmaWx0ZXJlZExlYWRzLmxlbmd0aCA+IDB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17KGNoZWNrZWQ6IGJvb2xlYW4pID0+IGhhbmRsZVNlbGVjdEFsbChjaGVja2VkLCBmaWx0ZXJlZExlYWRzKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cImgtMTAgcHgtMSB0ZXh0LWxlZnQgZm9udC1ib2xkIHRleHQtYmxhY2tcIj5OYW1lPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cImgtMTAgcHgtMSB0ZXh0LWxlZnQgZm9udC1ib2xkIHRleHQtYmxhY2tcIj5JbmR1c3RyeTwvVGFibGVIZWFkPlxuICAgICAgICAgICAgPFRhYmxlSGVhZCBjbGFzc05hbWU9XCJoLTEwIHB4LTEgdGV4dC1sZWZ0IGZvbnQtYm9sZCB0ZXh0LWJsYWNrXCI+TG9jYXRpb248L1RhYmxlSGVhZD5cbiAgICAgICAgICAgIDxUYWJsZUhlYWQgY2xhc3NOYW1lPVwiaC0xMCBweC0xIHRleHQtbGVmdCBmb250LWJvbGQgdGV4dC1ibGFja1wiPkVtYWlsPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cImgtMTAgcHgtMSB0ZXh0LWxlZnQgZm9udC1ib2xkIHRleHQtYmxhY2tcIj5QaG9uZSBudW1iZXI8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgIDxUYWJsZUhlYWQgY2xhc3NOYW1lPVwiaC0xMCBweC0xIHRleHQtbGVmdCBmb250LWJvbGQgdGV4dC1ibGFja1wiPlNvY2lhbCBMaW5rczwvVGFibGVIZWFkPlxuICAgICAgICAgICAgPFRhYmxlSGVhZCBjbGFzc05hbWU9XCJ3LTEyIGgtMTAgcHgtMVwiPjwvVGFibGVIZWFkPlxuICAgICAgICA8L1RhYmxlUm93PlxuICAgIDwvVGFibGVIZWFkZXI+XG4pXG5cblxuZXhwb3J0IGNvbnN0IHVzZUxlYWRNYW5hZ2VtZW50ID0gKCkgPT4ge1xuICAgIGNvbnN0IHsgdG9hc3QgfSA9IHVzZUFsZXJ0KClcbiAgICBjb25zdCB7IHRva2VuIH0gPSB1c2VBdXRoKClcbiAgICBjb25zdCB7IHdvcmtzcGFjZSB9ID0gdXNlV29ya3NwYWNlKClcbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICAgIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpXG4gICAgXG5cbiAgICBjb25zdCBbc2VsZWN0ZWRMZWFkcywgc2V0U2VsZWN0ZWRMZWFkc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pXG4gICAgY29uc3QgW2VtYWlsTW9kYWxPcGVuLCBzZXRFbWFpbE1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgICBjb25zdCBbcGhvbmVNb2RhbE9wZW4sIHNldFBob25lTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgIGNvbnN0IFtzZWxlY3RlZExlYWRJZCwgc2V0U2VsZWN0ZWRMZWFkSWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgICBcblxuICAgIGNvbnN0IHVubG9ja0VtYWlsQ2FsbGJhY2tSZWYgPSB1c2VSZWY8KChkYXRhOiBhbnkpID0+IHZvaWQpIHwgbnVsbD4obnVsbClcbiAgICBjb25zdCB1bmxvY2tQaG9uZUNhbGxiYWNrUmVmID0gdXNlUmVmPCgoZGF0YTogYW55KSA9PiB2b2lkKSB8IG51bGw+KG51bGwpXG4gICAgXG5cbiAgICBjb25zdCBbYWRkVG9EYXRhYmFzZURpYWxvZ09wZW4sIHNldEFkZFRvRGF0YWJhc2VEaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgIGNvbnN0IFtzZWxlY3RlZExlYWRGb3JEYXRhYmFzZSwgc2V0U2VsZWN0ZWRMZWFkRm9yRGF0YWJhc2VdID0gdXNlU3RhdGU8YW55PihudWxsKVxuICAgIGNvbnN0IFthZGRpbmdUb0RhdGFiYXNlLCBzZXRBZGRpbmdUb0RhdGFiYXNlXSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgIGNvbnN0IFtzZWxlY3RlZExlYWRJZEZvckFjdGlvbiwgc2V0U2VsZWN0ZWRMZWFkSWRGb3JBY3Rpb25dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgICBcblxuICAgIGNvbnN0IFtzZW5kRW1haWxEaWFsb2dPcGVuLCBzZXRTZW5kRW1haWxEaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgIGNvbnN0IFtlbWFpbFN1YmplY3QsIHNldEVtYWlsU3ViamVjdF0gPSB1c2VTdGF0ZSgnJylcbiAgICBjb25zdCBbZW1haWxCb2R5LCBzZXRFbWFpbEJvZHldID0gdXNlU3RhdGUoJycpXG4gICAgY29uc3QgW3NlbmRpbmdFbWFpbCwgc2V0U2VuZGluZ0VtYWlsXSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgIGNvbnN0IFtzZWxlY3RlZExlYWRGb3JFbWFpbCwgc2V0U2VsZWN0ZWRMZWFkRm9yRW1haWxdID0gdXNlU3RhdGU8YW55PihudWxsKVxuXG5cbiAgICBjb25zdCBbYWRkVG9TZWdtZW50RGlhbG9nT3Blbiwgc2V0QWRkVG9TZWdtZW50RGlhbG9nT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgICBjb25zdCBbc2VnbWVudE5hbWUsIHNldFNlZ21lbnROYW1lXSA9IHVzZVN0YXRlKCcnKVxuICAgIGNvbnN0IFthZGRpbmdUb1NlZ21lbnQsIHNldEFkZGluZ1RvU2VnbWVudF0gPSB1c2VTdGF0ZShmYWxzZSlcblxuXG4gICAgY29uc3QgW2FkZFRvV29ya2Zsb3dEaWFsb2dPcGVuLCBzZXRBZGRUb1dvcmtmbG93RGlhbG9nT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgICBjb25zdCBbc2VsZWN0ZWRXb3JrZmxvd0lkLCBzZXRTZWxlY3RlZFdvcmtmbG93SWRdID0gdXNlU3RhdGUoJycpXG4gICAgY29uc3QgW2FkZGluZ1RvV29ya2Zsb3csIHNldEFkZGluZ1RvV29ya2Zsb3ddID0gdXNlU3RhdGUoZmFsc2UpXG4gICAgY29uc3QgW2F2YWlsYWJsZVdvcmtmbG93cywgc2V0QXZhaWxhYmxlV29ya2Zsb3dzXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcbiAgICBjb25zdCBbbG9hZGluZ1dvcmtmbG93cywgc2V0TG9hZGluZ1dvcmtmbG93c10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgICBcblxuICAgIGNvbnN0IGhhbmRsZVNlbGVjdEFsbCA9IChjaGVja2VkOiBib29sZWFuLCBsZWFkczogYW55W10pID0+IHtcbiAgICAgICAgaWYgKGNoZWNrZWQpIHtcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTGVhZHMobGVhZHMubWFwKGxlYWQgPT4gbGVhZC5pZCkpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZExlYWRzKFtdKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgaGFuZGxlU2VsZWN0TGVhZCA9IChsZWFkSWQ6IHN0cmluZywgY2hlY2tlZDogYm9vbGVhbikgPT4ge1xuICAgICAgICBpZiAoY2hlY2tlZCkge1xuICAgICAgICAgICAgc2V0U2VsZWN0ZWRMZWFkcyhbLi4uc2VsZWN0ZWRMZWFkcywgbGVhZElkXSlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTGVhZHMoc2VsZWN0ZWRMZWFkcy5maWx0ZXIoaWQgPT4gaWQgIT09IGxlYWRJZCkpXG4gICAgICAgIH1cbiAgICB9XG5cblxuICAgIGNvbnN0IGhhbmRsZVVubG9ja0VtYWlsID0gKGxlYWRJZDogc3RyaW5nKSA9PiB7XG4gICAgICAgIHNldFNlbGVjdGVkTGVhZElkKGxlYWRJZClcbiAgICAgICAgc2V0RW1haWxNb2RhbE9wZW4odHJ1ZSlcbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVVbmxvY2tQaG9uZSA9IChsZWFkSWQ6IHN0cmluZykgPT4ge1xuICAgICAgICBzZXRTZWxlY3RlZExlYWRJZChsZWFkSWQpXG4gICAgICAgIHNldFBob25lTW9kYWxPcGVuKHRydWUpXG4gICAgfVxuXG5cbiAgICBjb25zdCBoYW5kbGVOYW1lQ2xpY2sgPSAobGVhZDogYW55KSA9PiB7XG4gICAgICAgIGNvbnN0IGRvbWFpbiA9IHBhcmFtcy5kb21haW5cbiAgICAgICAgcm91dGVyLnB1c2goYC8ke2RvbWFpbn0vbGVhZC1nZW5lcmF0aW9uL2NvbXBhbnkvZGV0YWlscy8ke2xlYWQuaWR9YClcbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVWaWV3TGlua3MgPSAobGVhZElkOiBzdHJpbmcpID0+IHtcblxuICAgIH1cblxuXG4gICAgY29uc3QgZ2V0Q29udGFjdExpbmtzID0gKGxlYWQ6IGFueSkgPT4ge1xuICAgICAgICBjb25zdCBsaW5rcyA9IFtdXG4gICAgICAgIFxuXG4gICAgICAgIGlmIChsZWFkLm5hbWUpIHtcbiAgICAgICAgICAgIGxpbmtzLnB1c2goeyBcbiAgICAgICAgICAgICAgICBpZDogXCJsaW5rZWRpblwiLCBcbiAgICAgICAgICAgICAgICB0aXRsZTogXCJMaW5rZWRJbiBQcm9maWxlXCIsIFxuICAgICAgICAgICAgICAgIHVybDogYGh0dHBzOi8vbGlua2VkaW4uY29tL3NlYXJjaC9yZXN1bHRzL3Blb3BsZS8/a2V5d29yZHM9JHtlbmNvZGVVUklDb21wb25lbnQobGVhZC5uYW1lICsgJyAnICsgbGVhZC5jb21wYW55KX1gXG4gICAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgICAgIFxuXG4gICAgICAgIGlmIChsZWFkLmNvbXBhbnkpIHtcbiAgICAgICAgICAgIGxpbmtzLnB1c2goeyBcbiAgICAgICAgICAgICAgICBpZDogXCJjb21wYW55XCIsIFxuICAgICAgICAgICAgICAgIHRpdGxlOiBcIkNvbXBhbnkgV2Vic2l0ZVwiLCBcbiAgICAgICAgICAgICAgICB1cmw6IGBodHRwczovL3d3dy5nb29nbGUuY29tL3NlYXJjaD9xPSR7ZW5jb2RlVVJJQ29tcG9uZW50KGxlYWQuY29tcGFueSArICcgb2ZmaWNpYWwgd2Vic2l0ZScpfWBcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICAgXG5cbiAgICAgICAgaWYgKGxlYWQuY29tcGFueSkge1xuICAgICAgICAgICAgbGlua3MucHVzaCh7IFxuICAgICAgICAgICAgICAgIGlkOiBcImNvbXBhbnktbGlua2VkaW5cIiwgXG4gICAgICAgICAgICAgICAgdGl0bGU6IFwiQ29tcGFueSBMaW5rZWRJblwiLCBcbiAgICAgICAgICAgICAgICB1cmw6IGBodHRwczovL2xpbmtlZGluLmNvbS9zZWFyY2gvcmVzdWx0cy9jb21wYW5pZXMvP2tleXdvcmRzPSR7ZW5jb2RlVVJJQ29tcG9uZW50KGxlYWQuY29tcGFueSl9YFxuICAgICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgICBcblxuICAgICAgICBpZiAobGVhZC5jb21wYW55KSB7XG4gICAgICAgICAgICBsaW5rcy5wdXNoKHsgXG4gICAgICAgICAgICAgICAgaWQ6IFwiZ29vZ2xlXCIsIFxuICAgICAgICAgICAgICAgIHRpdGxlOiBcIkdvb2dsZSBTZWFyY2hcIiwgXG4gICAgICAgICAgICAgICAgdXJsOiBgaHR0cHM6Ly93d3cuZ29vZ2xlLmNvbS9zZWFyY2g/cT0ke2VuY29kZVVSSUNvbXBvbmVudChsZWFkLmNvbXBhbnkpfWBcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICAgXG5cbiAgICAgICAgaWYgKGxlYWQuY29tcGFueSkge1xuICAgICAgICAgICAgbGlua3MucHVzaCh7IFxuICAgICAgICAgICAgICAgIGlkOiBcImVtcGxveWVlc1wiLCBcbiAgICAgICAgICAgICAgICB0aXRsZTogXCJDb21wYW55IEVtcGxveWVlc1wiLCBcbiAgICAgICAgICAgICAgICB1cmw6IGBodHRwczovL2xpbmtlZGluLmNvbS9zZWFyY2gvcmVzdWx0cy9wZW9wbGUvP2tleXdvcmRzPSR7ZW5jb2RlVVJJQ29tcG9uZW50KGxlYWQuY29tcGFueSl9YFxuICAgICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgcmV0dXJuIGxpbmtzXG4gICAgfVxuXG5cbiAgICBjb25zdCBoYW5kbGVJbXBvcnRMZWFkcyA9ICgpID0+IHtcblxuICAgIH1cblxuXG5cbiAgICBjb25zdCBjb252ZXJ0QXBpTGVhZHNUb1VJID0gKGFwaUxlYWRzOiBhbnlbXSk6IGFueVtdID0+IHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgICByZXR1cm4gYXBpTGVhZHMubWFwKChhcGlMZWFkKSA9PiB7XG4gICAgICAgICAgICAvLyBEZWJ1ZyBsb2dnaW5nIGZvciBCQkMgTmV3cyB1bmxvY2sgaXNzdWVcbiAgICAgICAgICAgIGlmIChhcGlMZWFkLm5hbWUgJiYgYXBpTGVhZC5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2JiYycpKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0JCQyBERUJVR10gUHJvY2Vzc2luZyBCQkMgbGVhZDonLCB7XG4gICAgICAgICAgICAgICAgICAgIGlkOiBhcGlMZWFkLmlkLFxuICAgICAgICAgICAgICAgICAgICBuYW1lOiBhcGlMZWFkLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgIGhhc05vcm1hbGl6ZWREYXRhOiAhIWFwaUxlYWQubm9ybWFsaXplZERhdGEsXG4gICAgICAgICAgICAgICAgICAgIGhhc0Fwb2xsb0RhdGE6ICEhYXBpTGVhZC5hcG9sbG9EYXRhLFxuICAgICAgICAgICAgICAgICAgICBub3JtYWxpemVkRGF0YTogYXBpTGVhZC5ub3JtYWxpemVkRGF0YSxcbiAgICAgICAgICAgICAgICAgICAgaXNFbWFpbFZpc2libGU6IGFwaUxlYWQubm9ybWFsaXplZERhdGE/LmlzRW1haWxWaXNpYmxlLFxuICAgICAgICAgICAgICAgICAgICBpc1Bob25lVmlzaWJsZTogYXBpTGVhZC5ub3JtYWxpemVkRGF0YT8uaXNQaG9uZVZpc2libGUsXG4gICAgICAgICAgICAgICAgICAgIGVtYWlsOiBhcGlMZWFkLm5vcm1hbGl6ZWREYXRhPy5lbWFpbCxcbiAgICAgICAgICAgICAgICAgICAgcGhvbmU6IGFwaUxlYWQubm9ybWFsaXplZERhdGE/LnBob25lXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIERldGVjdCBpZiB0aGlzIGlzIGFjdHVhbGx5IHBlcnNvbiBkYXRhIGluIGEgY29tcGFueSBsZWFkXG4gICAgICAgICAgICBjb25zdCBpc1BlcnNvbkRhdGEgPSBhcGlMZWFkLmFwb2xsb0RhdGEgJiYgKFxuICAgICAgICAgICAgICAgIGFwaUxlYWQuYXBvbGxvRGF0YS5maXJzdF9uYW1lIHx8IFxuICAgICAgICAgICAgICAgIGFwaUxlYWQuYXBvbGxvRGF0YS5sYXN0X25hbWUgfHwgXG4gICAgICAgICAgICAgICAgYXBpTGVhZC5hcG9sbG9EYXRhLnBlcnNvbiB8fFxuICAgICAgICAgICAgICAgIGFwaUxlYWQuYXBvbGxvRGF0YS50aXRsZVxuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0NPTlZFUlRdIERhdGEgdHlwZSBkZXRlY3Rpb246Jywge1xuICAgICAgICAgICAgICAgIGlzUGVyc29uRGF0YSxcbiAgICAgICAgICAgICAgICBoYXNGaXJzdE5hbWU6ICEhYXBpTGVhZC5hcG9sbG9EYXRhPy5maXJzdF9uYW1lLFxuICAgICAgICAgICAgICAgIGhhc0xhc3ROYW1lOiAhIWFwaUxlYWQuYXBvbGxvRGF0YT8ubGFzdF9uYW1lLFxuICAgICAgICAgICAgICAgIGhhc1BlcnNvbjogISFhcGlMZWFkLmFwb2xsb0RhdGE/LnBlcnNvbixcbiAgICAgICAgICAgICAgICBoYXNUaXRsZTogISFhcGlMZWFkLmFwb2xsb0RhdGE/LnRpdGxlLFxuICAgICAgICAgICAgICAgIGhhc0luZHVzdHJ5OiAhIWFwaUxlYWQuYXBvbGxvRGF0YT8uaW5kdXN0cnlcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICBsZXQgaW5kdXN0cnkgPSAnLSc7XG4gICAgICAgICAgICBsZXQgbG9jYXRpb24gPSAnLSc7XG4gICAgICAgICAgICBsZXQgbmFtZSA9ICdVbmtub3duJztcbiAgICAgICAgICAgIGxldCBjb21wYW55ID0gJ1Vua25vd24nO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICBpZiAoaXNQZXJzb25EYXRhKSB7XG4gICAgICAgICAgICAgICAgLy8gVGhpcyBpcyBwZXJzb24gZGF0YSwgZXh0cmFjdCBhY2NvcmRpbmdseVxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtDT05WRVJUXSBQcm9jZXNzaW5nIGFzIFBFUlNPTiBkYXRhIGluIGNvbXBhbnkgbGVhZCcpO1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC8vIEZvciBwZXJzb24gZGF0YSwgaW5kdXN0cnkgY29tZXMgZnJvbSBvcmdhbml6YXRpb25cbiAgICAgICAgICAgICAgICBpbmR1c3RyeSA9IGFwaUxlYWQuYXBvbGxvRGF0YT8ub3JnYW5pemF0aW9uPy5pbmR1c3RyeSB8fCAnLSc7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgLy8gRm9yIHBlcnNvbiBkYXRhLCBsb2NhdGlvbiBjb21lcyBmcm9tIHBlcnNvbiBmaWVsZHNcbiAgICAgICAgICAgICAgICBpZiAoYXBpTGVhZC5hcG9sbG9EYXRhLmNpdHkgfHwgYXBpTGVhZC5hcG9sbG9EYXRhLnN0YXRlIHx8IGFwaUxlYWQuYXBvbGxvRGF0YS5jb3VudHJ5KSB7XG4gICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uID0gW2FwaUxlYWQuYXBvbGxvRGF0YS5jaXR5LCBhcGlMZWFkLmFwb2xsb0RhdGEuc3RhdGUsIGFwaUxlYWQuYXBvbGxvRGF0YS5jb3VudHJ5XVxuICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihCb29sZWFuKVxuICAgICAgICAgICAgICAgICAgICAgICAgLmpvaW4oJywgJykgfHwgJy0nO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAvLyBGb3IgcGVyc29uIGRhdGEsIG5hbWUgaXMgZmlyc3QgKyBsYXN0IG5hbWVcbiAgICAgICAgICAgICAgICBjb25zdCBmaXJzdE5hbWUgPSBhcGlMZWFkLmFwb2xsb0RhdGE/LmZpcnN0X25hbWUgfHwgJyc7XG4gICAgICAgICAgICAgICAgY29uc3QgbGFzdE5hbWUgPSBhcGlMZWFkLmFwb2xsb0RhdGE/Lmxhc3RfbmFtZSB8fCAnJztcbiAgICAgICAgICAgICAgICBuYW1lID0gYCR7Zmlyc3ROYW1lfSAke2xhc3ROYW1lfWAudHJpbSgpIHx8IGFwaUxlYWQuYXBvbGxvRGF0YT8ubmFtZSB8fCAnVW5rbm93biBQZXJzb24nO1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC8vIEZvciBwZXJzb24gZGF0YSwgY29tcGFueSBjb21lcyBmcm9tIG9yZ2FuaXphdGlvblxuICAgICAgICAgICAgICAgIGNvbXBhbnkgPSBhcGlMZWFkLmFwb2xsb0RhdGE/Lm9yZ2FuaXphdGlvbj8ubmFtZSB8fCBhcGlMZWFkLmFwb2xsb0RhdGE/Lm9yZ2FuaXphdGlvbj8uY29tcGFueV9uYW1lIHx8ICdVbmtub3duIENvbXBhbnknO1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyBUaGlzIGlzIGFjdHVhbCBjb21wYW55IGRhdGFcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbQ09OVkVSVF0gUHJvY2Vzc2luZyBhcyBDT01QQU5ZIGRhdGEnKTtcbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAvLyBFeHRyYWN0IGluZHVzdHJ5IGZyb20gQXBvbGxvIGRhdGFcbiAgICAgICAgICAgICAgICBpbmR1c3RyeSA9IGFwaUxlYWQuYXBvbGxvRGF0YT8uaW5kdXN0cnkgfHwgJy0nO1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC8vIEV4dHJhY3QgbG9jYXRpb24gLSBjb21wYW5pZXMgbWlnaHQgbm90IGhhdmUgY2l0eS9zdGF0ZS9jb3VudHJ5IGluIEFwb2xsbyBkYXRhXG4gICAgICAgICAgICAgICAgaWYgKGFwaUxlYWQuYXBvbGxvRGF0YSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBhcG9sbG9EYXRhID0gYXBpTGVhZC5hcG9sbG9EYXRhO1xuICAgICAgICAgICAgICAgICAgICAvLyBDaGVjayBpZiBsb2NhdGlvbiBkYXRhIGV4aXN0cyBpbiBhcG9sbG9EYXRhXG4gICAgICAgICAgICAgICAgICAgIGlmIChhcG9sbG9EYXRhLmNpdHkgfHwgYXBvbGxvRGF0YS5zdGF0ZSB8fCBhcG9sbG9EYXRhLmNvdW50cnkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uID0gW2Fwb2xsb0RhdGEuY2l0eSwgYXBvbGxvRGF0YS5zdGF0ZSwgYXBvbGxvRGF0YS5jb3VudHJ5XVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5maWx0ZXIoQm9vbGVhbilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuam9pbignLCAnKSB8fCAnLSc7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoYXBvbGxvRGF0YS5sb2NhdGlvbikge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ2hlY2sgaWYgbG9jYXRpb24gaXMgaW4gYSBuZXN0ZWQgb2JqZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBsb2MgPSBhcG9sbG9EYXRhLmxvY2F0aW9uO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBsb2MgPT09ICdvYmplY3QnICYmIGxvYyAhPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uID0gW2xvYy5jaXR5LCBsb2Muc3RhdGUsIGxvYy5jb3VudHJ5XVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKEJvb2xlYW4pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5qb2luKCcsICcpIHx8ICctJztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAvLyBFeHRyYWN0IG5hbWUgd2l0aCBmYWxsYmFja1xuICAgICAgICAgICAgICAgIG5hbWUgPSBhcGlMZWFkLm5vcm1hbGl6ZWREYXRhPy5uYW1lIHx8IGFwaUxlYWQuYXBvbGxvRGF0YT8ubmFtZSB8fCAnVW5rbm93biBDb21wYW55JztcbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAvLyBFeHRyYWN0IGNvbXBhbnkgbmFtZSB3aXRoIGZhbGxiYWNrXG4gICAgICAgICAgICAgICAgY29tcGFueSA9IGFwaUxlYWQubm9ybWFsaXplZERhdGE/LmNvbXBhbnkgfHwgYXBpTGVhZC5hcG9sbG9EYXRhPy5uYW1lIHx8IG5hbWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtDT05WRVJUXSBFeHRyYWN0ZWQgZGF0YTonLCB7XG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICBjb21wYW55LFxuICAgICAgICAgICAgICAgIGluZHVzdHJ5LFxuICAgICAgICAgICAgICAgIGxvY2F0aW9uLFxuICAgICAgICAgICAgICAgIGlzUGVyc29uRGF0YVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgaWQ6IGFwaUxlYWQuaWQsXG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICBjb21wYW55LFxuICAgICAgICAgICAgICAgIGVtYWlsOiBhcGlMZWFkLm5vcm1hbGl6ZWREYXRhPy5pc0VtYWlsVmlzaWJsZSA/IGFwaUxlYWQubm9ybWFsaXplZERhdGEuZW1haWwgfHwgXCJ1bmxvY2tcIiA6IFwidW5sb2NrXCIsXG4gICAgICAgICAgICAgICAgcGhvbmU6IGFwaUxlYWQubm9ybWFsaXplZERhdGE/LmlzUGhvbmVWaXNpYmxlID8gYXBpTGVhZC5ub3JtYWxpemVkRGF0YS5waG9uZSB8fCBcInVubG9ja1wiIDogXCJ1bmxvY2tcIixcbiAgICAgICAgICAgICAgICBsaW5rczogXCJ2aWV3XCIsXG4gICAgICAgICAgICAgICAgaW5kdXN0cnksXG4gICAgICAgICAgICAgICAgbG9jYXRpb25cbiAgICAgICAgICAgIH07XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIFNoYXJlZCBmaWx0ZXJlZCBsZWFkcyBsb2dpYyAoY29tcGFueSB2ZXJzaW9uKVxuICAgIGNvbnN0IGdldEZpbHRlcmVkTGVhZHMgPSAobGVhZHM6IGFueVtdLCBzZWFyY2hRdWVyeT86IHN0cmluZywgZmlsdGVyPzogYW55KSA9PiB7XG4gICAgICAgIHJldHVybiB1c2VNZW1vKCgpID0+IHtcbiAgICAgICAgICAgIGxldCBmaWx0ZXJlZCA9IGxlYWRzXG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIEFwcGx5IHNlYXJjaCBmaWx0ZXIgaWYgdXNlciBpcyBzZWFyY2hpbmcgbG9jYWxseVxuICAgICAgICAgICAgaWYgKHNlYXJjaFF1ZXJ5Py50cmltKCkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBzZWFyY2hUZXJtID0gc2VhcmNoUXVlcnkudHJpbSgpLnRvTG93ZXJDYXNlKClcbiAgICAgICAgICAgICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihsZWFkID0+IFxuICAgICAgICAgICAgICAgICAgICBPYmplY3QudmFsdWVzKGxlYWQpLnNvbWUodmFsdWUgPT4gXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnICYmIHZhbHVlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSlcbiAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gQXBwbHkgZmlsdGVyIGNvbmRpdGlvbnMgKGlmIGFueSBmaWx0ZXJzIGFyZSBzZXQpXG4gICAgICAgICAgICBpZiAoZmlsdGVyPy5jb25kaXRpb25zPy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIobGVhZCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmaWx0ZXIuY29uZGl0aW9ucy5ldmVyeSgoY29uZGl0aW9uOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gY29uZGl0aW9uLnZhbHVlPy50b1N0cmluZygpLnRvTG93ZXJDYXNlKCkgfHwgJydcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGxlYWRWYWx1ZSA9IGxlYWRbY29uZGl0aW9uLmNvbHVtbklkIGFzIGtleW9mIGFueV0/LnRvU3RyaW5nKCkudG9Mb3dlckNhc2UoKSB8fCAnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGxlYWRWYWx1ZS5pbmNsdWRlcyh2YWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICByZXR1cm4gZmlsdGVyZWRcbiAgICAgICAgfSwgW2xlYWRzLCBzZWFyY2hRdWVyeSwgZmlsdGVyXSlcbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVTZW5kRW1haWwgPSBhc3luYyAobGVhZElkOiBzdHJpbmcsIGxlYWREYXRhPzogYW55KSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwi8J+UjSBbSU5ERVhdIGhhbmRsZVNlbmRFbWFpbCBjYWxsZWRcIilcbiAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIFtJTkRFWF0gbGVhZElkOlwiLCBsZWFkSWQpXG4gICAgICAgIGNvbnNvbGUubG9nKFwi8J+UjSBbSU5ERVhdIGxlYWREYXRhOlwiLCBsZWFkRGF0YSlcbiAgICAgICAgXG4gICAgICAgIC8vIFVzZSBwcm92aWRlZCBsZWFkIGRhdGEgb3IgY3JlYXRlIGEgYmFzaWMgb2JqZWN0XG4gICAgICAgIGNvbnN0IGxlYWQgPSBsZWFkRGF0YSB8fCB7IGlkOiBsZWFkSWQsIG5hbWU6ICdVbmtub3duIExlYWQnIH1cbiAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIFtJTkRFWF0gbGVhZCBvYmplY3Q6XCIsIGxlYWQpXG4gICAgICAgIFxuICAgICAgICAvLyBDaGVjayBpZiBlbWFpbCBpcyB1bmxvY2tlZCBhbmQgdmlzaWJsZVxuICAgICAgICBjb25zdCBlbWFpbCA9IGxlYWQubm9ybWFsaXplZERhdGE/LmVtYWlsIHx8IGxlYWQuYXBvbGxvRGF0YT8uZW1haWwgfHwgbGVhZC5lbWFpbFxuICAgICAgICBjb25zdCBpc0VtYWlsVmlzaWJsZSA9IGxlYWQubm9ybWFsaXplZERhdGE/LmlzRW1haWxWaXNpYmxlIHx8IGxlYWQuYXBvbGxvRGF0YT8ubm9ybWFsaXplZERhdGE/LmlzRW1haWxWaXNpYmxlXG4gICAgICAgIGNvbnNvbGUubG9nKFwi8J+UjSBbSU5ERVhdIGVtYWlsIGZvdW5kOlwiLCBlbWFpbClcbiAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIFtJTkRFWF0gaXNFbWFpbFZpc2libGU6XCIsIGlzRW1haWxWaXNpYmxlKVxuICAgICAgICBcbiAgICAgICAgaWYgKCFlbWFpbCB8fCAhaXNFbWFpbFZpc2libGUpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwi8J+UjSBbSU5ERVhdIEVtYWlsIG5vdCB1bmxvY2tlZCwgc2hvd2luZyBlcnJvciB0b2FzdFwiKVxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoXCJZb3UgaGF2ZSB0byB1bmxvY2sgdGhlIGVtYWlsIGZpcnN0IGJlZm9yZSBzZW5kaW5nIGFuIGVtYWlsLlwiKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnNvbGUubG9nKFwi8J+UjSBbSU5ERVhdIEVtYWlsIGZvdW5kLCBvcGVuaW5nIG1vZGFsXCIpXG4gICAgICAgIHNldFNlbGVjdGVkTGVhZEZvckVtYWlsKHsgLi4ubGVhZCwgZW1haWwgfSlcbiAgICAgICAgc2V0RW1haWxTdWJqZWN0KCcnKVxuICAgICAgICBzZXRFbWFpbEJvZHkoJycpXG4gICAgICAgIHNldFNlbmRFbWFpbERpYWxvZ09wZW4odHJ1ZSlcbiAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIFtJTkRFWF0gTW9kYWwgc3RhdGUgc2V0IHRvIHRydWVcIilcbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVDb25maXJtU2VuZEVtYWlsID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICBpZiAoIXNlbGVjdGVkTGVhZEZvckVtYWlsIHx8ICFlbWFpbFN1YmplY3QudHJpbSgpIHx8ICFlbWFpbEJvZHkudHJpbSgpKSB7XG4gICAgICAgICAgICB0b2FzdC5lcnJvcihcIlBsZWFzZSBmaWxsIGluIGJvdGggc3ViamVjdCBhbmQgYm9keVwiKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cblxuICAgICAgICBzZXRTZW5kaW5nRW1haWwodHJ1ZSlcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc2VuZEVtYWlsVG9MZWFkKHRva2VuPy50b2tlbiB8fCAnJywgd29ya3NwYWNlPy53b3Jrc3BhY2U/LmlkIHx8ICcnLCBzZWxlY3RlZExlYWRGb3JFbWFpbC5pZCwge1xuICAgICAgICAgICAgICAgIHN1YmplY3Q6IGVtYWlsU3ViamVjdC50cmltKCksXG4gICAgICAgICAgICAgICAgYm9keTogZW1haWxCb2R5LnRyaW0oKVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gQ2hlY2sgaWYgdGhlIEFQSSBjYWxsIHdhcyBhY3R1YWxseSBzdWNjZXNzZnVsXG4gICAgICAgICAgICBpZiAocmVzcG9uc2UuaXNTdWNjZXNzKSB7XG4gICAgICAgICAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkVtYWlsIHNlbnQgc3VjY2Vzc2Z1bGx5IVwiKVxuICAgICAgICAgICAgICAgIHNldFNlbmRFbWFpbERpYWxvZ09wZW4oZmFsc2UpXG4gICAgICAgICAgICAgICAgc2V0RW1haWxTdWJqZWN0KCcnKVxuICAgICAgICAgICAgICAgIHNldEVtYWlsQm9keSgnJylcbiAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZExlYWRGb3JFbWFpbChudWxsKVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcihyZXNwb25zZS5lcnJvciB8fCBcIkZhaWxlZCB0byBzZW5kIGVtYWlsXCIpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIHNlbmQgZW1haWw6XCIsIGVycm9yKVxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gc2VuZCBlbWFpbFwiKVxuICAgICAgICB9IGZpbmFsbHkge1xuICAgICAgICAgICAgc2V0U2VuZGluZ0VtYWlsKGZhbHNlKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgaGFuZGxlQWRkVG9TZWdtZW50cyA9IGFzeW5jIChsZWFkSWQ6IHN0cmluZykgPT4ge1xuICAgICAgICBzZXRTZWxlY3RlZExlYWRJZEZvckFjdGlvbihsZWFkSWQpXG4gICAgICAgIHNldFNlZ21lbnROYW1lKCcnKVxuICAgICAgICBzZXRBZGRUb1NlZ21lbnREaWFsb2dPcGVuKHRydWUpXG4gICAgfVxuXG4gICAgY29uc3QgaGFuZGxlQ29uZmlybUFkZFRvU2VnbWVudCA9IGFzeW5jICgpID0+IHtcbiAgICAgICAgaWYgKCFzZWxlY3RlZExlYWRJZEZvckFjdGlvbiB8fCAhc2VnbWVudE5hbWUudHJpbSgpKSB7XG4gICAgICAgICAgICB0b2FzdC5lcnJvcihcIlBsZWFzZSBlbnRlciBhIHNlZ21lbnQgbmFtZVwiKVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cblxuICAgICAgICBzZXRBZGRpbmdUb1NlZ21lbnQodHJ1ZSlcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGF3YWl0IGFkZExlYWRUb1NlZ21lbnQodG9rZW4/LnRva2VuIHx8ICcnLCB3b3Jrc3BhY2U/LndvcmtzcGFjZT8uaWQgfHwgJycsIHNlbGVjdGVkTGVhZElkRm9yQWN0aW9uLCB7IFxuICAgICAgICAgICAgICAgIG5hbWU6IHNlZ21lbnROYW1lLnRyaW0oKSBcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB0b2FzdC5zdWNjZXNzKFwiTGVhZCBhZGRlZCB0byBzZWdtZW50IHN1Y2Nlc3NmdWxseSFcIilcbiAgICAgICAgICAgIHNldEFkZFRvU2VnbWVudERpYWxvZ09wZW4oZmFsc2UpXG4gICAgICAgICAgICBzZXRTZWdtZW50TmFtZSgnJylcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTGVhZElkRm9yQWN0aW9uKCcnKVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBhZGQgdG8gc2VnbWVudDpcIiwgZXJyb3IpXG4gICAgICAgICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byBhZGQgdG8gc2VnbWVudFwiKVxuICAgICAgICB9IGZpbmFsbHkge1xuICAgICAgICAgICAgc2V0QWRkaW5nVG9TZWdtZW50KGZhbHNlKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgaGFuZGxlQWRkVG9EYXRhYmFzZSA9IGFzeW5jIChsZWFkSWQ6IHN0cmluZywgbGVhZERhdGE/OiBhbnkpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIGhhbmRsZUFkZFRvRGF0YWJhc2UgY2FsbGVkIHdpdGggbGVhZElkOlwiLCBsZWFkSWQpXG4gICAgICAgIFxuICAgICAgICAvLyBVc2UgcHJvdmlkZWQgbGVhZCBkYXRhIG9yIGNyZWF0ZSBhIGJhc2ljIG9iamVjdFxuICAgICAgICBjb25zdCBsZWFkID0gbGVhZERhdGEgfHwgeyBpZDogbGVhZElkLCBuYW1lOiAnVW5rbm93biBDb21wYW55JyB9XG4gICAgICAgIHNldFNlbGVjdGVkTGVhZEZvckRhdGFiYXNlKGxlYWQpXG4gICAgICAgIHNldEFkZFRvRGF0YWJhc2VEaWFsb2dPcGVuKHRydWUpXG4gICAgfVxuXG5cblxuICAgIGNvbnN0IGhhbmRsZUNvbmZpcm1BZGRUb0RhdGFiYXNlID0gYXN5bmMgKG1hcHBpbmdzOiBhbnlbXSwgZGF0YWJhc2VJZDogc3RyaW5nKSA9PiB7XG4gICAgICAgIGlmICghc2VsZWN0ZWRMZWFkRm9yRGF0YWJhc2UgfHwgIWRhdGFiYXNlSWQgfHwgbWFwcGluZ3MubGVuZ3RoID09PSAwKSByZXR1cm5cbiAgICAgICAgXG4gICAgICAgIHNldEFkZGluZ1RvRGF0YWJhc2UodHJ1ZSlcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIEZvciBub3csIHVzZSB0aGUgZXhpc3RpbmcgQVBJIHdpdGgganVzdCBkYXRhYmFzZUlkXG4gICAgICAgICAgICAvLyBUT0RPOiBVcGRhdGUgQVBJIHRvIHN1cHBvcnQgZmllbGQgbWFwcGluZ3NcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYWRkTGVhZFRvRGF0YWJhc2UoXG4gICAgICAgICAgICAgICAgdG9rZW4/LnRva2VuIHx8ICcnLCBcbiAgICAgICAgICAgICAgICB3b3Jrc3BhY2U/LndvcmtzcGFjZT8uaWQgfHwgJycsIFxuICAgICAgICAgICAgICAgIHNlbGVjdGVkTGVhZEZvckRhdGFiYXNlLmlkLCBcbiAgICAgICAgICAgICAgICB7IFxuICAgICAgICAgICAgICAgICAgICB0YXJnZXREYXRhYmFzZUlkOiBkYXRhYmFzZUlkXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgKVxuICAgICAgICAgICAgXG4gICAgICAgICAgICBpZiAocmVzcG9uc2UuaXNTdWNjZXNzKSB7XG4gICAgICAgICAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkxlYWQgYWRkZWQgdG8gZGF0YWJhc2Ugc3VjY2Vzc2Z1bGx5IVwiKVxuICAgICAgICAgICAgICAgIHNldEFkZFRvRGF0YWJhc2VEaWFsb2dPcGVuKGZhbHNlKVxuICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkTGVhZEZvckRhdGFiYXNlKG51bGwpXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRvYXN0LmVycm9yKHJlc3BvbnNlLmVycm9yIHx8IFwiRmFpbGVkIHRvIGFkZCBsZWFkIHRvIGRhdGFiYXNlXCIpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGFkZCBsZWFkIHRvIGRhdGFiYXNlOlwiLCBlcnJvcilcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiRmFpbGVkIHRvIGFkZCBsZWFkIHRvIGRhdGFiYXNlXCIpXG4gICAgICAgIH0gZmluYWxseSB7XG4gICAgICAgICAgICBzZXRBZGRpbmdUb0RhdGFiYXNlKGZhbHNlKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgaGFuZGxlQWRkVG9Xb3JrZmxvdyA9IGFzeW5jIChsZWFkSWQ6IHN0cmluZykgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gW0ZST05URU5EXSBoYW5kbGVBZGRUb1dvcmtmbG93IGNhbGxlZCB3aXRoIGxlYWRJZDpcIiwgbGVhZElkKVxuICAgICAgICBzZXRTZWxlY3RlZExlYWRJZEZvckFjdGlvbihsZWFkSWQpXG4gICAgICAgIFxuICAgICAgICAvLyBMb2FkIHdvcmtmbG93cyB3aGVuIGRpYWxvZyBvcGVuc1xuICAgICAgICBzZXRMb2FkaW5nV29ya2Zsb3dzKHRydWUpXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gW0ZST05URU5EXSBMb2FkaW5nIHJlYWwgd29ya2Zsb3dzIGZyb20gQVBJLi4uXCIpXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFdvcmtmbG93cyh0b2tlbj8udG9rZW4gfHwgJycsIHdvcmtzcGFjZT8ud29ya3NwYWNlPy5pZCB8fCAnJylcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmlzU3VjY2VzcyAmJiByZXNwb25zZS5kYXRhPy5kYXRhPy53b3JrZmxvd3MpIHtcbiAgICAgICAgICAgICAgICAvLyBGaWx0ZXIgZm9yIE9uRGVtYW5kX0NhbGxhYmxlIHdvcmtmbG93cyB0aGF0IGNhbiBiZSBtYW51YWxseSB0cmlnZ2VyZWRcbiAgICAgICAgICAgICAgICBjb25zdCBvbkRlbWFuZFdvcmtmbG93cyA9IHJlc3BvbnNlLmRhdGEuZGF0YS53b3JrZmxvd3MuZmlsdGVyKHcgPT4gdy50cmlnZ2VyVHlwZSA9PT0gV29ya2Zsb3dUcmlnZ2VyVHlwZS5PbkRlbWFuZF9DYWxsYWJsZSlcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gW0ZST05URU5EXSBGb3VuZCBPbkRlbWFuZCB3b3JrZmxvd3M6XCIsIG9uRGVtYW5kV29ya2Zsb3dzLm1hcCh3ID0+ICh7IGlkOiB3LmlkLCBuYW1lOiB3Lm5hbWUgfSkpKVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIGlmIChvbkRlbWFuZFdvcmtmbG93cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldEF2YWlsYWJsZVdvcmtmbG93cyhvbkRlbWFuZFdvcmtmbG93cylcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiBubyBPbkRlbWFuZCB3b3JrZmxvd3MsIGFkZCBkZWZhdWx0IG9wdGlvblxuICAgICAgICAgICAgICAgICAgICBzZXRBdmFpbGFibGVXb3JrZmxvd3MoW1xuICAgICAgICAgICAgICAgICAgICAgICAgeyBpZDogJ2RlZmF1bHQtd29ya2Zsb3cnLCBuYW1lOiAnRGVmYXVsdCBMZWFkIFdvcmtmbG93JyB9XG4gICAgICAgICAgICAgICAgICAgIF0pXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwi8J+UjSBbRlJPTlRFTkRdIEZhaWxlZCB0byBsb2FkIHdvcmtmbG93czpcIiwgcmVzcG9uc2UuZXJyb3IpXG4gICAgICAgICAgICAgICAgc2V0QXZhaWxhYmxlV29ya2Zsb3dzKFtcbiAgICAgICAgICAgICAgICAgICAgeyBpZDogJ2RlZmF1bHQtd29ya2Zsb3cnLCBuYW1lOiAnRGVmYXVsdCBMZWFkIFdvcmtmbG93JyB9XG4gICAgICAgICAgICAgICAgXSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwi8J+UjSBbRlJPTlRFTkRdIExvYWRlZCB3b3JrZmxvd3Mgc3VjY2Vzc2Z1bGx5XCIpXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwi8J+UjSBbRlJPTlRFTkRdIEZhaWxlZCB0byBsb2FkIHdvcmtmbG93czpcIiwgZXJyb3IpXG4gICAgICAgICAgICBzZXRBdmFpbGFibGVXb3JrZmxvd3MoW1xuICAgICAgICAgICAgICAgIHsgaWQ6ICdkZWZhdWx0LXdvcmtmbG93JywgbmFtZTogJ0RlZmF1bHQgTGVhZCBXb3JrZmxvdycgfVxuICAgICAgICAgICAgXSlcbiAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgIHNldExvYWRpbmdXb3JrZmxvd3MoZmFsc2UpXG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIHNldEFkZFRvV29ya2Zsb3dEaWFsb2dPcGVuKHRydWUpXG4gICAgfVxuXG4gICAgY29uc3QgaGFuZGxlQ29uZmlybUFkZFRvV29ya2Zsb3cgPSBhc3luYyAoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwi8J+UjSBbRlJPTlRFTkRdIGhhbmRsZUNvbmZpcm1BZGRUb1dvcmtmbG93IGNhbGxlZFwiKVxuICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gW0ZST05URU5EXSBzZWxlY3RlZExlYWRJZEZvckFjdGlvbjpcIiwgc2VsZWN0ZWRMZWFkSWRGb3JBY3Rpb24pXG4gICAgICAgIGNvbnNvbGUubG9nKFwi8J+UjSBbRlJPTlRFTkRdIHNlbGVjdGVkV29ya2Zsb3dJZDpcIiwgc2VsZWN0ZWRXb3JrZmxvd0lkKVxuICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gW0ZST05URU5EXSB0b2tlbjpcIiwgdG9rZW4/LnRva2VuID8gXCJleGlzdHNcIiA6IFwibWlzc2luZ1wiKVxuICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gW0ZST05URU5EXSB3b3Jrc3BhY2VJZDpcIiwgd29ya3NwYWNlPy53b3Jrc3BhY2U/LmlkKVxuICAgICAgICBcbiAgICAgICAgaWYgKCFzZWxlY3RlZExlYWRJZEZvckFjdGlvbiB8fCAhc2VsZWN0ZWRXb3JrZmxvd0lkKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gW0ZST05URU5EXSBFUlJPUjogTWlzc2luZyBsZWFkSWQgb3Igd29ya2Zsb3dJZFwiKVxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoXCJQbGVhc2Ugc2VsZWN0IGEgd29ya2Zsb3dcIilcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG5cbiAgICAgICAgc2V0QWRkaW5nVG9Xb3JrZmxvdyh0cnVlKVxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIFtGUk9OVEVORF0gQ2FsbGluZyBhZGRMZWFkVG9Xb3JrZmxvdyBBUEkuLi5cIilcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGFkZExlYWRUb1dvcmtmbG93KHRva2VuPy50b2tlbiB8fCAnJywgd29ya3NwYWNlPy53b3Jrc3BhY2U/LmlkIHx8ICcnLCBzZWxlY3RlZExlYWRJZEZvckFjdGlvbiwgeyBcbiAgICAgICAgICAgICAgICB3b3JrZmxvd0lkOiBzZWxlY3RlZFdvcmtmbG93SWQgXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIFtGUk9OVEVORF0gYWRkTGVhZFRvV29ya2Zsb3cgQVBJIHJlc3VsdDpcIiwgcmVzdWx0KVxuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgQVBJIGNhbGwgd2FzIGFjdHVhbGx5IHN1Y2Nlc3NmdWxcbiAgICAgICAgICAgIGlmIChyZXN1bHQuaXNTdWNjZXNzKSB7XG4gICAgICAgICAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkxlYWQgYWRkZWQgdG8gd29ya2Zsb3cgc3VjY2Vzc2Z1bGx5IVwiKVxuICAgICAgICAgICAgICAgIHNldEFkZFRvV29ya2Zsb3dEaWFsb2dPcGVuKGZhbHNlKVxuICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkV29ya2Zsb3dJZCgnJylcbiAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZExlYWRJZEZvckFjdGlvbignJylcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgdG9hc3QuZXJyb3IocmVzdWx0LmVycm9yIHx8IFwiRmFpbGVkIHRvIGFkZCBsZWFkIHRvIHdvcmtmbG93XCIpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwi8J+UjSBbRlJPTlRFTkRdIEVSUk9SIGluIGFkZExlYWRUb1dvcmtmbG93OlwiLCBlcnJvcilcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCLwn5SNIFtGUk9OVEVORF0gRXJyb3IgZGV0YWlsczpcIiwge1xuICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBlcnJvci5zdGF0dXMsXG4gICAgICAgICAgICAgICAgcmVzcG9uc2U6IGVycm9yLnJlc3BvbnNlXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gYWRkIHRvIHdvcmtmbG93XCIpXG4gICAgICAgIH0gZmluYWxseSB7XG4gICAgICAgICAgICBzZXRBZGRpbmdUb1dvcmtmbG93KGZhbHNlKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgQWRkVG9Xb3JrZmxvd0RpYWxvZyA9ICh7XG4gICAgICAgIG9wZW4sXG4gICAgICAgIG9uT3BlbkNoYW5nZSxcbiAgICAgICAgc2VsZWN0ZWRXb3JrZmxvd0lkLFxuICAgICAgICBzZXRTZWxlY3RlZFdvcmtmbG93SWQsXG4gICAgICAgIGFkZGluZ1RvV29ya2Zsb3csXG4gICAgICAgIGxvYWRpbmdXb3JrZmxvd3MsXG4gICAgICAgIGF2YWlsYWJsZVdvcmtmbG93cyxcbiAgICAgICAgaGFuZGxlQ29uZmlybUFkZFRvV29ya2Zsb3dcbiAgICB9OiB7XG4gICAgICAgIG9wZW46IGJvb2xlYW5cbiAgICAgICAgb25PcGVuQ2hhbmdlOiAob3BlbjogYm9vbGVhbikgPT4gdm9pZFxuICAgICAgICBzZWxlY3RlZFdvcmtmbG93SWQ6IHN0cmluZ1xuICAgICAgICBzZXRTZWxlY3RlZFdvcmtmbG93SWQ6IChpZDogc3RyaW5nKSA9PiB2b2lkXG4gICAgICAgIGFkZGluZ1RvV29ya2Zsb3c6IGJvb2xlYW5cbiAgICAgICAgbG9hZGluZ1dvcmtmbG93czogYm9vbGVhblxuICAgICAgICBhdmFpbGFibGVXb3JrZmxvd3M6IGFueVtdXG4gICAgICAgIGhhbmRsZUNvbmZpcm1BZGRUb1dvcmtmbG93OiAoKSA9PiB2b2lkXG4gICAgfSkgPT4ge1xuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPERpYWxvZyBvcGVuPXtvcGVufSBvbk9wZW5DaGFuZ2U9e29uT3BlbkNoYW5nZX0+XG4gICAgICAgICAgICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwic206bWF4LXctWzQyNXB4XVwiPlxuICAgICAgICAgICAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPERpYWxvZ1RpdGxlPkFkZCBMZWFkIHRvIFdvcmtmbG93PC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxEaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBTZWxlY3QgYSB3b3JrZmxvdyB0byBhZGQgdGhpcyBsZWFkIHRvLlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmdXb3JrZmxvd3MgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Mb2FkaW5nIHdvcmtmbG93cy4uLjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IGF2YWlsYWJsZVdvcmtmbG93cy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgbWItMlwiPk5vIHdvcmtmbG93cyBhdmFpbGFibGU8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUGxlYXNlIGNyZWF0ZSBhIHdvcmtmbG93IGZpcnN0LlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+U2VsZWN0IFdvcmtmbG93OjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkV29ya2Zsb3dJZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkV29ya2Zsb3dJZChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmdXb3JrZmxvd3N9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMSB3LWZ1bGwgcC0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPkNob29zZSBhIHdvcmtmbG93Li4uPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2F2YWlsYWJsZVdvcmtmbG93cy5tYXAoKHdvcmtmbG93KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXt3b3JrZmxvdy5pZH0gdmFsdWU9e3dvcmtmbG93LmlkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt3b3JrZmxvdy5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8RGlhbG9nRm9vdGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbk9wZW5DaGFuZ2UoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXthZGRpbmdUb1dvcmtmbG93fVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ29uZmlybUFkZFRvV29ya2Zsb3d9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZFdvcmtmbG93SWQgfHwgYWRkaW5nVG9Xb3JrZmxvdyB8fCBsb2FkaW5nV29ya2Zsb3dzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdhcC0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YWRkaW5nVG9Xb3JrZmxvdyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMb2FkZXIgdGhlbWU9XCJkYXJrXCIgY2xhc3NOYW1lPVwic2l6ZS00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFkZGluZy4uLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENvZGVNZXJnZUljb24gY2xhc3NOYW1lPVwic2l6ZS00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFkZCB0byBXb3JrZmxvd1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvRGlhbG9nRm9vdGVyPlxuICAgICAgICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgICAgICAgIDwvRGlhbG9nPlxuICAgICAgICApXG4gICAgfVxuXG5cbiAgICAvLyBTZW5kIEVtYWlsIERpYWxvZyBDb21wb25lbnRcbiAgICBjb25zdCBTZW5kRW1haWxEaWFsb2cgPSAoKSA9PiB7XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8RGlhbG9nIG9wZW49e3NlbmRFbWFpbERpYWxvZ09wZW59IG9uT3BlbkNoYW5nZT17c2V0U2VuZEVtYWlsRGlhbG9nT3Blbn0+XG4gICAgICAgICAgICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwic206bWF4LXctWzYwMHB4XVwiPlxuICAgICAgICAgICAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPERpYWxvZ1RpdGxlPlNlbmQgRW1haWwgdG8gTGVhZDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTQgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRMZWFkRm9yRW1haWwgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktNTAgcm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+VG86PC9zcGFuPiB7c2VsZWN0ZWRMZWFkRm9yRW1haWwubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRMZWFkRm9yRW1haWwuZW1haWwgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5FbWFpbDo8L3NwYW4+IHtzZWxlY3RlZExlYWRGb3JFbWFpbC5lbWFpbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJlbWFpbC1zdWJqZWN0XCI+U3ViamVjdDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZW1haWwtc3ViamVjdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtlbWFpbFN1YmplY3R9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RW1haWxTdWJqZWN0KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBlbWFpbCBzdWJqZWN0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NlbmRpbmdFbWFpbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJlbWFpbC1ib2R5XCI+TWVzc2FnZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZW1haWwtYm9keVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtlbWFpbEJvZHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RW1haWxCb2R5KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIG1lc3NhZ2UgaGVyZS4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezh9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzZW5kaW5nRW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPERpYWxvZ0Zvb3Rlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VuZEVtYWlsRGlhbG9nT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NlbmRpbmdFbWFpbH1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNvbmZpcm1TZW5kRW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFlbWFpbFN1YmplY3QudHJpbSgpIHx8ICFlbWFpbEJvZHkudHJpbSgpIHx8IHNlbmRpbmdFbWFpbH1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VuZGluZ0VtYWlsID8gXCJTZW5kaW5nLi4uXCIgOiBcIlNlbmQgRW1haWxcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L0RpYWxvZ0Zvb3Rlcj5cbiAgICAgICAgICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgICAgICAgICA8L0RpYWxvZz5cbiAgICAgICAgKVxuICAgIH1cblxuICAgIGNvbnN0IEFkZFRvU2VnbWVudERpYWxvZyA9ICgpID0+IHtcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxEaWFsb2cgb3Blbj17YWRkVG9TZWdtZW50RGlhbG9nT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRBZGRUb1NlZ21lbnREaWFsb2dPcGVufT5cbiAgICAgICAgICAgICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJzbTptYXgtdy1bNDI1cHhdXCI+XG4gICAgICAgICAgICAgICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RGlhbG9nVGl0bGU+QWRkIExlYWQgdG8gU2VnbWVudDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRW50ZXIgYSBuYW1lIGZvciB0aGUgc2VnbWVudCB0byBhZGQgdGhpcyBsZWFkIHRvLlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlZ21lbnQtbmFtZVwiPlNlZ21lbnQgTmFtZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2VnbWVudC1uYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlZ21lbnROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlZ21lbnROYW1lKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBIaWdoIFByaW9yaXR5IExlYWRzLCBRMSBQcm9zcGVjdHNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17YWRkaW5nVG9TZWdtZW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxEaWFsb2dGb290ZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFkZFRvU2VnbWVudERpYWxvZ09wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXthZGRpbmdUb1NlZ21lbnR9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDb25maXJtQWRkVG9TZWdtZW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshc2VnbWVudE5hbWUudHJpbSgpIHx8IGFkZGluZ1RvU2VnbWVudH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJnYXAtMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2FkZGluZ1RvU2VnbWVudCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMb2FkZXIgdGhlbWU9XCJkYXJrXCIgY2xhc3NOYW1lPVwic2l6ZS00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFkZGluZy4uLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoYXJ0TGluZUljb24gY2xhc3NOYW1lPVwic2l6ZS00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFkZCB0byBTZWdtZW50XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9EaWFsb2dGb290ZXI+XG4gICAgICAgICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgICAgICAgICAgPC9EaWFsb2c+XG4gICAgICAgIClcbiAgICB9XG5cbiAgICAvLyBTaGFyZWQgbW9kYWxzXG4gICAgY29uc3QgU2hhcmVkTW9kYWxzID0gKCkgPT4gKFxuICAgICAgICA8PlxuICAgICAgICAgICAgPFVubG9ja0VtYWlsTW9kYWxcbiAgICAgICAgICAgICAgICBvcGVuPXtlbWFpbE1vZGFsT3Blbn1cbiAgICAgICAgICAgICAgICBvbk9wZW5DaGFuZ2U9e3NldEVtYWlsTW9kYWxPcGVufVxuICAgICAgICAgICAgICAgIGxlYWRJZD17c2VsZWN0ZWRMZWFkSWR9XG4gICAgICAgICAgICAgICAgb25VbmxvY2tTdWNjZXNzPXsodW5sb2NrZWRMZWFkKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh1bmxvY2tFbWFpbENhbGxiYWNrUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHVubG9ja0VtYWlsQ2FsbGJhY2tSZWYuY3VycmVudCh1bmxvY2tlZExlYWQpXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxVbmxvY2tQaG9uZU1vZGFsXG4gICAgICAgICAgICAgICAgb3Blbj17cGhvbmVNb2RhbE9wZW59XG4gICAgICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRQaG9uZU1vZGFsT3Blbn1cbiAgICAgICAgICAgICAgICBsZWFkSWQ9e3NlbGVjdGVkTGVhZElkfVxuICAgICAgICAgICAgICAgIG9uVW5sb2NrU3VjY2Vzcz17KHVubG9ja2VkTGVhZCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAodW5sb2NrUGhvbmVDYWxsYmFja1JlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB1bmxvY2tQaG9uZUNhbGxiYWNrUmVmLmN1cnJlbnQodW5sb2NrZWRMZWFkKVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7c2VsZWN0ZWRMZWFkRm9yRGF0YWJhc2UgJiYgKFxuICAgICAgICAgICAgICAgIDxUd29TdGVwRGF0YWJhc2VNYXBwaW5nXG4gICAgICAgICAgICAgICAgICAgIG9wZW49e2FkZFRvRGF0YWJhc2VEaWFsb2dPcGVufVxuICAgICAgICAgICAgICAgICAgICBvbk9wZW5DaGFuZ2U9e3NldEFkZFRvRGF0YWJhc2VEaWFsb2dPcGVufVxuICAgICAgICAgICAgICAgICAgICBsZWFkPXtzZWxlY3RlZExlYWRGb3JEYXRhYmFzZX1cbiAgICAgICAgICAgICAgICAgICAgb25Db25maXJtPXtoYW5kbGVDb25maXJtQWRkVG9EYXRhYmFzZX1cbiAgICAgICAgICAgICAgICAgICAgbG9hZGluZz17YWRkaW5nVG9EYXRhYmFzZX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDxBZGRUb1NlZ21lbnREaWFsb2cgLz5cbiAgICAgICAgICAgIDxBZGRUb1dvcmtmbG93RGlhbG9nXG4gICAgICAgICAgICAgICAgb3Blbj17YWRkVG9Xb3JrZmxvd0RpYWxvZ09wZW59XG4gICAgICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRBZGRUb1dvcmtmbG93RGlhbG9nT3Blbn1cbiAgICAgICAgICAgICAgICBzZWxlY3RlZFdvcmtmbG93SWQ9e3NlbGVjdGVkV29ya2Zsb3dJZH1cbiAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFdvcmtmbG93SWQ9e3NldFNlbGVjdGVkV29ya2Zsb3dJZH1cbiAgICAgICAgICAgICAgICBhZGRpbmdUb1dvcmtmbG93PXthZGRpbmdUb1dvcmtmbG93fVxuICAgICAgICAgICAgICAgIGxvYWRpbmdXb3JrZmxvd3M9e2xvYWRpbmdXb3JrZmxvd3N9XG4gICAgICAgICAgICAgICAgYXZhaWxhYmxlV29ya2Zsb3dzPXthdmFpbGFibGVXb3JrZmxvd3N9XG4gICAgICAgICAgICAgICAgaGFuZGxlQ29uZmlybUFkZFRvV29ya2Zsb3c9e2hhbmRsZUNvbmZpcm1BZGRUb1dvcmtmbG93fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxTZW5kRW1haWxEaWFsb2cgLz5cbiAgICAgICAgPC8+XG4gICAgKVxuXG4gICAgcmV0dXJuIHtcbiAgICAgICAgLy8gU3RhdGVcbiAgICAgICAgc2VsZWN0ZWRMZWFkcyxcbiAgICAgICAgc2V0U2VsZWN0ZWRMZWFkcyxcbiAgICAgICAgZW1haWxNb2RhbE9wZW4sXG4gICAgICAgIHBob25lTW9kYWxPcGVuLFxuICAgICAgICBzZWxlY3RlZExlYWRJZCxcbiAgICAgICAgXG4gICAgICAgIC8vIFNlbGVjdGlvbiBoYW5kbGVyc1xuICAgICAgICBoYW5kbGVTZWxlY3RBbGwsXG4gICAgICAgIGhhbmRsZVNlbGVjdExlYWQsXG4gICAgICAgIFxuICAgICAgICAvLyBVbmxvY2sgaGFuZGxlcnNcbiAgICAgICAgaGFuZGxlVW5sb2NrRW1haWwsXG4gICAgICAgIGhhbmRsZVVubG9ja1Bob25lLFxuICAgICAgICBcbiAgICAgICAgLy8gTmF2aWdhdGlvbiBoYW5kbGVyc1xuICAgICAgICBoYW5kbGVOYW1lQ2xpY2ssXG4gICAgICAgIGhhbmRsZVZpZXdMaW5rcyxcbiAgICAgICAgXG4gICAgICAgIC8vIENvbnRhY3QgbGlua3NcbiAgICAgICAgZ2V0Q29udGFjdExpbmtzLFxuICAgICAgICBcbiAgICAgICAgLy8gSW1wb3J0IGhhbmRsZXJcbiAgICAgICAgaGFuZGxlSW1wb3J0TGVhZHMsXG4gICAgICAgIFxuICAgICAgICAvLyBBUEkgY29udmVyc2lvblxuICAgICAgICBjb252ZXJ0QXBpTGVhZHNUb1VJLFxuICAgICAgICBcbiAgICAgICAgLy8gRmlsdGVyIGxvZ2ljXG4gICAgICAgIGdldEZpbHRlcmVkTGVhZHMsXG4gICAgICAgIFxuICAgICAgICAvLyBMZWFkIGFjdGlvbnNcbiAgICAgICAgaGFuZGxlU2VuZEVtYWlsLFxuICAgICAgICBoYW5kbGVDb25maXJtU2VuZEVtYWlsLFxuICAgICAgICBoYW5kbGVBZGRUb1NlZ21lbnRzLFxuICAgICAgICBoYW5kbGVBZGRUb0RhdGFiYXNlLFxuICAgICAgICBoYW5kbGVBZGRUb1dvcmtmbG93LFxuICAgICAgICBoYW5kbGVDb25maXJtQWRkVG9EYXRhYmFzZSxcbiAgICAgICAgXG4gICAgICAgIC8vIERhdGFiYXNlIGRpYWxvZyBzdGF0ZVxuICAgICAgICBhZGRUb0RhdGFiYXNlRGlhbG9nT3BlbixcbiAgICAgICAgc2V0QWRkVG9EYXRhYmFzZURpYWxvZ09wZW4sXG4gICAgICAgIHNlbGVjdGVkTGVhZEZvckRhdGFiYXNlLFxuICAgICAgICBzZXRTZWxlY3RlZExlYWRGb3JEYXRhYmFzZSxcbiAgICAgICAgYWRkaW5nVG9EYXRhYmFzZSxcbiAgICAgICAgc2V0QWRkaW5nVG9EYXRhYmFzZSxcbiAgICAgICAgc2VsZWN0ZWRMZWFkSWRGb3JBY3Rpb24sXG4gICAgICAgIHNldFNlbGVjdGVkTGVhZElkRm9yQWN0aW9uLFxuICAgICAgICBcbiAgICAgICAgLy8gRW1haWwgZGlhbG9nIHN0YXRlXG4gICAgICAgIHNlbmRFbWFpbERpYWxvZ09wZW4sXG4gICAgICAgIHNldFNlbmRFbWFpbERpYWxvZ09wZW4sXG4gICAgICAgIGVtYWlsU3ViamVjdCxcbiAgICAgICAgc2V0RW1haWxTdWJqZWN0LFxuICAgICAgICBlbWFpbEJvZHksXG4gICAgICAgIHNldEVtYWlsQm9keSxcbiAgICAgICAgc2VuZGluZ0VtYWlsLFxuICAgICAgICBzZXRTZW5kaW5nRW1haWwsXG4gICAgICAgIHNlbGVjdGVkTGVhZEZvckVtYWlsLFxuICAgICAgICBzZXRTZWxlY3RlZExlYWRGb3JFbWFpbCxcbiAgICAgICAgXG4gICAgICAgIC8vIFNlZ21lbnQgZGlhbG9nIHN0YXRlXG4gICAgICAgIGFkZFRvU2VnbWVudERpYWxvZ09wZW4sXG4gICAgICAgIHNldEFkZFRvU2VnbWVudERpYWxvZ09wZW4sXG4gICAgICAgIHNlZ21lbnROYW1lLFxuICAgICAgICBzZXRTZWdtZW50TmFtZSxcbiAgICAgICAgYWRkaW5nVG9TZWdtZW50LFxuICAgICAgICBzZXRBZGRpbmdUb1NlZ21lbnQsXG4gICAgICAgIGhhbmRsZUNvbmZpcm1BZGRUb1NlZ21lbnQsXG4gICAgICAgIFxuICAgICAgICAvLyBXb3JrZmxvdyBkaWFsb2cgc3RhdGVcbiAgICAgICAgYWRkVG9Xb3JrZmxvd0RpYWxvZ09wZW4sXG4gICAgICAgIHNldEFkZFRvV29ya2Zsb3dEaWFsb2dPcGVuLFxuICAgICAgICBzZWxlY3RlZFdvcmtmbG93SWQsXG4gICAgICAgIHNldFNlbGVjdGVkV29ya2Zsb3dJZCxcbiAgICAgICAgYWRkaW5nVG9Xb3JrZmxvdyxcbiAgICAgICAgc2V0QWRkaW5nVG9Xb3JrZmxvdyxcbiAgICAgICAgYXZhaWxhYmxlV29ya2Zsb3dzLFxuICAgICAgICBzZXRBdmFpbGFibGVXb3JrZmxvd3MsXG4gICAgICAgIGxvYWRpbmdXb3JrZmxvd3MsXG4gICAgICAgIHNldExvYWRpbmdXb3JrZmxvd3MsXG4gICAgICAgIGhhbmRsZUNvbmZpcm1BZGRUb1dvcmtmbG93LFxuICAgICAgICBcbiAgICAgICAgLy8gQ2FsbGJhY2sgc2V0dGVyc1xuICAgICAgICBzZXRVbmxvY2tFbWFpbENhbGxiYWNrOiAoY2FsbGJhY2s6IChkYXRhOiBhbnkpID0+IHZvaWQpID0+IHtcbiAgICAgICAgICAgIHVubG9ja0VtYWlsQ2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrXG4gICAgICAgIH0sXG4gICAgICAgIHNldFVubG9ja1Bob25lQ2FsbGJhY2s6IChjYWxsYmFjazogKGRhdGE6IGFueSkgPT4gdm9pZCkgPT4ge1xuICAgICAgICAgICAgdW5sb2NrUGhvbmVDYWxsYmFja1JlZi5jdXJyZW50ID0gY2FsbGJhY2tcbiAgICAgICAgfSxcbiAgICAgICAgXG4gICAgICAgIC8vIENvbXBvbmVudHNcbiAgICAgICAgU2hhcmVkTW9kYWxzLFxuICAgICAgICBDb21wYW55VGFibGVIZWFkZXJcbiAgICB9XG59XG5cblxuXG5jb25zdCBDb21wYW5pZXMgPSAoeyBhY3RpdmVTdWJUYWIsIG9uTGVhZENyZWF0ZWQgfTogQ29tcGFuaWVzUHJvcHMpID0+IHtcbiAgICBjb25zdCB7IHdvcmtzcGFjZSB9ID0gdXNlV29ya3NwYWNlKClcbiAgICBjb25zdCB7IHRva2VuIH0gPSB1c2VBdXRoKClcbiAgICBcbiAgICAvLyBTaGFyZWQgc2lkZWJhciBzdGF0ZSB0aGF0IHBlcnNpc3RzIGFjcm9zcyB0YWIgc3dpdGNoZXNcbiAgICBjb25zdCBbc2lkZWJhck9wZW4sIHNldFNpZGViYXJPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgIFxuICAgIC8vIENlbnRyYWxpemVkIGxlYWQgbWFuYWdlbWVudCBhbmQgYWN0aW9uc1xuICAgIGNvbnN0IGxlYWRNYW5hZ2VtZW50ID0gdXNlTGVhZE1hbmFnZW1lbnQoKVxuICAgIFxuICAgIC8vIENyZWF0ZSBsZWFkQWN0aW9ucyBkaXJlY3RseSBmcm9tIGxlYWRNYW5hZ2VtZW50IHRvIGF2b2lkIGR1cGxpY2F0ZSBzdGF0ZVxuICAgIGNvbnN0IGxlYWRBY3Rpb25zID0ge1xuICAgICAgICBoYW5kbGVTZW5kRW1haWw6IGxlYWRNYW5hZ2VtZW50LmhhbmRsZVNlbmRFbWFpbCxcbiAgICAgICAgaGFuZGxlQWRkVG9TZWdtZW50czogbGVhZE1hbmFnZW1lbnQuaGFuZGxlQWRkVG9TZWdtZW50cyxcbiAgICAgICAgaGFuZGxlQWRkVG9EYXRhYmFzZTogbGVhZE1hbmFnZW1lbnQuaGFuZGxlQWRkVG9EYXRhYmFzZSxcbiAgICAgICAgaGFuZGxlQWRkVG9Xb3JrZmxvdzogbGVhZE1hbmFnZW1lbnQuaGFuZGxlQWRkVG9Xb3JrZmxvdyxcbiAgICAgICAgaGFuZGxlVW5sb2NrRW1haWw6IGxlYWRNYW5hZ2VtZW50LmhhbmRsZVVubG9ja0VtYWlsLFxuICAgICAgICBoYW5kbGVVbmxvY2tQaG9uZTogbGVhZE1hbmFnZW1lbnQuaGFuZGxlVW5sb2NrUGhvbmUsXG4gICAgfVxuICAgIFxuICAgIGNvbnN0IHNpZGViYXJTdGF0ZSA9IHtcbiAgICAgICAgaXNPcGVuOiBzaWRlYmFyT3BlbixcbiAgICAgICAgc2V0SXNPcGVuOiBzZXRTaWRlYmFyT3BlblxuICAgIH1cbiAgICBcbiAgICAvLyBTaGFyZWQgcHJvcHMgZm9yIGFsbCBzdWItY29tcG9uZW50c1xuICAgIGNvbnN0IHNoYXJlZFByb3BzID0ge1xuICAgICAgICBvbkxlYWRDcmVhdGVkLFxuICAgICAgICB0b2tlbjogdG9rZW4/LnRva2VuLFxuICAgICAgICB3b3Jrc3BhY2VJZDogd29ya3NwYWNlPy53b3Jrc3BhY2U/LmlkLFxuICAgICAgICAvLyBQYXNzIHNoYXJlZCBjb21wb25lbnRzIGFuZCBhY3Rpb25zXG4gICAgICAgIEFjdGlvbkJ1dHRvbixcbiAgICAgICAgVmlld0xpbmtzTW9kYWwsXG4gICAgICAgIExlYWRBY3Rpb25zRHJvcGRvd24sXG4gICAgICAgIGxlYWRBY3Rpb25zLFxuICAgICAgICBsZWFkTWFuYWdlbWVudFxuICAgIH1cbiAgICBcbiAgICBjb25zdCByZW5kZXJDb250ZW50ID0gKCkgPT4ge1xuICAgICAgICBzd2l0Y2ggKGFjdGl2ZVN1YlRhYikge1xuICAgICAgICAgICAgY2FzZSAnbXktbGVhZHMnOlxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TXlMZWFkcyB7Li4uc2hhcmVkUHJvcHN9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGVhZE1hbmFnZW1lbnQuU2hhcmVkTW9kYWxzIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgIGNhc2UgJ2ZpbmQtbGVhZHMnOlxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RmluZExlYWRzIHsuLi5zaGFyZWRQcm9wc30gc2lkZWJhclN0YXRlPXtzaWRlYmFyU3RhdGV9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGVhZE1hbmFnZW1lbnQuU2hhcmVkTW9kYWxzIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgIGNhc2UgJ3NhdmVkLXNlYXJjaCc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTYXZlZFNlYXJjaCB7Li4uc2hhcmVkUHJvcHN9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGVhZE1hbmFnZW1lbnQuU2hhcmVkTW9kYWxzIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxNeUxlYWRzIHsuLi5zaGFyZWRQcm9wc30gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsZWFkTWFuYWdlbWVudC5TaGFyZWRNb2RhbHMgLz5cbiAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlbmRlckNvbnRlbnQoKVxufVxuXG5leHBvcnQgZGVmYXVsdCBDb21wYW5pZXMiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZU1lbW8iLCJNeUxlYWRzIiwiRmluZExlYWRzIiwiU2F2ZWRTZWFyY2giLCJ1c2VBdXRoIiwidXNlV29ya3NwYWNlIiwiQnV0dG9uIiwiUG9wb3ZlciIsIlBvcG92ZXJDb250ZW50IiwiUG9wb3ZlclRyaWdnZXIiLCJEcm9wZG93bk1lbnUiLCJEcm9wZG93bk1lbnVDb250ZW50IiwiRHJvcGRvd25NZW51SXRlbSIsIkRyb3Bkb3duTWVudVRyaWdnZXIiLCJEcm9wZG93bk1lbnVHcm91cCIsIkVudmVsb3BlSWNvbiIsIkNoYXJ0TGluZUljb24iLCJEYXRhYmFzZUljb24iLCJDb2RlTWVyZ2VJY29uIiwiVXBSaWdodEZyb21TcXVhcmVJY29uIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nRGVzY3JpcHRpb24iLCJEaWFsb2dGb290ZXIiLCJUYWJsZUhlYWQiLCJUYWJsZUhlYWRlciIsIlRhYmxlUm93IiwiQ2hlY2tib3giLCJJbnB1dCIsIlRleHRhcmVhIiwiTGFiZWwiLCJhZGRMZWFkVG9EYXRhYmFzZSIsImFkZExlYWRUb1NlZ21lbnQiLCJhZGRMZWFkVG9Xb3JrZmxvdyIsInNlbmRFbWFpbFRvTGVhZCIsImdldFdvcmtmbG93cyIsIldvcmtmbG93VHJpZ2dlclR5cGUiLCJ1c2VBbGVydCIsInVzZVJvdXRlciIsInVzZVBhcmFtcyIsIlVubG9ja0VtYWlsTW9kYWwiLCJVbmxvY2tQaG9uZU1vZGFsIiwiVHdvU3RlcERhdGFiYXNlTWFwcGluZyIsIkxvYWRlciIsIkFjdGlvbkJ1dHRvbiIsImljb24iLCJJY29uIiwiY2hpbGRyZW4iLCJvbkNsaWNrIiwidmFyaWFudCIsInNpemUiLCJjbGFzc05hbWUiLCJzcGFuIiwiVmlld0xpbmtzTW9kYWwiLCJ0cmlnZ2VyIiwibGlua3MiLCJpc09wZW4iLCJzZXRJc09wZW4iLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwiYXNDaGlsZCIsImFsaWduIiwiaDQiLCJkaXYiLCJsZW5ndGgiLCJtYXAiLCJsaW5rIiwiYSIsImhyZWYiLCJ1cmwiLCJ0YXJnZXQiLCJyZWwiLCJ0aXRsZSIsImlkIiwiTGVhZEFjdGlvbnNEcm9wZG93biIsIm9uU2VuZEVtYWlsIiwib25BZGRUb1NlZ21lbnRzIiwib25BZGRUb0RhdGFiYXNlIiwib25BZGRUb1dvcmtmbG93IiwibGVhZERhdGEiLCJlbWFpbCIsIm5vcm1hbGl6ZWREYXRhIiwiYXBvbGxvRGF0YSIsImhhc0VtYWlsIiwic2lkZU9mZnNldCIsInVuZGVmaW5lZCIsImRpc2FibGVkIiwiQ29tcGFueVRhYmxlSGVhZGVyIiwic2VsZWN0ZWRMZWFkcyIsImZpbHRlcmVkTGVhZHMiLCJoYW5kbGVTZWxlY3RBbGwiLCJjaGVja2VkIiwib25DaGVja2VkQ2hhbmdlIiwidXNlTGVhZE1hbmFnZW1lbnQiLCJ0b2FzdCIsInRva2VuIiwid29ya3NwYWNlIiwicm91dGVyIiwicGFyYW1zIiwic2V0U2VsZWN0ZWRMZWFkcyIsImVtYWlsTW9kYWxPcGVuIiwic2V0RW1haWxNb2RhbE9wZW4iLCJwaG9uZU1vZGFsT3BlbiIsInNldFBob25lTW9kYWxPcGVuIiwic2VsZWN0ZWRMZWFkSWQiLCJzZXRTZWxlY3RlZExlYWRJZCIsInVubG9ja0VtYWlsQ2FsbGJhY2tSZWYiLCJ1bmxvY2tQaG9uZUNhbGxiYWNrUmVmIiwiYWRkVG9EYXRhYmFzZURpYWxvZ09wZW4iLCJzZXRBZGRUb0RhdGFiYXNlRGlhbG9nT3BlbiIsInNlbGVjdGVkTGVhZEZvckRhdGFiYXNlIiwic2V0U2VsZWN0ZWRMZWFkRm9yRGF0YWJhc2UiLCJhZGRpbmdUb0RhdGFiYXNlIiwic2V0QWRkaW5nVG9EYXRhYmFzZSIsInNlbGVjdGVkTGVhZElkRm9yQWN0aW9uIiwic2V0U2VsZWN0ZWRMZWFkSWRGb3JBY3Rpb24iLCJzZW5kRW1haWxEaWFsb2dPcGVuIiwic2V0U2VuZEVtYWlsRGlhbG9nT3BlbiIsImVtYWlsU3ViamVjdCIsInNldEVtYWlsU3ViamVjdCIsImVtYWlsQm9keSIsInNldEVtYWlsQm9keSIsInNlbmRpbmdFbWFpbCIsInNldFNlbmRpbmdFbWFpbCIsInNlbGVjdGVkTGVhZEZvckVtYWlsIiwic2V0U2VsZWN0ZWRMZWFkRm9yRW1haWwiLCJhZGRUb1NlZ21lbnREaWFsb2dPcGVuIiwic2V0QWRkVG9TZWdtZW50RGlhbG9nT3BlbiIsInNlZ21lbnROYW1lIiwic2V0U2VnbWVudE5hbWUiLCJhZGRpbmdUb1NlZ21lbnQiLCJzZXRBZGRpbmdUb1NlZ21lbnQiLCJhZGRUb1dvcmtmbG93RGlhbG9nT3BlbiIsInNldEFkZFRvV29ya2Zsb3dEaWFsb2dPcGVuIiwic2VsZWN0ZWRXb3JrZmxvd0lkIiwic2V0U2VsZWN0ZWRXb3JrZmxvd0lkIiwiYWRkaW5nVG9Xb3JrZmxvdyIsInNldEFkZGluZ1RvV29ya2Zsb3ciLCJhdmFpbGFibGVXb3JrZmxvd3MiLCJzZXRBdmFpbGFibGVXb3JrZmxvd3MiLCJsb2FkaW5nV29ya2Zsb3dzIiwic2V0TG9hZGluZ1dvcmtmbG93cyIsImxlYWRzIiwibGVhZCIsImhhbmRsZVNlbGVjdExlYWQiLCJsZWFkSWQiLCJmaWx0ZXIiLCJoYW5kbGVVbmxvY2tFbWFpbCIsImhhbmRsZVVubG9ja1Bob25lIiwiaGFuZGxlTmFtZUNsaWNrIiwiZG9tYWluIiwicHVzaCIsImhhbmRsZVZpZXdMaW5rcyIsImdldENvbnRhY3RMaW5rcyIsIm5hbWUiLCJlbmNvZGVVUklDb21wb25lbnQiLCJjb21wYW55IiwiaGFuZGxlSW1wb3J0TGVhZHMiLCJjb252ZXJ0QXBpTGVhZHNUb1VJIiwiYXBpTGVhZHMiLCJhcGlMZWFkIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImNvbnNvbGUiLCJsb2ciLCJoYXNOb3JtYWxpemVkRGF0YSIsImhhc0Fwb2xsb0RhdGEiLCJpc0VtYWlsVmlzaWJsZSIsImlzUGhvbmVWaXNpYmxlIiwicGhvbmUiLCJpc1BlcnNvbkRhdGEiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwicGVyc29uIiwiaGFzRmlyc3ROYW1lIiwiaGFzTGFzdE5hbWUiLCJoYXNQZXJzb24iLCJoYXNUaXRsZSIsImhhc0luZHVzdHJ5IiwiaW5kdXN0cnkiLCJsb2NhdGlvbiIsIm9yZ2FuaXphdGlvbiIsImNpdHkiLCJzdGF0ZSIsImNvdW50cnkiLCJCb29sZWFuIiwiam9pbiIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwidHJpbSIsImNvbXBhbnlfbmFtZSIsImxvYyIsImdldEZpbHRlcmVkTGVhZHMiLCJzZWFyY2hRdWVyeSIsImZpbHRlcmVkIiwic2VhcmNoVGVybSIsIk9iamVjdCIsInZhbHVlcyIsInNvbWUiLCJ2YWx1ZSIsImNvbmRpdGlvbnMiLCJldmVyeSIsImNvbmRpdGlvbiIsInRvU3RyaW5nIiwibGVhZFZhbHVlIiwiY29sdW1uSWQiLCJoYW5kbGVTZW5kRW1haWwiLCJlcnJvciIsImhhbmRsZUNvbmZpcm1TZW5kRW1haWwiLCJyZXNwb25zZSIsInN1YmplY3QiLCJib2R5IiwiaXNTdWNjZXNzIiwic3VjY2VzcyIsImhhbmRsZUFkZFRvU2VnbWVudHMiLCJoYW5kbGVDb25maXJtQWRkVG9TZWdtZW50IiwiaGFuZGxlQWRkVG9EYXRhYmFzZSIsImhhbmRsZUNvbmZpcm1BZGRUb0RhdGFiYXNlIiwibWFwcGluZ3MiLCJkYXRhYmFzZUlkIiwidGFyZ2V0RGF0YWJhc2VJZCIsImhhbmRsZUFkZFRvV29ya2Zsb3ciLCJkYXRhIiwid29ya2Zsb3dzIiwib25EZW1hbmRXb3JrZmxvd3MiLCJ3IiwidHJpZ2dlclR5cGUiLCJPbkRlbWFuZF9DYWxsYWJsZSIsImhhbmRsZUNvbmZpcm1BZGRUb1dvcmtmbG93IiwicmVzdWx0Iiwid29ya2Zsb3dJZCIsIm1lc3NhZ2UiLCJzdGF0dXMiLCJBZGRUb1dvcmtmbG93RGlhbG9nIiwibGFiZWwiLCJzZWxlY3QiLCJvbkNoYW5nZSIsImUiLCJvcHRpb24iLCJ3b3JrZmxvdyIsInRoZW1lIiwiU2VuZEVtYWlsRGlhbG9nIiwicCIsImh0bWxGb3IiLCJwbGFjZWhvbGRlciIsInJvd3MiLCJBZGRUb1NlZ21lbnREaWFsb2ciLCJTaGFyZWRNb2RhbHMiLCJvblVubG9ja1N1Y2Nlc3MiLCJ1bmxvY2tlZExlYWQiLCJjdXJyZW50Iiwib25Db25maXJtIiwibG9hZGluZyIsInNldFVubG9ja0VtYWlsQ2FsbGJhY2siLCJjYWxsYmFjayIsInNldFVubG9ja1Bob25lQ2FsbGJhY2siLCJDb21wYW5pZXMiLCJhY3RpdmVTdWJUYWIiLCJvbkxlYWRDcmVhdGVkIiwic2lkZWJhck9wZW4iLCJzZXRTaWRlYmFyT3BlbiIsImxlYWRNYW5hZ2VtZW50IiwibGVhZEFjdGlvbnMiLCJzaWRlYmFyU3RhdGUiLCJzaGFyZWRQcm9wcyIsIndvcmtzcGFjZUlkIiwicmVuZGVyQ29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui/src/components/workspace/main/lead-generation/company/index.tsx\n"));

/***/ })

});