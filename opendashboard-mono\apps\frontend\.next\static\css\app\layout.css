/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Space+Grotesk:wght@300..700&display=swap');
/*@import url('assets/fonts/fontawesome/css/fontawesome.min.css');*/
/*@import url('assets/fonts/fontawesome/css/light.min.css');*/
/*@import url('assets/fonts/fontawesome/css/regular.min.css');*/
/*@import url('assets/fonts/fontawesome/css/sharp-light.min.css');*/
/*@import url('assets/fonts/fontawesome/css/sharp-regular.min.css');*/
*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
/*! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com*/
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
:root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;

    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;

    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }
.dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;

    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;

    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;

    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;

    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;

    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
/* Charts */
:root {
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }
.dark {
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
*{
  border-color: hsl(var(--border));
}
body{
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}
@media (min-width: 1400px){
  .container{
    max-width: 1400px;
  }
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.visible{
  visibility: visible;
}
.\!invisible{
  visibility: hidden !important;
}
.invisible{
  visibility: hidden;
}
.\!collapse{
  visibility: collapse !important;
}
.collapse{
  visibility: collapse;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.inset-x-0{
  left: 0px;
  right: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.\!-left-1{
  left: -0.25rem !important;
}
.\!-right-1{
  right: -0.25rem !important;
}
.-bottom-1{
  bottom: -0.25rem;
}
.-bottom-12{
  bottom: -3rem;
}
.-bottom-2{
  bottom: -0.5rem;
}
.-left-1{
  left: -0.25rem;
}
.-left-12{
  left: -3rem;
}
.-left-2{
  left: -0.5rem;
}
.-left-3{
  left: -0.75rem;
}
.-right-1{
  right: -0.25rem;
}
.-right-12{
  right: -3rem;
}
.-right-2{
  right: -0.5rem;
}
.-right-4{
  right: -1rem;
}
.-right-5{
  right: -1.25rem;
}
.-right-8{
  right: -2rem;
}
.-top-0\.5{
  top: -0.125rem;
}
.-top-1{
  top: -0.25rem;
}
.-top-12{
  top: -3rem;
}
.-top-2{
  top: -0.5rem;
}
.-top-3{
  top: -0.75rem;
}
.-top-4{
  top: -1rem;
}
.-top-6{
  top: -1.5rem;
}
.-top-7{
  top: -1.75rem;
}
.-top-8{
  top: -2rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-1{
  bottom: 0.25rem;
}
.bottom-2{
  bottom: 0.5rem;
}
.bottom-4{
  bottom: 1rem;
}
.bottom-8{
  bottom: 2rem;
}
.bottom-\[-11px\]{
  bottom: -11px;
}
.left-0{
  left: 0px;
}
.left-1\.5{
  left: 0.375rem;
}
.left-1\/2{
  left: 50%;
}
.left-14{
  left: 3.5rem;
}
.left-2{
  left: 0.5rem;
}
.left-2\.5{
  left: 0.625rem;
}
.left-3{
  left: 0.75rem;
}
.left-4{
  left: 1rem;
}
.left-8{
  left: 2rem;
}
.left-\[-11px\]{
  left: -11px;
}
.left-\[-45px\]{
  left: -45px;
}
.left-\[11px\]{
  left: 11px;
}
.left-\[253px\]{
  left: 253px;
}
.left-\[50\%\]{
  left: 50%;
}
.right-0{
  right: 0px;
}
.right-1{
  right: 0.25rem;
}
.right-12{
  right: 3rem;
}
.right-2{
  right: 0.5rem;
}
.right-3{
  right: 0.75rem;
}
.right-4{
  right: 1rem;
}
.right-8{
  right: 2rem;
}
.right-\[-10px\]{
  right: -10px;
}
.right-\[-11px\]{
  right: -11px;
}
.top-0{
  top: 0px;
}
.top-0\.5{
  top: 0.125rem;
}
.top-1{
  top: 0.25rem;
}
.top-1\.5{
  top: 0.375rem;
}
.top-1\/2{
  top: 50%;
}
.top-10{
  top: 2.5rem;
}
.top-16{
  top: 4rem;
}
.top-2{
  top: 0.5rem;
}
.top-2\.5{
  top: 0.625rem;
}
.top-3{
  top: 0.75rem;
}
.top-3\.5{
  top: 0.875rem;
}
.top-32{
  top: 8rem;
}
.top-4{
  top: 1rem;
}
.top-7{
  top: 1.75rem;
}
.top-8{
  top: 2rem;
}
.top-\[-11px\]{
  top: -11px;
}
.top-\[-12px\]{
  top: -12px;
}
.top-\[-45px\]{
  top: -45px;
}
.top-\[-5px\]{
  top: -5px;
}
.top-\[30px\]{
  top: 30px;
}
.top-\[50\%\]{
  top: 50%;
}
.top-full{
  top: 100%;
}
.isolate{
  isolation: isolate;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-30{
  z-index: 30;
}
.z-50{
  z-index: 50;
}
.z-\[-9999999\]{
  z-index: -9999999;
}
.z-\[-9999\]{
  z-index: -9999;
}
.z-\[100\]{
  z-index: 100;
}
.z-\[200\]{
  z-index: 200;
}
.z-\[250\]{
  z-index: 250;
}
.z-\[999999\]{
  z-index: 999999;
}
.z-\[99999\]{
  z-index: 99999;
}
.col-span-3{
  grid-column: span 3 / span 3;
}
.col-span-full{
  grid-column: 1 / -1;
}
.col-span-2{
  grid-column: span 2 / span 2;
}
.-m-1\.5{
  margin: -0.375rem;
}
.-m-2\.5{
  margin: -0.625rem;
}
.m-0{
  margin: 0px;
}
.-mx-0\.5{
  margin-left: -0.125rem;
  margin-right: -0.125rem;
}
.-mx-1{
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.-mx-1\.5{
  margin-left: -0.375rem;
  margin-right: -0.375rem;
}
.-mx-2{
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.-mx-3{
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}
.-my-6{
  margin-top: -1.5rem;
  margin-bottom: -1.5rem;
}
.mx-1{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3\.5{
  margin-left: 0.875rem;
  margin-right: 0.875rem;
}
.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-5{
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-0{
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-0\.5{
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-1\.5{
  margin-top: 0.375rem;
  margin-bottom: 0.375rem;
}
.my-10{
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
.my-2{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3{
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-8{
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.mx-3{
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.-mb-2{
  margin-bottom: -0.5rem;
}
.-mb-24{
  margin-bottom: -6rem;
}
.-mb-6{
  margin-bottom: -1.5rem;
}
.-ml-1{
  margin-left: -0.25rem;
}
.-ml-1\.5{
  margin-left: -0.375rem;
}
.-ml-2{
  margin-left: -0.5rem;
}
.-ml-3{
  margin-left: -0.75rem;
}
.-ml-4{
  margin-left: -1rem;
}
.-mr-1{
  margin-right: -0.25rem;
}
.-mr-1\.5{
  margin-right: -0.375rem;
}
.-mr-2{
  margin-right: -0.5rem;
}
.-mt-1{
  margin-top: -0.25rem;
}
.-mt-2{
  margin-top: -0.5rem;
}
.-mt-4{
  margin-top: -1rem;
}
.-mt-5{
  margin-top: -1.25rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-1\.5{
  margin-bottom: 0.375rem;
}
.mb-12{
  margin-bottom: 3rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-20{
  margin-bottom: 5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-2\.5{
  margin-left: 0.625rem;
}
.ml-auto{
  margin-left: auto;
}
.mr-0{
  margin-right: 0px;
}
.mr-0\.5{
  margin-right: 0.125rem;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-1\.5{
  margin-right: 0.375rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-2\.5{
  margin-right: 0.625rem;
}
.ms-1{
  margin-inline-start: 0.25rem;
}
.ms-3{
  margin-inline-start: 0.75rem;
}
.ms-auto{
  margin-inline-start: auto;
}
.mt-0{
  margin-top: 0px;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-1\.5{
  margin-top: 0.375rem;
}
.mt-10{
  margin-top: 2.5rem;
}
.mt-12{
  margin-top: 3rem;
}
.mt-16{
  margin-top: 4rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-20{
  margin-top: 5rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.mt-auto{
  margin-top: auto;
}
.line-clamp-1{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.\!block{
  display: block !important;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.flow-root{
  display: flow-root;
}
.grid{
  display: grid;
}
.contents{
  display: contents;
}
.list-item{
  display: list-item;
}
.hidden{
  display: none;
}
.aspect-square{
  aspect-ratio: 1 / 1;
}
.aspect-video{
  aspect-ratio: 16 / 9;
}
.\!size-3{
  width: 0.75rem !important;
  height: 0.75rem !important;
}
.\!size-6{
  width: 1.5rem !important;
  height: 1.5rem !important;
}
.\!size-8{
  width: 2rem !important;
  height: 2rem !important;
}
.size-0\.5{
  width: 0.125rem;
  height: 0.125rem;
}
.size-1\.5{
  width: 0.375rem;
  height: 0.375rem;
}
.size-10{
  width: 2.5rem;
  height: 2.5rem;
}
.size-11{
  width: 2.75rem;
  height: 2.75rem;
}
.size-12{
  width: 3rem;
  height: 3rem;
}
.size-16{
  width: 4rem;
  height: 4rem;
}
.size-2{
  width: 0.5rem;
  height: 0.5rem;
}
.size-2\.5{
  width: 0.625rem;
  height: 0.625rem;
}
.size-20{
  width: 5rem;
  height: 5rem;
}
.size-3{
  width: 0.75rem;
  height: 0.75rem;
}
.size-3\.5{
  width: 0.875rem;
  height: 0.875rem;
}
.size-4{
  width: 1rem;
  height: 1rem;
}
.size-5{
  width: 1.25rem;
  height: 1.25rem;
}
.size-6{
  width: 1.5rem;
  height: 1.5rem;
}
.size-7{
  width: 1.75rem;
  height: 1.75rem;
}
.size-8{
  width: 2rem;
  height: 2rem;
}
.size-9{
  width: 2.25rem;
  height: 2.25rem;
}
.size-96{
  width: 24rem;
  height: 24rem;
}
.size-\[21px\]{
  width: 21px;
  height: 21px;
}
.size-auto{
  width: auto;
  height: auto;
}
.size-full{
  width: 100%;
  height: 100%;
}
.\!h-0{
  height: 0px !important;
}
.\!h-8{
  height: 2rem !important;
}
.\!h-auto{
  height: auto !important;
}
.h-0{
  height: 0px;
}
.h-0\.5{
  height: 0.125rem;
}
.h-1{
  height: 0.25rem;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-11{
  height: 2.75rem;
}
.h-12{
  height: 3rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-20{
  height: 5rem;
}
.h-24{
  height: 6rem;
}
.h-28{
  height: 7rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-3\/4{
  height: 75%;
}
.h-32{
  height: 8rem;
}
.h-36{
  height: 9rem;
}
.h-4{
  height: 1rem;
}
.h-4\/5{
  height: 80%;
}
.h-40{
  height: 10rem;
}
.h-48{
  height: 12rem;
}
.h-5{
  height: 1.25rem;
}
.h-52{
  height: 13rem;
}
.h-56{
  height: 14rem;
}
.h-6{
  height: 1.5rem;
}
.h-60{
  height: 15rem;
}
.h-64{
  height: 16rem;
}
.h-7{
  height: 1.75rem;
}
.h-72{
  height: 18rem;
}
.h-8{
  height: 2rem;
}
.h-80{
  height: 20rem;
}
.h-9{
  height: 2.25rem;
}
.h-96{
  height: 24rem;
}
.h-\[1px\]{
  height: 1px;
}
.h-\[20px\]{
  height: 20px;
}
.h-\[21px\]{
  height: 21px;
}
.h-\[250px\]{
  height: 250px;
}
.h-\[280px\]{
  height: 280px;
}
.h-\[302px\]{
  height: 302px;
}
.h-\[32px\]{
  height: 32px;
}
.h-\[330px\]{
  height: 330px;
}
.h-\[36px\]{
  height: 36px;
}
.h-\[400px\]{
  height: 400px;
}
.h-\[40px\]{
  height: 40px;
}
.h-\[540px\]{
  height: 540px;
}
.h-\[72px\]{
  height: 72px;
}
.h-\[calc\(100\%-20px\)\]{
  height: calc(100% - 20px);
}
.h-\[calc\(100vh-100px\)\]{
  height: calc(100vh - 100px);
}
.h-\[calc\(100vh-50px\)\]{
  height: calc(100vh - 50px);
}
.h-\[calc\(100vh-64px\)\]{
  height: calc(100vh - 64px);
}
.h-\[var\(--radix-select-trigger-height\)\]{
  height: var(--radix-select-trigger-height);
}
.h-auto{
  height: auto;
}
.h-fit{
  height: -moz-fit-content;
  height: fit-content;
}
.h-full{
  height: 100%;
}
.h-px{
  height: 1px;
}
.h-screen{
  height: 100vh;
}
.h-svh{
  height: 100svh;
}
.\!max-h-full{
  max-height: 100% !important;
}
.max-h-36{
  max-height: 9rem;
}
.max-h-48{
  max-height: 12rem;
}
.max-h-56{
  max-height: 14rem;
}
.max-h-60{
  max-height: 15rem;
}
.max-h-64{
  max-height: 16rem;
}
.max-h-72{
  max-height: 18rem;
}
.max-h-80{
  max-height: 20rem;
}
.max-h-96{
  max-height: 24rem;
}
.max-h-\[300px\]{
  max-height: 300px;
}
.max-h-\[500px\]{
  max-height: 500px;
}
.max-h-\[50vh\]{
  max-height: 50vh;
}
.max-h-\[550px\]{
  max-height: 550px;
}
.max-h-\[600px\]{
  max-height: 600px;
}
.max-h-\[60vh\]{
  max-height: 60vh;
}
.max-h-\[80vh\]{
  max-height: 80vh;
}
.max-h-\[85vh\]{
  max-height: 85vh;
}
.max-h-\[90vh\]{
  max-height: 90vh;
}
.max-h-\[calc\(85vh-120px\)\]{
  max-height: calc(85vh - 120px);
}
.max-h-\[calc\(90vh-120px\)\]{
  max-height: calc(90vh - 120px);
}
.max-h-full{
  max-height: 100%;
}
.max-h-20{
  max-height: 5rem;
}
.min-h-0{
  min-height: 0px;
}
.min-h-12{
  min-height: 3rem;
}
.min-h-20{
  min-height: 5rem;
}
.min-h-24{
  min-height: 6rem;
}
.min-h-32{
  min-height: 8rem;
}
.min-h-40{
  min-height: 10rem;
}
.min-h-52{
  min-height: 13rem;
}
.min-h-7{
  min-height: 1.75rem;
}
.min-h-72{
  min-height: 18rem;
}
.min-h-8{
  min-height: 2rem;
}
.min-h-\[100px\]{
  min-height: 100px;
}
.min-h-\[170px\]{
  min-height: 170px;
}
.min-h-\[200px\]{
  min-height: 200px;
}
.min-h-\[400px\]{
  min-height: 400px;
}
.min-h-\[40px\]{
  min-height: 40px;
}
.min-h-\[450px\]{
  min-height: 450px;
}
.min-h-\[500px\]{
  min-height: 500px;
}
.min-h-\[60px\]{
  min-height: 60px;
}
.min-h-\[650px\]{
  min-height: 650px;
}
.min-h-\[75px\]{
  min-height: 75px;
}
.min-h-\[80px\]{
  min-height: 80px;
}
.min-h-\[calc\(100\%-100px\)\]{
  min-height: calc(100% - 100px);
}
.min-h-full{
  min-height: 100%;
}
.min-h-screen{
  min-height: 100vh;
}
.min-h-svh{
  min-height: 100svh;
}
.\!w-0{
  width: 0px !important;
}
.\!w-1{
  width: 0.25rem !important;
}
.\!w-32{
  width: 8rem !important;
}
.\!w-\[50vw\]{
  width: 50vw !important;
}
.w-0{
  width: 0px;
}
.w-0\.5{
  width: 0.125rem;
}
.w-1{
  width: 0.25rem;
}
.w-1\.5{
  width: 0.375rem;
}
.w-1\/3{
  width: 33.333333%;
}
.w-1\/5{
  width: 20%;
}
.w-10{
  width: 2.5rem;
}
.w-12{
  width: 3rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\.5{
  width: 0.625rem;
}
.w-2\/5{
  width: 40%;
}
.w-20{
  width: 5rem;
}
.w-24{
  width: 6rem;
}
.w-28{
  width: 7rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-3\/4{
  width: 75%;
}
.w-3\/5{
  width: 60%;
}
.w-32{
  width: 8rem;
}
.w-36{
  width: 9rem;
}
.w-4{
  width: 1rem;
}
.w-40{
  width: 10rem;
}
.w-44{
  width: 11rem;
}
.w-48{
  width: 12rem;
}
.w-5{
  width: 1.25rem;
}
.w-56{
  width: 14rem;
}
.w-6{
  width: 1.5rem;
}
.w-60{
  width: 15rem;
}
.w-64{
  width: 16rem;
}
.w-7{
  width: 1.75rem;
}
.w-72{
  width: 18rem;
}
.w-8{
  width: 2rem;
}
.w-80{
  width: 20rem;
}
.w-9{
  width: 2.25rem;
}
.w-96{
  width: 24rem;
}
.w-\[--sidebar-width\]{
  width: var(--sidebar-width);
}
.w-\[100px\]{
  width: 100px;
}
.w-\[1px\]{
  width: 1px;
}
.w-\[20\%\]{
  width: 20%;
}
.w-\[200px\]{
  width: 200px;
}
.w-\[20px\]{
  width: 20px;
}
.w-\[250px\]{
  width: 250px;
}
.w-\[2px\]{
  width: 2px;
}
.w-\[300px\]{
  width: 300px;
}
.w-\[330px\]{
  width: 330px;
}
.w-\[40px\]{
  width: 40px;
}
.w-\[420px\]{
  width: 420px;
}
.w-\[450px\]{
  width: 450px;
}
.w-\[500px\]{
  width: 500px;
}
.w-\[600px\]{
  width: 600px;
}
.w-\[650px\]{
  width: 650px;
}
.w-\[70px\]{
  width: 70px;
}
.w-\[800px\]{
  width: 800px;
}
.w-\[95vw\]{
  width: 95vw;
}
.w-\[calc\(100vw-100px\)\]{
  width: calc(100vw - 100px);
}
.w-auto{
  width: auto;
}
.w-fit{
  width: -moz-fit-content;
  width: fit-content;
}
.w-full{
  width: 100%;
}
.w-min{
  width: -moz-min-content;
  width: min-content;
}
.w-px{
  width: 1px;
}
.w-screen{
  width: 100vw;
}
.\!min-w-\[400px\]{
  min-width: 400px !important;
}
.min-w-0{
  min-width: 0px;
}
.min-w-14{
  min-width: 3.5rem;
}
.min-w-4{
  min-width: 1rem;
}
.min-w-40{
  min-width: 10rem;
}
.min-w-5{
  min-width: 1.25rem;
}
.min-w-56{
  min-width: 14rem;
}
.min-w-6{
  min-width: 1.5rem;
}
.min-w-72{
  min-width: 18rem;
}
.min-w-80{
  min-width: 20rem;
}
.min-w-\[200px\]{
  min-width: 200px;
}
.min-w-\[400px\]{
  min-width: 400px;
}
.min-w-\[450px\]{
  min-width: 450px;
}
.min-w-\[600px\]{
  min-width: 600px;
}
.min-w-\[700px\]{
  min-width: 700px;
}
.min-w-\[8rem\]{
  min-width: 8rem;
}
.min-w-\[calc\(100vw-3\.5rem\)\]{
  min-width: calc(100vw - 3.5rem);
}
.min-w-\[var\(--radix-select-trigger-width\)\]{
  min-width: var(--radix-select-trigger-width);
}
.min-w-full{
  min-width: 100%;
}
.\!max-w-32{
  max-width: 8rem !important;
}
.\!max-w-full{
  max-width: 100% !important;
}
.max-w-24{
  max-width: 6rem;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-32{
  max-width: 8rem;
}
.max-w-48{
  max-width: 12rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-52{
  max-width: 13rem;
}
.max-w-5xl{
  max-width: 64rem;
}
.max-w-64{
  max-width: 16rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-80{
  max-width: 20rem;
}
.max-w-96{
  max-width: 24rem;
}
.max-w-\[--skeleton-width\]{
  max-width: var(--skeleton-width);
}
.max-w-\[1000px\]{
  max-width: 1000px;
}
.max-w-\[120px\]{
  max-width: 120px;
}
.max-w-\[350px\]{
  max-width: 350px;
}
.max-w-\[450px\]{
  max-width: 450px;
}
.max-w-\[500px\]{
  max-width: 500px;
}
.max-w-\[60\%\]{
  max-width: 60%;
}
.max-w-\[600px\]{
  max-width: 600px;
}
.max-w-\[640px\]{
  max-width: 640px;
}
.max-w-\[650px\]{
  max-width: 650px;
}
.max-w-\[70\%\]{
  max-width: 70%;
}
.max-w-\[700px\]{
  max-width: 700px;
}
.max-w-\[750px\]{
  max-width: 750px;
}
.max-w-\[800px\]{
  max-width: 800px;
}
.max-w-\[850px\]{
  max-width: 850px;
}
.max-w-\[90vw\]{
  max-width: 90vw;
}
.max-w-\[950px\]{
  max-width: 950px;
}
.max-w-\[95vw\]{
  max-width: 95vw;
}
.max-w-\[960px\]{
  max-width: 960px;
}
.max-w-full{
  max-width: 100%;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-none{
  max-width: none;
}
.max-w-xl{
  max-width: 36rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-none{
  flex: none;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.shrink-0{
  flex-shrink: 0;
}
.grow{
  flex-grow: 1;
}
.grow-0{
  flex-grow: 0;
}
.basis-full{
  flex-basis: 100%;
}
.caption-bottom{
  caption-side: bottom;
}
.origin-\[--radix-tooltip-content-transform-origin\]{
  transform-origin: var(--radix-tooltip-content-transform-origin);
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-px{
  --tw-translate-x: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-10{
  --tw-translate-y: -2.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-10{
  --tw-translate-x: 2.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-px{
  --tw-translate-x: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1{
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4{
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\!transform-none{
  transform: none !important;
}
@keyframes pulse{
  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-col-resize{
  cursor: col-resize;
}
.cursor-default{
  cursor: default;
}
.cursor-move{
  cursor: move;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.touch-none{
  touch-action: none;
}
.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.select-auto{
  -webkit-user-select: auto;
     -moz-user-select: auto;
          user-select: auto;
}
.resize-none{
  resize: none;
}
.resize{
  resize: both;
}
.list-disc{
  list-style-type: disc;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-7{
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-col-reverse{
  flex-direction: column-reverse;
}
.flex-wrap{
  flex-wrap: wrap;
}
.flex-nowrap{
  flex-wrap: nowrap;
}
.content-center{
  align-content: center;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-baseline{
  align-items: baseline;
}
.items-stretch{
  align-items: stretch;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-0{
  gap: 0px;
}
.gap-0\.5{
  gap: 0.125rem;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-2\.5{
  gap: 0.625rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.gap-x-1{
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}
.gap-x-3{
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}
.gap-x-6{
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-x-8{
  -moz-column-gap: 2rem;
       column-gap: 2rem;
}
.gap-y-16{
  row-gap: 4rem;
}
.-space-x-px > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-0 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-500\/10 > :not([hidden]) ~ :not([hidden]){
  border-color: rgb(107 114 128 / 0.1);
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-visible{
  overflow: visible;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-x-hidden{
  overflow-x: hidden;
}
.overflow-y-hidden{
  overflow-y: hidden;
}
.\!truncate{
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis{
  text-overflow: ellipsis;
}
.whitespace-normal{
  white-space: normal;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.whitespace-break-spaces{
  white-space: break-spaces;
}
.text-nowrap{
  text-wrap: nowrap;
}
.break-words{
  overflow-wrap: break-word;
}
.\!rounded{
  border-radius: 0.25rem !important;
}
.\!rounded-full{
  border-radius: 9999px !important;
}
.\!rounded-none{
  border-radius: 0px !important;
}
.\!rounded-sm{
  border-radius: calc(var(--radius) - 4px) !important;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-3xl{
  border-radius: 1.5rem;
}
.rounded-\[10px\]{
  border-radius: 10px;
}
.rounded-\[2px\]{
  border-radius: 2px;
}
.rounded-\[50\%\]{
  border-radius: 50%;
}
.rounded-\[5px\]{
  border-radius: 5px;
}
.rounded-\[inherit\]{
  border-radius: inherit;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: var(--radius);
}
.rounded-md{
  border-radius: calc(var(--radius) - 2px);
}
.rounded-none{
  border-radius: 0px;
}
.rounded-sm{
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl{
  border-radius: 0.75rem;
}
.rounded-b{
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.rounded-l-md{
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.rounded-l-sm{
  border-top-left-radius: calc(var(--radius) - 4px);
  border-bottom-left-radius: calc(var(--radius) - 4px);
}
.rounded-r{
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.rounded-r-md{
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.rounded-r-sm{
  border-top-right-radius: calc(var(--radius) - 4px);
  border-bottom-right-radius: calc(var(--radius) - 4px);
}
.rounded-t{
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-t-lg{
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.rounded-bl-full{
  border-bottom-left-radius: 9999px;
}
.\!border-0{
  border-width: 0px !important;
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-\[\.5px\]{
  border-width: .5px;
}
.border-\[1\.5px\]{
  border-width: 1.5px;
}
.\!border-b{
  border-bottom-width: 1px !important;
}
.\!border-r-0{
  border-right-width: 0px !important;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-0{
  border-bottom-width: 0px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-b-\[6px\]{
  border-bottom-width: 6px;
}
.border-l{
  border-left-width: 1px;
}
.border-l-2{
  border-left-width: 2px;
}
.border-l-\[8px\]{
  border-left-width: 8px;
}
.border-r{
  border-right-width: 1px;
}
.border-r-0{
  border-right-width: 0px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-0{
  border-top-width: 0px;
}
.border-t-2{
  border-top-width: 2px;
}
.border-t-\[6px\]{
  border-top-width: 6px;
}
.border-solid{
  border-style: solid;
}
.border-dashed{
  border-style: dashed;
}
.border-none{
  border-style: none;
}
.border-\[\#b3b3b3\]{
  --tw-border-opacity: 1;
  border-color: rgb(179 179 179 / var(--tw-border-opacity, 1));
}
.border-\[--color-border\]{
  border-color: var(--color-border);
}
.border-black{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-300{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-blue-400{
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-600{
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-border\/50{
  border-color: hsl(var(--border) / 0.5);
}
.border-destructive{
  border-color: hsl(var(--destructive));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-900{
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.border-green-200{
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-input{
  border-color: hsl(var(--input));
}
.border-neutral-100{
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));
}
.border-neutral-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
}
.border-neutral-300{
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}
.border-neutral-400{
  --tw-border-opacity: 1;
  border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
}
.border-neutral-900{
  --tw-border-opacity: 1;
  border-color: rgb(23 23 23 / var(--tw-border-opacity, 1));
}
.border-orange-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}
.border-primary{
  border-color: hsl(var(--primary));
}
.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300{
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-slate-100{
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}
.border-transparent{
  border-color: transparent;
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/10{
  border-color: rgb(255 255 255 / 0.1);
}
.border-yellow-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-yellow-400{
  --tw-border-opacity: 1;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}
.border-b-neutral-300{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}
.border-b-transparent{
  border-bottom-color: transparent;
}
.border-l-transparent{
  border-left-color: transparent;
}
.border-l-white{
  --tw-border-opacity: 1;
  border-left-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-t-transparent{
  border-top-color: transparent;
}
.\!bg-green-300{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(134 239 172 / var(--tw-bg-opacity, 1)) !important;
}
.\!bg-transparent{
  background-color: transparent !important;
}
.\!bg-white{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
}
.bg-\[\#2070EB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(32 112 235 / var(--tw-bg-opacity, 1));
}
.bg-\[\#4292DB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(66 146 219 / var(--tw-bg-opacity, 1));
}
.bg-\[\#4292EB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(66 146 235 / var(--tw-bg-opacity, 1));
}
.bg-\[\#E5E4E7\]{
  --tw-bg-opacity: 1;
  background-color: rgb(229 228 231 / var(--tw-bg-opacity, 1));
}
.bg-\[--color-bg\]{
  background-color: var(--color-bg);
}
.bg-\[rgba\(0\2c 0\2c 0\2c \.8\)\]{
  background-color: rgba(0,0,0,.8);
}
.bg-\[rgba\(0\2c 0\2c 0\2c 0\.1\)\]{
  background-color: rgba(0,0,0,0.1);
}
.bg-accent{
  background-color: hsl(var(--accent));
}
.bg-background{
  background-color: hsl(var(--background));
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/80{
  background-color: rgb(0 0 0 / 0.8);
}
.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-300{
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-700{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.bg-border{
  background-color: hsl(var(--border));
}
.bg-card{
  background-color: hsl(var(--card));
}
.bg-destructive{
  background-color: hsl(var(--destructive));
}
.bg-destructive-foreground{
  background-color: hsl(var(--destructive-foreground));
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-200{
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}
.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-indigo-100{
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-muted{
  background-color: hsl(var(--muted));
}
.bg-muted\/50{
  background-color: hsl(var(--muted) / 0.5);
}
.bg-neutral-100{
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
.bg-neutral-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}
.bg-neutral-300{
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}
.bg-neutral-400{
  --tw-bg-opacity: 1;
  background-color: rgb(163 163 163 / var(--tw-bg-opacity, 1));
}
.bg-neutral-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}
.bg-neutral-600{
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
}
.bg-neutral-900{
  --tw-bg-opacity: 1;
  background-color: rgb(23 23 23 / var(--tw-bg-opacity, 1));
}
.bg-orange-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-popover{
  background-color: hsl(var(--popover));
}
.bg-primary{
  background-color: hsl(var(--primary));
}
.bg-primary\/10{
  background-color: hsl(var(--primary) / 0.1);
}
.bg-primary\/20{
  background-color: hsl(var(--primary) / 0.2);
}
.bg-purple-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-600{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-secondary{
  background-color: hsl(var(--secondary));
}
.bg-slate-100{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.bg-slate-50{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.bg-transparent{
  background-color: transparent;
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/80{
  background-color: rgb(255 255 255 / 0.8);
}
.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-zinc-900{
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));
}
.bg-opacity-75{
  --tw-bg-opacity: 0.75;
}
.bg-\[url\(\'\/assets\/landing\/section-bg\.png\'\)\]{
  background-image: url('/assets/landing/section-bg.png');
}
.bg-\[url\(\'\/assets\/templates\/bg\.gif\'\)\]{
  background-image: url('/assets/templates/bg.gif');
}
.bg-gradient-to-b{
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-blue-500{
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-neutral-100{
  --tw-gradient-from: #f5f5f5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 245 245 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-neutral-200{
  --tw-gradient-from: #e5e5e5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(229 229 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-neutral-50{
  --tw-gradient-from: #fafafa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 250 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-50{
  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 242 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50{
  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent{
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white{
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-blue-50{
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}
.to-neutral-100{
  --tw-gradient-to: #f5f5f5 var(--tw-gradient-to-position);
}
.to-neutral-200{
  --tw-gradient-to: #e5e5e5 var(--tw-gradient-to-position);
}
.to-neutral-300{
  --tw-gradient-to: #d4d4d4 var(--tw-gradient-to-position);
}
.to-neutral-50{
  --tw-gradient-to: #fafafa var(--tw-gradient-to-position);
}
.to-pink-50{
  --tw-gradient-to: #fdf2f8 var(--tw-gradient-to-position);
}
.to-purple-600{
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-transparent{
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.bg-contain{
  background-size: contain;
}
.bg-cover{
  background-size: cover;
}
.bg-center{
  background-position: center;
}
.bg-no-repeat{
  background-repeat: no-repeat;
}
.fill-background{
  fill: hsl(var(--background));
}
.fill-current{
  fill: currentColor;
}
.fill-foreground{
  fill: hsl(var(--foreground));
}
.fill-muted-foreground{
  fill: hsl(var(--muted-foreground));
}
.fill-primary{
  fill: hsl(var(--primary));
}
.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}
.\!p-0{
  padding: 0px !important;
}
.\!p-0\.5{
  padding: 0.125rem !important;
}
.\!p-1{
  padding: 0.25rem !important;
}
.\!p-1\.5{
  padding: 0.375rem !important;
}
.p-0{
  padding: 0px;
}
.p-0\.5{
  padding: 0.125rem;
}
.p-1{
  padding: 0.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-10{
  padding: 2.5rem;
}
.p-2{
  padding: 0.5rem;
}
.p-2\.5{
  padding: 0.625rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.p-9{
  padding: 2.25rem;
}
.p-\[1px\]{
  padding: 1px;
}
.\!px-0{
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.\!px-1{
  padding-left: 0.25rem !important;
  padding-right: 0.25rem !important;
}
.\!px-1\.5{
  padding-left: 0.375rem !important;
  padding-right: 0.375rem !important;
}
.\!px-2{
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}
.\!px-3{
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}
.\!px-4{
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}
.\!py-0{
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.px-0{
  padding-left: 0px;
  padding-right: 0px;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5{
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-12{
  padding-left: 3rem;
  padding-right: 3rem;
}
.px-14{
  padding-left: 3.5rem;
  padding-right: 3.5rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0{
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-24{
  padding-top: 6rem;
  padding-bottom: 6rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-7{
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.\!pb-0{
  padding-bottom: 0px !important;
}
.\!pr-2{
  padding-right: 0.5rem !important;
}
.pb-0{
  padding-bottom: 0px;
}
.pb-0\.5{
  padding-bottom: 0.125rem;
}
.pb-1{
  padding-bottom: 0.25rem;
}
.pb-10{
  padding-bottom: 2.5rem;
}
.pb-12{
  padding-bottom: 3rem;
}
.pb-16{
  padding-bottom: 4rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-20{
  padding-bottom: 5rem;
}
.pb-24{
  padding-bottom: 6rem;
}
.pb-28{
  padding-bottom: 7rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-48{
  padding-bottom: 12rem;
}
.pb-5{
  padding-bottom: 1.25rem;
}
.pb-6{
  padding-bottom: 1.5rem;
}
.pb-8{
  padding-bottom: 2rem;
}
.pb-\[1px\]{
  padding-bottom: 1px;
}
.pl-0{
  padding-left: 0px;
}
.pl-1{
  padding-left: 0.25rem;
}
.pl-1\.5{
  padding-left: 0.375rem;
}
.pl-10{
  padding-left: 2.5rem;
}
.pl-2{
  padding-left: 0.5rem;
}
.pl-2\.5{
  padding-left: 0.625rem;
}
.pl-3{
  padding-left: 0.75rem;
}
.pl-3\.5{
  padding-left: 0.875rem;
}
.pl-4{
  padding-left: 1rem;
}
.pl-6{
  padding-left: 1.5rem;
}
.pl-7{
  padding-left: 1.75rem;
}
.pl-8{
  padding-left: 2rem;
}
.pl-9{
  padding-left: 2.25rem;
}
.pr-0{
  padding-right: 0px;
}
.pr-0\.5{
  padding-right: 0.125rem;
}
.pr-1{
  padding-right: 0.25rem;
}
.pr-10{
  padding-right: 2.5rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.pr-3\.5{
  padding-right: 0.875rem;
}
.pr-4{
  padding-right: 1rem;
}
.pr-6{
  padding-right: 1.5rem;
}
.pr-8{
  padding-right: 2rem;
}
.pt-0{
  padding-top: 0px;
}
.pt-0\.5{
  padding-top: 0.125rem;
}
.pt-1{
  padding-top: 0.25rem;
}
.pt-1\.5{
  padding-top: 0.375rem;
}
.pt-12{
  padding-top: 3rem;
}
.pt-14{
  padding-top: 3.5rem;
}
.pt-16{
  padding-top: 4rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-2\.5{
  padding-top: 0.625rem;
}
.pt-28{
  padding-top: 7rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-5{
  padding-top: 1.25rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-8{
  padding-top: 2rem;
}
.pt-\[0px\]{
  padding-top: 0px;
}
.text-left{
  text-align: left;
}
.\!text-center{
  text-align: center !important;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.align-middle{
  vertical-align: middle;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-sans{
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.\!text-\[10px\]{
  font-size: 10px !important;
}
.\!text-\[11px\]{
  font-size: 11px !important;
}
.\!text-\[9px\]{
  font-size: 9px !important;
}
.\!text-xs{
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-\[0\.65rem\]{
  font-size: 0.65rem;
}
.text-\[0\.6rem\]{
  font-size: 0.6rem;
}
.text-\[0\.8rem\]{
  font-size: 0.8rem;
}
.text-\[10px\]{
  font-size: 10px;
}
.text-\[11px\]{
  font-size: 11px;
}
.text-\[13px\]{
  font-size: 13px;
}
.text-\[8px\]{
  font-size: 8px;
}
.text-\[9px\]{
  font-size: 9px;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.\!font-semibold{
  font-weight: 600 !important;
}
.font-black{
  font-weight: 900;
}
.font-bold{
  font-weight: 700;
}
.font-extrabold{
  font-weight: 800;
}
.font-light{
  font-weight: 300;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.lowercase{
  text-transform: lowercase;
}
.capitalize{
  text-transform: capitalize;
}
.italic{
  font-style: italic;
}
.tabular-nums{
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.\!leading-normal{
  line-height: 1.5 !important;
}
.\!leading-tight{
  line-height: 1.25 !important;
}
.leading-4{
  line-height: 1rem;
}
.leading-5{
  line-height: 1.25rem;
}
.leading-6{
  line-height: 1.5rem;
}
.leading-7{
  line-height: 1.75rem;
}
.leading-8{
  line-height: 2rem;
}
.leading-none{
  line-height: 1;
}
.leading-relaxed{
  line-height: 1.625;
}
.leading-tight{
  line-height: 1.25;
}
.tracking-normal{
  letter-spacing: 0em;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-widest{
  letter-spacing: 0.1em;
}
.\!text-black{
  --tw-text-opacity: 1 !important;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) !important;
}
.\!text-destructive{
  color: hsl(var(--destructive)) !important;
}
.\!text-neutral-800{
  --tw-text-opacity: 1 !important;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1)) !important;
}
.text-\[\#333\]{
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity, 1));
}
.text-\[\#4292EB\]{
  --tw-text-opacity: 1;
  color: rgb(66 146 235 / var(--tw-text-opacity, 1));
}
.text-\[\#EEF5FC\]{
  --tw-text-opacity: 1;
  color: rgb(238 245 252 / var(--tw-text-opacity, 1));
}
.text-\[\#f22929\]{
  --tw-text-opacity: 1;
  color: rgb(242 41 41 / var(--tw-text-opacity, 1));
}
.text-accent-foreground{
  color: hsl(var(--accent-foreground));
}
.text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-100{
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-card-foreground{
  color: hsl(var(--card-foreground));
}
.text-current{
  color: currentColor;
}
.text-destructive{
  color: hsl(var(--destructive));
}
.text-destructive-foreground{
  color: hsl(var(--destructive-foreground));
}
.text-foreground{
  color: hsl(var(--foreground));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-200{
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-indigo-700{
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}
.text-inherit{
  color: inherit;
}
.text-muted-foreground{
  color: hsl(var(--muted-foreground));
}
.text-neutral-300{
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
}
.text-neutral-400{
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}
.text-neutral-500{
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}
.text-neutral-600{
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}
.text-neutral-700{
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}
.text-neutral-800{
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}
.text-neutral-900{
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}
.text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-700{
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}
.text-orange-800{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-popover-foreground{
  color: hsl(var(--popover-foreground));
}
.text-primary{
  color: hsl(var(--primary));
}
.text-primary-foreground{
  color: hsl(var(--primary-foreground));
}
.text-purple-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-purple-700{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-red-900{
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}
.text-secondary-foreground{
  color: hsl(var(--secondary-foreground));
}
.text-slate-300{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.text-slate-400{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.text-slate-500{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-slate-600{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-slate-700{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.text-slate-900{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-300{
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600{
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-700{
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}
.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline{
  text-decoration-line: underline;
}
.line-through{
  text-decoration-line: line-through;
}
.\!no-underline{
  text-decoration-line: none !important;
}
.decoration-neutral-300{
  text-decoration-color: #d4d4d4;
}
.decoration-dashed{
  text-decoration-style: dashed;
}
.underline-offset-2{
  text-underline-offset: 2px;
}
.underline-offset-4{
  text-underline-offset: 4px;
}
.antialiased{
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.\!opacity-0{
  opacity: 0 !important;
}
.\!opacity-100{
  opacity: 1 !important;
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-30{
  opacity: 0.3;
}
.opacity-35{
  opacity: 0.35;
}
.opacity-40{
  opacity: 0.4;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-65{
  opacity: 0.65;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-75{
  opacity: 0.75;
}
.opacity-90{
  opacity: 0.9;
}
.\!shadow-none{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\]{
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.\!outline-none{
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.\!outline-0{
  outline-width: 0px !important;
}
.outline-0{
  outline-width: 0px;
}
.outline-offset-\[8px\]{
  outline-offset: 8px;
}
.outline-black{
  outline-color: #000;
}
.outline-neutral-300{
  outline-color: #d4d4d4;
}
.\!ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}
.ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-inset{
  --tw-ring-inset: inset;
}
.\!ring-red-500{
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1)) !important;
}
.ring-black{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.ring-blue-500\/50{
  --tw-ring-color: rgb(59 130 246 / 0.5);
}
.ring-gray-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1));
}
.ring-gray-900\/5{
  --tw-ring-color: rgb(17 24 39 / 0.05);
}
.ring-opacity-5{
  --tw-ring-opacity: 0.05;
}
.ring-offset-background{
  --tw-ring-offset-color: hsl(var(--background));
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow{
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-none{
  --tw-drop-shadow: drop-shadow(0 0 #0000);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale{
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.\!filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[left\2c right\2c width\]{
  transition-property: left,right,width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[margin\2c opacity\]{
  transition-property: margin,opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\2c height\2c padding\]{
  transition-property: width,height,padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\]{
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-150{
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.ease-in{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear{
  transition-timing-function: linear;
}
.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
@keyframes enter{
  from{
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit{
  to{
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.fade-in-0{
  --tw-enter-opacity: 0;
}
.zoom-in-95{
  --tw-enter-scale: .95;
}
.duration-150{
  animation-duration: 150ms;
}
.duration-200{
  animation-duration: 200ms;
}
.duration-300{
  animation-duration: 300ms;
}
.ease-in{
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out{
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear{
  animation-timing-function: linear;
}
.ease-out{
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.paused{
  animation-play-state: paused;
}
body {
  font-family: "Plus Jakarta Sans", sans-serif;
}
.font-grotesk {
  font-family: "Space Grotesk", sans-serif;
  font-optical-sizing: auto;
  /*font-weight: <weight>;*/
  /*font-style: normal;*/
}
.scrollBlockChild > div > div {
  display: block !important;
}
#app-wrap {
  position: fixed;
  overflow: hidden;
  display: flex;
  width: 100%;
  height: 100%;
}
#app-wrap nav {
    width: 254px;
    position: fixed;
    z-index: 100;
    background: white;
    /*box-shadow: 0px 0px 1px 0px rgba(24, 26, 27, 0.04), 0px 3px 6px 0px rgba(24, 26, 27, 0.08), 0px 9px 24px 0px rgba(24, 26, 27, 0.16), 0 0 0 1px rgba(141, 141, 141, 0.24);*/

  }
@media (min-width: 1024px) {
    #app-wrap nav {
      position: relative;
      /*box-shadow: none;*/
    }
  }
#app-wrap.collapsed nav {
    display: none;
  }
#app-wrap main {
    /*background: black;*/
    /*background: green;*/
    flex: 1;
  }
#app-wrap main .title {
    padding-left: 36px;
  }
#app-wrap.collapsed main .title {
    padding-left: 36px !important;
  }
@media (min-width: 1024px) {
    #app-wrap main .title {
      padding-left: 0;
    }
  }
.react-flow {
  /*--xy-edge-stroke-width: 2px;*/
  --xy-handle-background-color: transparent;
  --xy-handle-border-color: transparent;
}
/*.rdg-row[aria-selected="true"] .rdg-cell[aria-selected="true"] {*/
/*    @apply !bg-neutral-200 !border-neutral-200;*/
/*}*/
.rdg-row[aria-selected='true'] {
  /*background-color: var(--rdg-row-selected-background-color);*/
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1)) !important;
}
.rdg-cell {
  --grid-pad-size: 12px;


  /*--rdg-row-hover-background-color: hsl(0deg 0% 96%);*/
  /*--rdg-row-selected-background-color: hsl(0deg 0% 96%);*/
  --rdg-row-selected-background-color: hsl(0deg 0% 96%);

  /*background-color: var(--rdg-row-selected-background-color);*/
  /*}*/
  /*--rdg-row-selected-hover-background-color: hsl(207deg 3.9% 4.43%);*/
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;

  padding: 0;
  cursor: default;


}
.rdg-cell[role="columnheader"] {
    outline: none;
  }
.rdg-cell[role="columnheader"] > .r-header > button {
    opacity: 0;
    visibility: hidden;
  }
.rdg-cell[role="columnheader"]:hover > .r-header > button {
    opacity: 1;
    visibility: visible;
  }
.rdg-cell .r-text, .rdg-cell .r-text-edit input, .rdg-cell .r-header, .rdg-cell .r-checkbox, .rdg-cell .r-tag, .rdg-cell .r-tag-edit, .rdg-cell .r-date-edit, .rdg-cell .r-files, .rdg-cell .r-row-select {
    padding-left: var(--grid-pad-size) !important;
    padding-right: var(--grid-pad-size) !important;
    border-bottom-width: 1px;
    border-right-width: 1px;
    font-weight: 500;
    overflow: hidden;
  }
.rdg-cell .r-files {
    padding-left: calc(var(--grid-pad-size) / 2) !important;
    padding-right: calc(var(--grid-pad-size) / 2) !important;
  }
.rdg-cell .r-row-select {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
.rdg-cell .r-header {
    outline: none !important;
  }
.r-date-edit-pop .react-datepicker {
    border: 0;
    font-family: inherit;
  }
.r-date-edit-pop .react-datepicker__header, .r-date-edit-pop .react-datepicker__time-container{
  border-radius: 0px;
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.r-date-edit-pop .react-datepicker__current-month, .r-date-edit-pop .react-datepicker-time__header, .r-date-edit-pop .react-datepicker-year-header{
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(10 10 10 / var(--tw-text-opacity, 1));
}
.r-date-edit-pop .react-datepicker__day{
  border-radius: 9999px !important;
}
.r-date-edit-pop .react-datepicker__day-name{
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}
.list-view-field-static {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.list-view-field-static * {
  pointer-events: none;
  cursor: default !important;
}
.file\:border-0::file-selector-button{
  border-width: 0px;
}
.file\:bg-transparent::file-selector-button{
  background-color: transparent;
}
.file\:text-sm::file-selector-button{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.file\:font-medium::file-selector-button{
  font-weight: 500;
}
.placeholder\:text-gray-500::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.placeholder\:text-gray-500::placeholder{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.placeholder\:text-muted-foreground::-moz-placeholder{
  color: hsl(var(--muted-foreground));
}
.placeholder\:text-muted-foreground::placeholder{
  color: hsl(var(--muted-foreground));
}
.placeholder\:text-neutral-400::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}
.placeholder\:text-neutral-400::placeholder{
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}
.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}
.after\:-inset-2::after{
  content: var(--tw-content);
  inset: -0.5rem;
}
.after\:inset-y-0::after{
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}
.after\:left-1\/2::after{
  content: var(--tw-content);
  left: 50%;
}
.after\:w-1::after{
  content: var(--tw-content);
  width: 0.25rem;
}
.after\:w-\[2px\]::after{
  content: var(--tw-content);
  width: 2px;
}
.after\:-translate-x-1\/2::after{
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.first\:border-l:first-child{
  border-left-width: 1px;
}
.last\:border-r-0:last-child{
  border-right-width: 0px;
}
.focus-within\:border-black:focus-within{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:border-b:hover{
  border-bottom-width: 1px;
}
.hover\:border-black:hover{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.hover\:border-neutral-200:hover{
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
}
.hover\:border-neutral-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}
.hover\:border-neutral-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
}
.hover\:border-neutral-600:hover{
  --tw-border-opacity: 1;
  border-color: rgb(82 82 82 / var(--tw-border-opacity, 1));
}
.hover\:bg-\[\#276bac\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(39 107 172 / var(--tw-bg-opacity, 1));
}
.hover\:bg-\[\#ddd\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(221 221 221 / var(--tw-bg-opacity, 1));
}
.hover\:bg-\[\#eee\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1));
}
.hover\:bg-accent:hover{
  background-color: hsl(var(--accent));
}
.hover\:bg-blue-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.hover\:bg-destructive\/80:hover{
  background-color: hsl(var(--destructive) / 0.8);
}
.hover\:bg-destructive\/90:hover{
  background-color: hsl(var(--destructive) / 0.9);
}
.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.hover\:bg-muted:hover{
  background-color: hsl(var(--muted));
}
.hover\:bg-muted\/50:hover{
  background-color: hsl(var(--muted) / 0.5);
}
.hover\:bg-neutral-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary\/80:hover{
  background-color: hsl(var(--primary) / 0.8);
}
.hover\:bg-primary\/90:hover{
  background-color: hsl(var(--primary) / 0.9);
}
.hover\:bg-red-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.hover\:bg-secondary\/80:hover{
  background-color: hsl(var(--secondary) / 0.8);
}
.hover\:bg-slate-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.hover\:bg-transparent:hover{
  background-color: transparent;
}
.hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white\/90:hover{
  background-color: rgb(255 255 255 / 0.9);
}
.hover\:bg-orange-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.hover\:from-neutral-50:hover{
  --tw-gradient-from: #fafafa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 250 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:to-neutral-100:hover{
  --tw-gradient-to: #f5f5f5 var(--tw-gradient-to-position);
}
.hover\:text-accent-foreground:hover{
  color: hsl(var(--accent-foreground));
}
.hover\:text-black:hover{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-300:hover{
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-700:hover{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-800:hover{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.hover\:text-foreground:hover{
  color: hsl(var(--foreground));
}
.hover\:text-gray-400:hover{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-500:hover{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-800:hover{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-900:hover{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.hover\:text-muted-foreground:hover{
  color: hsl(var(--muted-foreground));
}
.hover\:text-neutral-600:hover{
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}
.hover\:text-neutral-700:hover{
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}
.hover\:text-neutral-800:hover{
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}
.hover\:text-primary:hover{
  color: hsl(var(--primary));
}
.hover\:text-primary\/80:hover{
  color: hsl(var(--primary) / 0.8);
}
.hover\:text-red-600:hover{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.hover\:text-red-700:hover{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.hover\:underline:hover{
  text-decoration-line: underline;
}
.hover\:opacity-100:hover{
  opacity: 1;
}
.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover{
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-sm:hover{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:z-10:focus{
  z-index: 10;
}
.focus\:w-48:focus{
  width: 12rem;
}
.focus\:border-black:focus{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.focus\:border-transparent:focus{
  border-color: transparent;
}
.focus\:bg-accent:focus{
  background-color: hsl(var(--accent));
}
.focus\:bg-neutral-100:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
.focus\:bg-transparent:focus{
  background-color: transparent;
}
.focus\:text-accent-foreground:focus{
  color: hsl(var(--accent-foreground));
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-0:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-4:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-blue-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}
.focus\:ring-neutral-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(115 115 115 / var(--tw-ring-opacity, 1));
}
.focus\:ring-ring:focus{
  --tw-ring-color: hsl(var(--ring));
}
.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}
.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:ring-0:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-1:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-ring:focus-visible{
  --tw-ring-color: hsl(var(--ring));
}
.focus-visible\:ring-offset-1:focus-visible{
  --tw-ring-offset-width: 1px;
}
.focus-visible\:ring-offset-2:focus-visible{
  --tw-ring-offset-width: 2px;
}
.focus-visible\:ring-offset-background:focus-visible{
  --tw-ring-offset-color: hsl(var(--background));
}
.active\:border-black:active{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.active\:bg-accent:active{
  background-color: hsl(var(--accent));
}
.active\:bg-neutral-100:active{
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
.active\:bg-transparent:active{
  background-color: transparent;
}
.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}
.disabled\:opacity-50:disabled{
  opacity: 0.5;
}
.disabled\:opacity-60:disabled{
  opacity: 0.6;
}
.group[open] .group-open\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100{
  opacity: 1;
}
.group\/listview:hover .group-hover\/listview\:visible{
  visibility: visible;
}
.group:hover .group-hover\:visible{
  visibility: visible;
}
.group:hover .group-hover\:right-0{
  right: 0px;
}
.group\/view:hover .group-hover\/view\:block{
  display: block;
}
.group:hover .group-hover\:block{
  display: block;
}
.group:hover .group-hover\:flex{
  display: flex;
}
.group\/view:hover .group-hover\/view\:hidden{
  display: none;
}
.group:hover .group-hover\:hidden{
  display: none;
}
.group:hover .group-hover\:size-14{
  width: 3.5rem;
  height: 3.5rem;
}
.group:hover .group-hover\:bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-blue-500\/20{
  background-color: rgb(59 130 246 / 0.2);
}
.group:hover .group-hover\:pr-6{
  padding-right: 1.5rem;
}
.group:hover .group-hover\:underline{
  text-decoration-line: underline;
}
.group\/listview:hover .group-hover\/listview\:opacity-100{
  opacity: 1;
}
.group\/menu-item:hover .group-hover\/menu-item\:opacity-100{
  opacity: 1;
}
.group:hover .group-hover\:opacity-100{
  opacity: 1;
}
.group.toaster .group-\[\.toaster\]\:border-border{
  border-color: hsl(var(--border));
}
.group.toast .group-\[\.toast\]\:bg-muted{
  background-color: hsl(var(--muted));
}
.group.toast .group-\[\.toast\]\:bg-primary{
  background-color: hsl(var(--primary));
}
.group.toaster .group-\[\.toaster\]\:bg-background{
  background-color: hsl(var(--background));
}
.group.toast .group-\[\.toast\]\:text-muted-foreground{
  color: hsl(var(--muted-foreground));
}
.group.toast .group-\[\.toast\]\:text-primary-foreground{
  color: hsl(var(--primary-foreground));
}
.group.toaster .group-\[\.toaster\]\:text-foreground{
  color: hsl(var(--foreground));
}
.group.toaster .group-\[\.toaster\]\:shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70{
  opacity: 0.7;
}
.group\/menu-item:has([data-sidebar=menu-action]) .group-has-\[\[data-sidebar\=menu-action\]\]\/menu-item\:pr-8{
  padding-right: 2rem;
}
.aria-disabled\:pointer-events-none[aria-disabled="true"]{
  pointer-events: none;
}
.aria-disabled\:opacity-50[aria-disabled="true"]{
  opacity: 0.5;
}
.aria-selected\:bg-accent[aria-selected="true"]{
  background-color: hsl(var(--accent));
}
.aria-selected\:text-accent-foreground[aria-selected="true"]{
  color: hsl(var(--accent-foreground));
}
.data-\[disabled\]\:pointer-events-none[data-disabled]{
  pointer-events: none;
}
.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"]{
  height: 1px;
}
.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"]{
  width: 100%;
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"]{
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=left\]\:-translate-x-1[data-side="left"]{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=right\]\:translate-x-1[data-side="right"]{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=top\]\:-translate-y-1[data-side="top"]{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=checked\]\:translate-x-4[data-state="checked"]{
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"]{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes accordion-up{
  from{
    height: var(--radix-accordion-content-height);
  }
  to{
    height: 0;
  }
}
.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"]{
  animation: accordion-up 0.2s ease-out;
}
@keyframes accordion-down{
  from{
    height: 0;
  }
  to{
    height: var(--radix-accordion-content-height);
  }
}
.data-\[state\=open\]\:animate-accordion-down[data-state="open"]{
  animation: accordion-down 0.2s ease-out;
}
.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"]{
  flex-direction: column;
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"]{
  background-color: hsl(var(--primary));
}
.data-\[state\=on\]\:bg-accent[data-state="on"]{
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-accent[data-state="open"]{
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-secondary[data-state="open"]{
  background-color: hsl(var(--secondary));
}
.data-\[state\=selected\]\:bg-muted[data-state="selected"]{
  background-color: hsl(var(--muted));
}
.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"]{
  background-color: hsl(var(--input));
}
.data-\[active\=true\]\:font-medium[data-active="true"]{
  font-weight: 500;
}
.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"]{
  color: hsl(var(--primary-foreground));
}
.data-\[state\=on\]\:text-accent-foreground[data-state="on"]{
  color: hsl(var(--accent-foreground));
}
.data-\[state\=open\]\:text-accent-foreground[data-state="open"]{
  color: hsl(var(--accent-foreground));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"]{
  color: hsl(var(--muted-foreground));
}
.data-\[disabled\]\:opacity-50[data-disabled]{
  opacity: 0.5;
}
.data-\[state\=open\]\:opacity-100[data-state="open"]{
  opacity: 1;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"]{
  transition-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"]{
  transition-duration: 500ms;
}
.data-\[state\=open\]\:animate-in[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=closed\]\:animate-out[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=closed\]\:fade-out-0[data-state="closed"]{
  --tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"]{
  --tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"]{
  --tw-exit-scale: .95;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"]{
  --tw-enter-scale: .95;
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"]{
  --tw-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"]{
  --tw-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"]{
  --tw-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"]{
  --tw-enter-translate-y: 0.5rem;
}
.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"]{
  --tw-exit-translate-y: 100%;
}
.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"]{
  --tw-exit-translate-x: -100%;
}
.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"]{
  --tw-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"]{
  --tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"]{
  --tw-exit-translate-y: -100%;
}
.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"]{
  --tw-exit-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"]{
  --tw-enter-translate-y: 100%;
}
.data-\[state\=open\]\:slide-in-from-left[data-state="open"]{
  --tw-enter-translate-x: -100%;
}
.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"]{
  --tw-enter-translate-x: -50%;
}
.data-\[state\=open\]\:slide-in-from-right[data-state="open"]{
  --tw-enter-translate-x: 100%;
}
.data-\[state\=open\]\:slide-in-from-top[data-state="open"]{
  --tw-enter-translate-y: -100%;
}
.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"]{
  --tw-enter-translate-y: -48%;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"]{
  animation-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"]{
  animation-duration: 500ms;
}
.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  left: 0px;
}
.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  height: 0.25rem;
}
.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  width: 100%;
}
.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]{
  left: calc(var(--sidebar-width) * -1);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]{
  right: calc(var(--sidebar-width) * -1);
}
.group[data-side="left"] .group-data-\[side\=left\]\:-right-4{
  right: -1rem;
}
.group[data-side="right"] .group-data-\[side\=right\]\:left-0{
  left: 0px;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:-mt-8{
  margin-top: -2rem;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:hidden{
  display: none;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!size-8{
  width: 2rem !important;
  height: 2rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[--sidebar-width-icon\]{
  width: var(--sidebar-width-icon);
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\]{
  width: calc(var(--sidebar-width-icon) + 1rem);
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\]{
  width: calc(var(--sidebar-width-icon) + 1rem + 2px);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:w-0{
  width: 0px;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-side="right"] .group-data-\[side\=right\]\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:overflow-hidden{
  overflow: hidden;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:rounded-lg{
  border-radius: var(--radius);
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border{
  border-width: 1px;
}
.group[data-side="left"] .group-data-\[side\=left\]\:border-r{
  border-right-width: 1px;
}
.group[data-side="right"] .group-data-\[side\=right\]\:border-l{
  border-left-width: 1px;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-0{
  padding: 0px !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-2{
  padding: 0.5rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:opacity-0{
  opacity: 0;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:after\:left-full::after{
  content: var(--tw-content);
  left: 100%;
}
.peer\/menu-button[data-size="default"] ~ .peer-data-\[size\=default\]\/menu-button\:top-1\.5{
  top: 0.375rem;
}
.peer\/menu-button[data-size="lg"] ~ .peer-data-\[size\=lg\]\/menu-button\:top-2\.5{
  top: 0.625rem;
}
.peer\/menu-button[data-size="sm"] ~ .peer-data-\[size\=sm\]\/menu-button\:top-1{
  top: 0.25rem;
}
.dark\:border-r:is(.dark *){
  border-right-width: 1px;
}
.dark\:border-gray-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.dark\:border-gray-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.dark\:bg-blue-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.dark\:bg-blue-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.dark\:text-gray-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.dark\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.dark\:hover\:bg-blue-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:text-white:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.dark\:focus\:ring-blue-800:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-gray-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(75 85 99 / var(--tw-ring-opacity, 1));
}
@media (min-width: 640px){
  .sm\:my-8{
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
  .sm\:mb-3{
    margin-bottom: 0.75rem;
  }
  .sm\:mt-0{
    margin-top: 0px;
  }
  .sm\:mt-1{
    margin-top: 0.25rem;
  }
  .sm\:mt-1\.5{
    margin-top: 0.375rem;
  }
  .sm\:mt-10{
    margin-top: 2.5rem;
  }
  .sm\:mt-20{
    margin-top: 5rem;
  }
  .sm\:mt-3{
    margin-top: 0.75rem;
  }
  .sm\:line-clamp-2{
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .sm\:inline{
    display: inline;
  }
  .sm\:flex{
    display: flex;
  }
  .sm\:h-12{
    height: 3rem;
  }
  .sm\:h-4{
    height: 1rem;
  }
  .sm\:h-5{
    height: 1.25rem;
  }
  .sm\:h-6{
    height: 1.5rem;
  }
  .sm\:h-7{
    height: 1.75rem;
  }
  .sm\:max-h-\[65vh\]{
    max-height: 65vh;
  }
  .sm\:w-12{
    width: 3rem;
  }
  .sm\:w-4{
    width: 1rem;
  }
  .sm\:w-5{
    width: 1.25rem;
  }
  .sm\:w-6{
    width: 1.5rem;
  }
  .sm\:w-7{
    width: 1.75rem;
  }
  .sm\:w-\[350px\]{
    width: 350px;
  }
  .sm\:w-\[400px\]{
    width: 400px;
  }
  .sm\:w-\[420px\]{
    width: 420px;
  }
  .sm\:w-\[450px\]{
    width: 450px;
  }
  .sm\:w-\[650px\]{
    width: 650px;
  }
  .sm\:w-full{
    width: 100%;
  }
  .sm\:max-w-3xl{
    max-width: 48rem;
  }
  .sm\:max-w-\[425px\]{
    max-width: 425px;
  }
  .sm\:max-w-\[550px\]{
    max-width: 550px;
  }
  .sm\:max-w-\[600px\]{
    max-width: 600px;
  }
  .sm\:max-w-lg{
    max-width: 32rem;
  }
  .sm\:max-w-md{
    max-width: 28rem;
  }
  .sm\:max-w-sm{
    max-width: 24rem;
  }
  .sm\:translate-y-0{
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .sm\:scale-100{
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .sm\:scale-95{
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .sm\:flex-row{
    flex-direction: row;
  }
  .sm\:flex-row-reverse{
    flex-direction: row-reverse;
  }
  .sm\:flex-nowrap{
    flex-wrap: nowrap;
  }
  .sm\:items-start{
    align-items: flex-start;
  }
  .sm\:items-center{
    align-items: center;
  }
  .sm\:justify-start{
    justify-content: flex-start;
  }
  .sm\:justify-end{
    justify-content: flex-end;
  }
  .sm\:gap-2\.5{
    gap: 0.625rem;
  }
  .sm\:gap-3{
    gap: 0.75rem;
  }
  .sm\:gap-y-20{
    row-gap: 5rem;
  }
  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:rounded-lg{
    border-radius: var(--radius);
  }
  .sm\:p-0{
    padding: 0px;
  }
  .sm\:p-1\.5{
    padding: 0.375rem;
  }
  .sm\:p-14{
    padding: 3.5rem;
  }
  .sm\:p-4{
    padding: 1rem;
  }
  .sm\:px-3{
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .sm\:py-0{
    padding-top: 0px;
    padding-bottom: 0px;
  }
  .sm\:py-1{
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }
  .sm\:py-2{
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  .sm\:py-20{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
  .sm\:py-28{
    padding-top: 7rem;
    padding-bottom: 7rem;
  }
  .sm\:py-3{
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  .sm\:py-32{
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
  .sm\:py-8{
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .sm\:pt-24{
    padding-top: 6rem;
  }
  .sm\:pt-32{
    padding-top: 8rem;
  }
  .sm\:text-left{
    text-align: left;
  }
  .sm\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  .sm\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
  .sm\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }
  .sm\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  .sm\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  .sm\:leading-6{
    line-height: 1.5rem;
  }
  .sm\:ring-1{
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  }
  .sm\:ring-gray-900\/10{
    --tw-ring-color: rgb(17 24 39 / 0.1);
  }
}
@media (min-width: 768px){
  .md\:inset-0{
    inset: 0px;
  }
  .md\:mt-0{
    margin-top: 0px;
  }
  .md\:block{
    display: block;
  }
  .md\:flex{
    display: flex;
  }
  .md\:grid{
    display: grid;
  }
  .md\:w-24{
    width: 6rem;
  }
  .md\:w-\[80\%\]{
    width: 80%;
  }
  .md\:max-w-\[80\%\]{
    max-width: 80%;
  }
  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:flex-row{
    flex-direction: row;
  }
  .md\:gap-8{
    gap: 2rem;
  }
  .md\:gap-y-5{
    row-gap: 1.25rem;
  }
  .md\:p-5{
    padding: 1.25rem;
  }
  .md\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }
  .md\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .md\:opacity-0{
    opacity: 0;
  }
  .after\:md\:hidden::after{
    content: var(--tw-content);
    display: none;
  }
  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:m-2{
    margin: 0.5rem;
  }
  .peer[data-state="collapsed"][data-variant="inset"] ~ .md\:peer-data-\[state\=collapsed\]\:peer-data-\[variant\=inset\]\:ml-2{
    margin-left: 0.5rem;
  }
  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:ml-0{
    margin-left: 0px;
  }
  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:rounded-xl{
    border-radius: 0.75rem;
  }
  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:shadow{
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 1024px){
  .lg\:left-20{
    left: 5rem;
  }
  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .lg\:mx-0{
    margin-left: 0px;
    margin-right: 0px;
  }
  .lg\:mb-48{
    margin-bottom: 12rem;
  }
  .lg\:ml-auto{
    margin-left: auto;
  }
  .lg\:mt-12{
    margin-top: 3rem;
  }
  .lg\:mt-24{
    margin-top: 6rem;
  }
  .lg\:block{
    display: block;
  }
  .lg\:flex{
    display: flex;
  }
  .lg\:inline-flex{
    display: inline-flex;
  }
  .lg\:hidden{
    display: none;
  }
  .lg\:min-h-\[190px\]{
    min-height: 190px;
  }
  .lg\:w-1\/5{
    width: 20%;
  }
  .lg\:w-2\/5{
    width: 40%;
  }
  .lg\:w-20{
    width: 5rem;
  }
  .lg\:w-28{
    width: 7rem;
  }
  .lg\:w-32{
    width: 8rem;
  }
  .lg\:w-36{
    width: 9rem;
  }
  .lg\:w-44{
    width: 11rem;
  }
  .lg\:w-60{
    width: 15rem;
  }
  .lg\:w-96{
    width: 24rem;
  }
  .lg\:w-auto{
    width: auto;
  }
  .lg\:w-full{
    width: 100%;
  }
  .lg\:min-w-0{
    min-width: 0px;
  }
  .lg\:min-w-\[850px\]{
    min-width: 850px;
  }
  .lg\:max-w-4xl{
    max-width: 56rem;
  }
  .lg\:max-w-96{
    max-width: 24rem;
  }
  .lg\:max-w-\[450px\]{
    max-width: 450px;
  }
  .lg\:max-w-\[90\%\]{
    max-width: 90%;
  }
  .lg\:max-w-lg{
    max-width: 32rem;
  }
  .lg\:max-w-none{
    max-width: none;
  }
  .lg\:flex-1{
    flex: 1 1 0%;
  }
  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .lg\:flex-row{
    flex-direction: row;
  }
  .lg\:flex-col{
    flex-direction: column;
  }
  .lg\:items-start{
    align-items: flex-start;
  }
  .lg\:items-center{
    align-items: center;
  }
  .lg\:justify-end{
    justify-content: flex-end;
  }
  .lg\:gap-0{
    gap: 0px;
  }
  .lg\:gap-12{
    gap: 3rem;
  }
  .lg\:gap-x-12{
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }
  .lg\:gap-x-8{
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }
  .lg\:overflow-auto{
    overflow: auto;
  }
  .lg\:overflow-x-visible{
    overflow-x: visible;
  }
  .lg\:border-l-0{
    border-left-width: 0px;
  }
  .lg\:border-r{
    border-right-width: 1px;
  }
  .lg\:p-3{
    padding: 0.75rem;
  }
  .lg\:p-4{
    padding: 1rem;
  }
  .lg\:p-8{
    padding: 2rem;
  }
  .lg\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }
  .lg\:px-10{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
  .lg\:px-12{
    padding-left: 3rem;
    padding-right: 3rem;
  }
  .lg\:px-20{
    padding-left: 5rem;
    padding-right: 5rem;
  }
  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .lg\:py-4{
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  .lg\:pb-0{
    padding-bottom: 0px;
  }
  .lg\:pb-32{
    padding-bottom: 8rem;
  }
  .lg\:pb-4{
    padding-bottom: 1rem;
  }
  .lg\:pr-4{
    padding-right: 1rem;
  }
  .lg\:pt-16{
    padding-top: 4rem;
  }
  .lg\:pt-20{
    padding-top: 5rem;
  }
  .lg\:pt-32{
    padding-top: 8rem;
  }
  .lg\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  .lg\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
  .lg\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }
  .lg\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }
  .lg\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }
  .lg\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  .lg\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  .lg\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  .lg\:font-bold{
    font-weight: 700;
  }
  .lg\:shadow-lg{
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .lg\:shadow-none{
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 1280px){
  .xl\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .xl\:mt-0{
    margin-top: 0px;
  }
  .xl\:grid{
    display: grid;
  }
  .xl\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .xl\:grid-cols-6{
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  .xl\:gap-8{
    gap: 2rem;
  }
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]){
  padding-right: 0px;
}
.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\]>[role=checkbox]{
  --tw-translate-y: 2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>button\]\:hidden>button{
  display: none;
}
.\[\&\>span\:last-child\]\:truncate>span:last-child{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.\[\&\>span\]\:line-clamp-1>span{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.\[\&\>svg\]\:size-4>svg{
  width: 1rem;
  height: 1rem;
}
.\[\&\>svg\]\:h-2\.5>svg{
  height: 0.625rem;
}
.\[\&\>svg\]\:h-3>svg{
  height: 0.75rem;
}
.\[\&\>svg\]\:h-3\.5>svg{
  height: 0.875rem;
}
.\[\&\>svg\]\:w-2\.5>svg{
  width: 0.625rem;
}
.\[\&\>svg\]\:w-3>svg{
  width: 0.75rem;
}
.\[\&\>svg\]\:w-3\.5>svg{
  width: 0.875rem;
}
.\[\&\>svg\]\:shrink-0>svg{
  flex-shrink: 0;
}
.\[\&\>svg\]\:text-muted-foreground>svg{
  color: hsl(var(--muted-foreground));
}
.\[\&\>tr\]\:last\:border-b-0:last-child>tr{
  border-bottom-width: 0px;
}
.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text{
  fill: hsl(var(--muted-foreground));
}
.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke='#ccc']{
  stroke: hsl(var(--border) / 0.5);
}
.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor{
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke='#fff']{
  stroke: transparent;
}
.\[\&_\.recharts-layer\]\:outline-none .recharts-layer{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke='#ccc']{
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector{
  fill: hsl(var(--muted));
}
.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{
  fill: hsl(var(--muted));
}
.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke='#ccc']{
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke='#fff']{
  stroke: transparent;
}
.\[\&_\.recharts-sector\]\:outline-none .recharts-sector{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\.recharts-surface\]\:outline-none .recharts-surface{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading]{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading]{
  font-size: 0.75rem;
  line-height: 1rem;
}
.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading]{
  font-weight: 500;
}
.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading]{
  color: hsl(var(--muted-foreground));
}
.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{
  padding-top: 0px;
}
.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg{
  height: 1.25rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg{
  width: 1.25rem;
}
.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input]{
  height: 3rem;
}
.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item]{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg{
  height: 1.25rem;
}
.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg{
  width: 1.25rem;
}
.\[\&_tr\:last-child\]\:border-0 tr:last-child{
  border-width: 0px;
}
.\[\&_tr\]\:border-b tr{
  border-bottom-width: 1px;
}
[data-side=left][data-collapsible=offcanvas] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2{
  right: -0.5rem;
}
[data-side=left][data-state=collapsed] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize{
  cursor: e-resize;
}
[data-side=left] .\[\[data-side\=left\]_\&\]\:cursor-w-resize{
  cursor: w-resize;
}
[data-side=right][data-collapsible=offcanvas] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2{
  left: -0.5rem;
}
[data-side=right][data-state=collapsed] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize{
  cursor: w-resize;
}
[data-side=right] .\[\[data-side\=right\]_\&\]\:cursor-e-resize{
  cursor: e-resize;
}

