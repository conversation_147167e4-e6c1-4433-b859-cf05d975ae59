"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+primitive@1.0.0";
exports.ids = ["vendor-chunks/@radix-ui+primitive@1.0.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.0.0/node_modules/@radix-ui/primitive/dist/index.module.js":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+primitive@1.0.0/node_modules/@radix-ui/primitive/dist/index.module.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ $e42e1063c40fb3ef$export$b9ecd428b558ff10)\n/* harmony export */ });\nfunction $e42e1063c40fb3ef$export$b9ecd428b558ff10(originalEventHandler, ourEventHandler, { checkForDefaultPrevented: checkForDefaultPrevented = true  } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler === null || originalEventHandler === void 0 || originalEventHandler(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) return ourEventHandler === null || ourEventHandler === void 0 ? void 0 : ourEventHandler(event);\n    };\n}\n\n\n\n\n\n//# sourceMappingURL=index.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByYWRpeC11aStwcmltaXRpdmVAMS4wLjAvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tb2R1bGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDRGQUE0Riw2REFBNkQsSUFBSTtBQUM3SjtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7OztBQUsyRTtBQUMzRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9AcmFkaXgtdWkrcHJpbWl0aXZlQDEuMC4wL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubW9kdWxlLmpzPzU5YjMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gJGU0MmUxMDYzYzQwZmIzZWYkZXhwb3J0JGI5ZWNkNDI4YjU1OGZmMTAob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQ6IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgIH0gPSB7fSkge1xuICAgIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgICAgICBvcmlnaW5hbEV2ZW50SGFuZGxlciA9PT0gbnVsbCB8fCBvcmlnaW5hbEV2ZW50SGFuZGxlciA9PT0gdm9pZCAwIHx8IG9yaWdpbmFsRXZlbnRIYW5kbGVyKGV2ZW50KTtcbiAgICAgICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHJldHVybiBvdXJFdmVudEhhbmRsZXIgPT09IG51bGwgfHwgb3VyRXZlbnRIYW5kbGVyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvdXJFdmVudEhhbmRsZXIoZXZlbnQpO1xuICAgIH07XG59XG5cblxuXG5cbmV4cG9ydCB7JGU0MmUxMDYzYzQwZmIzZWYkZXhwb3J0JGI5ZWNkNDI4YjU1OGZmMTAgYXMgY29tcG9zZUV2ZW50SGFuZGxlcnN9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubW9kdWxlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.0.0/node_modules/@radix-ui/primitive/dist/index.module.js\n");

/***/ })

};
;