"use client"

import React, { useState, useEffect } from "react"
import { useWorkspace } from "@ui/providers/workspace"
import { useScreenSize } from "@ui/providers/screenSize"
import { useRouter, useParams, usePathname } from "@ui/context/routerContext"
import { NavigationTabView, Tab } from "@ui/components/custom-ui/tabView"
import People from "./people"
import Companies from "./company"

interface LeadGenerationProps {
    workspaceId?: string;
    onLeadCreated?: (lead: any) => void;
}





const LeadGeneration = (props: LeadGenerationProps) => {
    const { workspace } = useWorkspace()
    const { isMobile } = useScreenSize()
    const router = useRouter()
    const params = useParams()
    const pathname = usePathname()
    

    const getActiveTabsFromUrl = () => {
        const pathSegments = pathname.split('/').filter(Boolean)
        const leadGenIndex = pathSegments.findIndex(segment => segment === 'lead-generation')
        
        if (leadGenIndex !== -1) {
            const primaryTab = pathSegments[leadGenIndex + 1]
            const secondaryTab = pathSegments[leadGenIndex + 2] // 'my-leads', 'find-leads', 'saved-search'
            
            return {
                primary: (primaryTab === 'companies' ? 'companies' : 'people') as 'people' | 'companies',
                secondary: (['my-leads', 'find-leads', 'saved-search'].includes(secondaryTab) ? secondaryTab : 'my-leads') as 'my-leads' | 'find-leads' | 'saved-search'
            }
        }
        
        return { primary: 'people' as const, secondary: 'my-leads' as const }
    }
    
    const { primary: activeTab, secondary: activeSubTab } = getActiveTabsFromUrl()
    

    const handlePrimaryTabClick = (tab: string) => {
        const domain = params.domain
        router.push(`/${domain}/lead-generation/${tab}/my-leads`)
    }
    
    const handleSecondaryTabClick = (subTab: string) => {
        const domain = params.domain
        router.push(`/${domain}/lead-generation/${activeTab}/${subTab}`)
    }


    const primaryTabs: Tab[] = [
        { id: 'people', title: 'People', content: null },
        { id: 'companies', title: 'Companies', content: null }
    ]

    const secondaryTabs: Tab[] = [
        { id: 'my-leads', title: 'My leads', content: null },
        { id: 'find-leads', title: 'Find leads', content: null },
        { id: 'saved-search', title: 'Saved search', content: null }
    ]


    const renderContent = () => {
        switch (activeTab) {
            case 'people':
                return <People activeSubTab={activeSubTab} onLeadCreated={props.onLeadCreated} />
            case 'companies':
                return <Companies activeSubTab={activeSubTab} onLeadCreated={props.onLeadCreated} />
            default:
                return <People activeSubTab={activeSubTab} onLeadCreated={props.onLeadCreated} />
        }
    }

    return (
        <div className="w-full h-full bg-white flex flex-col">
            {/* Primary Navigation */}
            <NavigationTabView
                tabs={primaryTabs}
                tab={activeTab}
                onTabChange={handlePrimaryTabClick}
                tabHeaderClassName={`${isMobile ? 'pl-8' : 'pl-0'}`}
            />
            
            {/* Secondary Navigation */}
            <NavigationTabView
                tabs={secondaryTabs}
                tab={activeSubTab}
                onTabChange={handleSecondaryTabClick}
                tabHeaderClassName={`py-2 border-b border-neutral-200 bg-white ${isMobile ? 'pl-4' : 'pl-0'}`}
            />
            
            {/* Content - Render the appropriate component */}
            <div className="flex-1 flex flex-col overflow-hidden">
                {renderContent()}
            </div>
        </div>
    )
}

export default LeadGeneration
