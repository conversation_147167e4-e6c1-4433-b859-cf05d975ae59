"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { ChevronDownIcon } from "@ui/components/icons/FontAwesomeRegular"
import { useLeadSubstitution } from "./LeadDataSubstitutionProvider"

interface LeadFieldSelectorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
}

export const LeadFieldSelector = ({ 
  value, 
  onChange, 
  placeholder = "Select lead field...",
  className = ""
}: LeadFieldSelectorProps) => {
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const { leadData, getAvailableFields } = useLeadSubstitution()


  const availableFields = getAvailableFields()
  const filteredFields = availableFields.filter(field => 
    field.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    field.key.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleFieldSelect = (fieldKey: string) => {
    onChange(fieldKey)
    setOpen(false)
    setSearchQuery("")
  }


  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
            {value ? availableFields.find(field => field.key === value)?.label || "Select field..." : "Select field..."}
            <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <div className="p-3 border-b">
            <Input
              placeholder="Search fields..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-8"
            />
          </div>
          <div className="max-h-60 overflow-y-auto">
            {filteredFields.length === 0 ? (
              <div className="p-3 text-sm text-muted-foreground text-center">
                No fields found
              </div>
            ) : (
              <div className="p-1">
                {filteredFields.map((field) => (
                  <button
                    key={field.key}
                    onClick={() => handleFieldSelect(field.key)}
                    className="w-full text-left p-2 hover:bg-muted rounded text-sm"
                  >
                    <div className="font-medium">{field.label}</div>
                    <div className="text-xs text-muted-foreground">
                      {String(field.value).substring(0, 50)}
                      {String(field.value).length > 50 ? "..." : ""}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
